import { Injectable } from '@nestjs/common';
import * as nodemailer from 'nodemailer';

@Injectable()
export class EmailService {
  private transporter: nodemailer.Transporter;

  constructor() {
    this.initializeTransporter();
  }

  private initializeTransporter() {
    const smtpConfig = {
      host: process.env.SMTP_HOST || 'smtp.gmail.com',
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS,
      },
      tls: {
        rejectUnauthorized: false,
      },
    };

    this.transporter = nodemailer.createTransport(smtpConfig);
  }

  /**
   * Envia e-mail de divergência para o fornecedor
   */
  async enviarEmailDivergencia(
    destinatario: string,
    numeroNota: string,
    conteudo: string,
    nomeFornecedor?: string,
  ): Promise<{ success: boolean; message: string }> {
    try {
      const mailOptions = {
        from: {
          name:
            process.env.SMTP_FROM_NAME ||
            'Departamento de Compras - Elco Engenharia',
          address: process.env.SMTP_USER || '<EMAIL>',
        },
        to: destinatario,
        subject: nomeFornecedor
          ? `Divergência Identificada - ${nomeFornecedor} - NF nº ${numeroNota}`
          : `Divergência Identificada - Sujeita à Recusa da NF nº ${numeroNota}`,
        text: conteudo,
        html: this.converterParaHtml(conteudo),
      };

      const info = await this.transporter.sendMail(mailOptions);

      console.log(
        `[EmailService] E-mail enviado com sucesso para ${destinatario}`,
      );
      console.log(`[EmailService] Message ID: ${info.messageId}`);

      return {
        success: true,
        message: `E-mail enviado com sucesso para ${destinatario}`,
      };
    } catch (error) {
      console.error('[EmailService] Erro ao enviar e-mail:', error);
      return {
        success: false,
        message: `Erro ao enviar e-mail: ${error.message}`,
      };
    }
  }

  /**
   * Converte o texto do e-mail para HTML para melhor formatação
   */
  private converterParaHtml(texto: string): string {
    return texto
      .replace(/\n\n/g, '</p><p>')
      .replace(/\n/g, '<br>')
      .replace(/•/g, '&bull;')
      .replace(/Prezado Fornecedor,/, '<strong>Prezado Fornecedor,</strong>')
      .replace(/Atenciosamente,/, '<strong>Atenciosamente,</strong>')
      .replace(
        /Departamento de Compras/,
        '<strong>Departamento de Compras</strong>',
      )
      .replace(/Elco Engenharia/, '<strong>Elco Engenharia</strong>')
      .replace(/Importante:/, '<strong>Importante:</strong>')
      .replace(
        /danfe@elco\.com\.br/g,
        '<a href="mailto:<EMAIL>"><EMAIL></a>',
      )
      .replace(/24 horas/g, '<strong>24 horas</strong>')
      .replace(/<p>/, '<p style="margin: 10px 0; line-height: 1.6;">')
      .replace(/<\/p>$/, '</p>')
      .replace(
        /^/,
        '<div style="font-family: Arial, sans-serif; font-size: 14px; line-height: 1.6; color: #333;">',
      )
      .replace(/$/, '</div>');
  }

  /**
   * Testa a conexão SMTP
   */
  async testarConexao(): Promise<{ success: boolean; message: string }> {
    try {
      await this.transporter.verify();
      return {
        success: true,
        message: 'Conexão SMTP configurada corretamente',
      };
    } catch (error) {
      console.error('[EmailService] Erro na conexão SMTP:', error);
      return {
        success: false,
        message: `Erro na conexão SMTP: ${error.message}`,
      };
    }
  }
}
