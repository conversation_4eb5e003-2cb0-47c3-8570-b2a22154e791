"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const user_service_1 = require("./user.service");
const jwt_auth_guard_1 = require("../auth/jwt-auth.guard");
const update_user_dto_1 = require("./dto/update-user.dto");
const create_user_dto_1 = require("./dto/create-user.dto");
let UserController = class UserController {
    userService;
    constructor(userService) {
        this.userService = userService;
    }
    async getProfile(req) {
        return {
            name: req.user.name,
            email: req.user.email,
            phone: req.user.phone,
            role: req.user.role?.name || 'N/A',
        };
    }
    async updateProfile(req, body) {
        const userId = req.user.id;
        return this.userService.updateProfile(userId, body);
    }
    async updateUser(id, body) {
        return this.userService.update(Number(id), body);
    }
    async getUser(id) {
        return this.userService.findOne(Number(id));
    }
    async findAll() {
        return this.userService.findAll();
    }
    async deleteUser(id) {
        return this.userService.softDelete(id);
    }
    async createUser(body) {
        return this.userService.create(body);
    }
};
exports.UserController = UserController;
__decorate([
    (0, common_1.Get)('me'),
    (0, swagger_1.ApiOperation)({
        summary: 'Obter perfil do usuário logado',
        description: 'Retorna as informações do perfil do usuário atual autenticado.',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Perfil do usuário',
        schema: {
            type: 'object',
            properties: {
                name: { type: 'string', example: 'João Silva' },
                email: { type: 'string', example: '<EMAIL>' },
                phone: { type: 'string', example: '(11) 99999-9999' },
                role: { type: 'string', example: 'admin' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Token inválido ou ausente' }),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "getProfile", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Put)('me'),
    (0, swagger_1.ApiOperation)({
        summary: 'Atualizar perfil do usuário logado',
        description: 'Permite ao usuário autenticado atualizar suas próprias informações.',
    }),
    (0, swagger_1.ApiBody)({
        type: update_user_dto_1.UpdateUserDto,
        description: 'Dados para atualização do perfil',
        examples: {
            update: {
                summary: 'Exemplo de atualização',
                value: {
                    name: 'João Silva Santos',
                    phone: '(11) 98888-8888',
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Perfil atualizado com sucesso',
        schema: {
            type: 'object',
            properties: {
                id: { type: 'number', example: 1 },
                name: { type: 'string', example: 'João Silva Santos' },
                email: { type: 'string', example: '<EMAIL>' },
                phone: { type: 'string', example: '(11) 98888-8888' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Token inválido ou ausente' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dados inválidos' }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, update_user_dto_1.UpdateUserDto]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "updateProfile", null);
__decorate([
    (0, common_1.Put)('edit/:id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Atualizar usuário por ID',
        description: 'Permite atualizar qualquer usuário por ID (requer permissões administrativas).',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID do usuário',
        type: 'string',
        example: '1',
    }),
    (0, swagger_1.ApiBody)({ type: update_user_dto_1.UpdateUserDto }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Usuário atualizado com sucesso',
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Usuário não encontrado' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_user_dto_1.UpdateUserDto]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "updateUser", null);
__decorate([
    (0, common_1.Get)('view/:id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Obter usuário por ID',
        description: 'Retorna os detalhes completos de um usuário específico.',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID do usuário',
        type: 'string',
        example: '1',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Dados do usuário',
        schema: {
            type: 'object',
            properties: {
                id: { type: 'number', example: 1 },
                name: { type: 'string', example: 'João Silva' },
                email: { type: 'string', example: '<EMAIL>' },
                phone: { type: 'string', example: '(11) 99999-9999' },
                createdAt: { type: 'string', format: 'date-time' },
                role: {
                    type: 'object',
                    properties: {
                        id: { type: 'number', example: 1 },
                        name: { type: 'string', example: 'admin' },
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Usuário não encontrado' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "getUser", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Listar todos os usuários',
        description: 'Retorna uma lista com todos os usuários cadastrados no sistema.',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de usuários',
        schema: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    id: { type: 'number', example: 1 },
                    name: { type: 'string', example: 'João Silva' },
                    email: { type: 'string', example: '<EMAIL>' },
                    phone: { type: 'string', example: '(11) 99999-9999' },
                    isActive: { type: 'boolean', example: true },
                    role: {
                        type: 'object',
                        properties: {
                            id: { type: 'number', example: 1 },
                            name: { type: 'string', example: 'admin' },
                        },
                    },
                },
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], UserController.prototype, "findAll", null);
__decorate([
    (0, common_1.Delete)('delete/:id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Deletar usuário (soft delete)',
        description: 'Desativa um usuário no sistema (soft delete). O usuário não é removido permanentemente.',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID do usuário',
        type: 'number',
        example: 1,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Usuário desativado com sucesso',
        schema: {
            type: 'object',
            properties: {
                message: { type: 'string', example: 'Usuário desativado com sucesso' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Usuário não encontrado' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "deleteUser", null);
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Criar novo usuário',
        description: 'Cria um novo usuário no sistema. Requer permissões administrativas.',
    }),
    (0, swagger_1.ApiBody)({
        type: create_user_dto_1.CreateUserDto,
        description: 'Dados do novo usuário',
        examples: {
            create: {
                summary: 'Exemplo de criação',
                value: {
                    name: 'Maria Santos',
                    email: '<EMAIL>',
                    password: 'senha123',
                    phone: '(11) 88888-8888',
                    roleId: 2,
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Usuário criado com sucesso',
        schema: {
            type: 'object',
            properties: {
                id: { type: 'number', example: 2 },
                name: { type: 'string', example: 'Maria Santos' },
                email: { type: 'string', example: '<EMAIL>' },
                phone: { type: 'string', example: '(11) 88888-8888' },
                role: {
                    type: 'object',
                    properties: {
                        id: { type: 'number', example: 2 },
                        name: { type: 'string', example: 'user' },
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Dados inválidos ou email já existe',
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_user_dto_1.CreateUserDto]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "createUser", null);
exports.UserController = UserController = __decorate([
    (0, swagger_1.ApiTags)('Usuários'),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, common_1.Controller)('users'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [user_service_1.UserService])
], UserController);
//# sourceMappingURL=user.controller.js.map