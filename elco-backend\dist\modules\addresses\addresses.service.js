"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddressesService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const address_entity_1 = require("./address.entity");
let AddressesService = class AddressesService {
    addressRepository;
    constructor(addressRepository) {
        this.addressRepository = addressRepository;
    }
    async create(createAddressDto) {
        const address = this.addressRepository.create(createAddressDto);
        return this.addressRepository.save(address);
    }
    async findAll() {
        return this.addressRepository.find();
    }
    async findOne(id) {
        const address = await this.addressRepository.findOneBy({ id });
        if (!address) {
            throw new Error(`Endereço com id ${id} não encontrado`);
        }
        return address;
    }
    async update(id, updateAddressDto) {
        await this.addressRepository.update(id, updateAddressDto);
        return this.findOne(id);
    }
    async remove(id) {
        await this.addressRepository.delete(id);
    }
    async findByProjectName(projectName) {
        console.log('🔍 Buscando endereços para projectName:', projectName);
        const cleanProjectName = projectName.trim().toLowerCase();
        let addresses = await this.addressRepository
            .createQueryBuilder('address')
            .where('LOWER(address.projectName) = LOWER(:projectName)', {
            projectName,
        })
            .getMany();
        console.log('📋 Resultado busca exata:', addresses.length, 'endereços encontrados');
        if (addresses.length === 0) {
            console.log('🔍 Tentando busca parcial para:', projectName);
            addresses = await this.addressRepository
                .createQueryBuilder('address')
                .where('LOWER(address.projectName) LIKE LOWER(:projectName)', {
                projectName: `%${projectName}%`,
            })
                .getMany();
            console.log('📋 Resultado busca parcial:', addresses.length, 'endereços encontrados');
        }
        if (addresses.length === 0) {
            const numericCode = projectName.match(/\d+/)?.[0];
            if (numericCode) {
                console.log('🔍 Tentando busca por código numérico:', numericCode);
                addresses = await this.addressRepository
                    .createQueryBuilder('address')
                    .where('LOWER(address.projectName) LIKE LOWER(:numericCode)', {
                    numericCode: `%${numericCode}%`,
                })
                    .getMany();
                console.log('📋 Resultado busca por código numérico:', addresses.length, 'endereços encontrados');
            }
        }
        if (addresses.length === 0) {
            console.log('🔍 Tentando busca por projectCode:', projectName);
            addresses = await this.addressRepository
                .createQueryBuilder('address')
                .where('LOWER(address.projectCode) LIKE LOWER(:projectCode)', {
                projectCode: `%${projectName}%`,
            })
                .getMany();
            console.log('📋 Resultado busca por projectCode:', addresses.length, 'endereços encontrados');
        }
        console.log('✅ Endereços encontrados:', addresses.map((a) => ({
            id: a.id,
            projectName: a.projectName,
            address: a.address,
        })));
        return addresses;
    }
};
exports.AddressesService = AddressesService;
exports.AddressesService = AddressesService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(address_entity_1.Address)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], AddressesService);
//# sourceMappingURL=addresses.service.js.map