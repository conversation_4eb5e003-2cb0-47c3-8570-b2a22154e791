import { InvoiceItem } from './invoice-item.entity';
export declare class Invoice {
    id: number;
    idOrdem: number;
    idRomaneio: number;
    filInCodigo: number;
    notInCodigo: number;
    notInNumero: string;
    tpdInCodigo: number;
    notDtEmissao: Date;
    notHrHoraemissao: string;
    notDtSaida: Date;
    notHrHorasaida: Date;
    notStUf: string;
    notStMunicipio: string;
    notStCgc: string;
    notStIncrestadual: string;
    cfopStDescricao: string;
    notStChaveacesso: string;
    ccfInReduzido: number;
    projeto: string;
    xml?: string;
    status?: string;
    pdfBase64?: string;
    numeroDanfe?: string;
    items: InvoiceItem[];
    createdAt: Date;
    updatedAt: Date;
    deletedAt: Date;
}
