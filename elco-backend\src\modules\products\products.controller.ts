import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBody,
  ApiParam,
} from '@nestjs/swagger';
import { ProductsService } from './products.service';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { CreateProductWeightDto } from './dto/create-product-weight.dto';
import { UpdateProductWeightDto } from './dto/update-product-weight.dto';
import { VerifyMultipleProductsDto } from './dto/verify-multiple-products.dto';

@ApiTags('Produtos')
@Controller('products')
export class ProductsController {
  constructor(private readonly productsService: ProductsService) {}

  @Post()
  @ApiOperation({
    summary: 'Criar novo produto',
    description: 'Cadastra um novo produto no sistema.',
  })
  @ApiBody({ type: CreateProductDto })
  @ApiResponse({
    status: 201,
    description: 'Produto criado com sucesso',
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  create(@Body() createProductDto: CreateProductDto) {
    return this.productsService.create(createProductDto);
  }

  @Get()
  @ApiOperation({
    summary: 'Listar todos os produtos',
    description: 'Retorna uma lista com todos os produtos cadastrados.',
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de produtos',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'number', example: 1 },
          code: { type: 'string', example: 'PROD001' },
          name: { type: 'string', example: 'Produto Exemplo' },
          description: { type: 'string', example: 'Descrição do produto' },
          weight: { type: 'number', example: 1.5 },
          isActive: { type: 'boolean', example: true },
        },
      },
    },
  })
  findAll() {
    return this.productsService.findAll();
  }

  @Get('/weights')
  @ApiOperation({
    summary: 'Listar pesos de produtos',
    description:
      'Retorna uma lista com todos os pesos de produtos configurados.',
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de pesos de produtos',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'string', example: 'uuid-string' },
          productCode: { type: 'string', example: 'PROD001' },
          productName: { type: 'string', example: 'Produto Exemplo' },
          weight: { type: 'number', example: 1.5 },
          unit: { type: 'string', example: 'kg' },
        },
      },
    },
  })
  async findAllProductWeights() {
    return this.productsService.findAllProductWeights();
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Obter produto por ID',
    description: 'Retorna os detalhes de um produto específico.',
  })
  @ApiParam({
    name: 'id',
    description: 'ID do produto',
    type: 'string',
    example: '1',
  })
  @ApiResponse({
    status: 200,
    description: 'Dados do produto',
  })
  @ApiResponse({ status: 404, description: 'Produto não encontrado' })
  findOne(@Param('id') id: string) {
    return this.productsService.findOne(+id);
  }

  @Delete(':id')
  @ApiOperation({
    summary: 'Deletar produto',
    description: 'Remove um produto do sistema.',
  })
  @ApiParam({
    name: 'id',
    description: 'ID do produto',
    type: 'string',
    example: '1',
  })
  @ApiResponse({
    status: 200,
    description: 'Produto removido com sucesso',
  })
  @ApiResponse({ status: 404, description: 'Produto não encontrado' })
  remove(@Param('id') id: string) {
    return this.productsService.remove(+id);
  }

  @Patch('/verify')
  @ApiOperation({
    summary: 'Verificar múltiplos produtos',
    description: 'Verifica múltiplos produtos em um romaneio com observações.',
  })
  @ApiBody({
    type: VerifyMultipleProductsDto,
    description: 'Dados para verificação de produtos',
    examples: {
      verify: {
        summary: 'Exemplo de verificação',
        value: {
          romaneioId: 1,
          products: [
            { id: 1, verified: true },
            { id: 2, verified: false },
          ],
          observations: 'Produto 2 com avaria',
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Produtos verificados com sucesso',
  })
  @ApiResponse({ status: 400, description: 'RomaneioId é obrigatório' })
  async verifyMultipleProducts(@Body() body: VerifyMultipleProductsDto) {
    if (!body.romaneioId) {
      throw new Error('RomaneioId é obrigatório');
    }
    return this.productsService.verifyMultipleProducts(
      body.romaneioId,
      body.products,
      body.observations,
    );
  }

  @Patch('/verify-many')
  @ApiOperation({
    summary: 'Verificar produtos por IDs',
    description: 'Verifica vários produtos de uma vez usando uma lista de IDs.',
  })
  @ApiBody({
    description: 'Lista de IDs dos produtos',
    schema: {
      type: 'object',
      properties: {
        ids: {
          type: 'array',
          items: { type: 'number' },
          example: [1, 2, 3, 4],
        },
      },
      required: ['ids'],
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Produtos verificados com sucesso',
  })
  verifyManyProducts(@Body() body: { ids: number[] }) {
    return this.productsService.verifyManyProducts(body.ids);
  }

  @Post('/product-packaging')
  @ApiOperation({
    summary: 'Sincronizar produto com embalagem',
    description: 'Vincula um produto a uma embalagem em um pedido específico.',
  })
  @ApiBody({
    description: 'Dados para vinculação produto-embalagem',
    schema: {
      type: 'object',
      properties: {
        orderId: { type: 'number', example: 1 },
        productId: { type: 'number', example: 1 },
        packagingId: { type: 'number', example: 1 },
      },
      required: ['orderId', 'productId', 'packagingId'],
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Vinculação realizada com sucesso',
  })
  syncProductPackaging(
    @Body() data: { orderId: number; productId: number; packagingId: number },
  ) {
    return this.productsService.syncProductPackaging(data);
  }

  @Patch(':id/weight')
  @ApiOperation({
    summary: 'Atualizar peso do produto',
    description: 'Atualiza o peso de um produto específico.',
  })
  @ApiParam({
    name: 'id',
    description: 'ID do produto',
    type: 'string',
    example: '1',
  })
  @ApiBody({
    description: 'Novo peso do produto',
    schema: {
      type: 'object',
      properties: {
        weight: { type: 'number', example: 2.5 },
      },
      required: ['weight'],
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Peso atualizado com sucesso',
  })
  updateWeight(@Param('id') id: string, @Body('weight') weight: number) {
    return this.productsService.updateWeight(+id, weight);
  }

  @Post('/weights')
  @ApiOperation({
    summary: 'Criar peso de produto',
    description:
      'Cria uma nova configuração de peso para um produto. Requer código OU nome do produto.',
  })
  @ApiBody({
    type: CreateProductWeightDto,
    description: 'Dados do peso do produto',
    examples: {
      byCode: {
        summary: 'Por código do produto',
        value: {
          productCode: 'PROD001',
          weight: 1.5,
          unit: 'kg',
        },
      },
      byName: {
        summary: 'Por nome do produto',
        value: {
          productName: 'Produto Exemplo',
          weight: 2.0,
          unit: 'kg',
        },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Peso de produto criado com sucesso',
  })
  @ApiResponse({
    status: 400,
    description: 'É obrigatório informar o código OU o nome do produto',
    schema: {
      type: 'object',
      properties: {
        error: {
          type: 'string',
          example: 'É obrigatório informar o código OU o nome do produto.',
        },
      },
    },
  })
  async createProductWeight(@Body() body: Partial<CreateProductWeightDto>) {
    if (!body.productCode && !body.productName) {
      return { error: 'É obrigatório informar o código OU o nome do produto.' };
    }
    return this.productsService.createProductWeight(body);
  }

  @Patch('/weights/:id')
  @ApiOperation({
    summary: 'Atualizar peso de produto',
    description: 'Atualiza uma configuração de peso de produto existente.',
  })
  @ApiParam({
    name: 'id',
    description: 'ID do peso do produto',
    type: 'string',
  })
  @ApiBody({ type: UpdateProductWeightDto })
  @ApiResponse({
    status: 200,
    description: 'Peso de produto atualizado com sucesso',
  })
  @ApiResponse({ status: 404, description: 'Peso de produto não encontrado' })
  async updateProductWeight(
    @Param('id') id: string,
    @Body() updateProductWeightDto: UpdateProductWeightDto,
  ) {
    return this.productsService.updateProductWeight(id, updateProductWeightDto);
  }

  @Delete('/weights/:id')
  @ApiOperation({
    summary: 'Deletar peso de produto',
    description: 'Remove uma configuração de peso de produto.',
  })
  @ApiParam({
    name: 'id',
    description: 'ID do peso do produto',
    type: 'string',
  })
  @ApiResponse({
    status: 200,
    description: 'Peso de produto removido com sucesso',
  })
  @ApiResponse({ status: 404, description: 'Peso de produto não encontrado' })
  async deleteProductWeight(@Param('id') id: string) {
    return this.productsService.deleteProductWeight(id);
  }
}
