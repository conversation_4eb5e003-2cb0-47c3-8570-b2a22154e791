// src/seed/seed.module.ts
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RolesEntity } from 'src/modules/role/entity/role.entity';
import { SeedService } from './seed.service';
import { UserModule } from 'src/modules/user/user.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([RolesEntity]),
    UserModule, // Importação do módulo de usuário para injeção do UserService
  ],
  providers: [SeedService],
})
export class SeedModule {}
