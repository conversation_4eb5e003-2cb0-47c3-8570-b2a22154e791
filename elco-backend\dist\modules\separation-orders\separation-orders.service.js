"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SeparationOrderService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const separation_order_entity_1 = require("./entity/separation-order.entity");
const axios_1 = require("axios");
let SeparationOrderService = class SeparationOrderService {
    repo;
    dataSource;
    constructor(repo, dataSource) {
        this.repo = repo;
        this.dataSource = dataSource;
    }
    async findProductByCode(productCode, depositorName, apiKey) {
        let page = 1;
        const pageSize = 10;
        const maxPages = 100;
        while (page <= maxPages) {
            try {
                const productRes = await axios_1.default.get('https://apigateway.smartgo.com.br/produto', {
                    params: {
                        CodigoInterno: productCode,
                        Page: page,
                        PageSize: pageSize,
                    },
                    headers: { Accept: 'application/json', api_key: apiKey },
                });
                const products = productRes.data?.model?.items ?? [];
                if (products.length === 0) {
                    break;
                }
                const product = products.find((prod) => prod.depositante === depositorName);
                if (product) {
                    return product;
                }
                const totalItems = productRes.data?.model?.totalItems || 0;
                if (page * pageSize >= totalItems) {
                    break;
                }
                page++;
            }
            catch (error) {
                console.error(`Erro ao buscar produto na página ${page}:`, error);
                break;
            }
        }
        return null;
    }
    async saveNew(orders) {
        const codes = orders.map((o) => o.internalCode);
        const existing = await this.repo.find({
            where: { internalCode: (0, typeorm_2.In)(codes) },
            select: ['internalCode'],
        });
        const existingCodes = new Set(existing.map((e) => e.internalCode));
        const newOnes = orders.filter((o) => !existingCodes.has(o.internalCode));
        return this.repo.save(newOnes);
    }
    async findAll(page = 1, limit = 10, filters = {}) {
        const queryBuilder = this.repo
            .createQueryBuilder('order')
            .where('order.status IN (:...statuses)', {
            statuses: ['PENDING', 'ROMANIO', 'LINKED'],
        });
        if (filters.search && filters.search.trim()) {
            const searchTerm = `%${filters.search.trim().toLowerCase()}%`;
            queryBuilder.andWhere('(LOWER(order.internalCode) LIKE :search OR LOWER(order.externalCode) LIKE :search OR LOWER(order.depositorName) LIKE :search)', { search: searchTerm });
        }
        if (filters.status && filters.status.trim()) {
            queryBuilder.andWhere('order.status = :status', {
                status: filters.status.trim(),
            });
        }
        if (filters.depositorName && filters.depositorName.trim()) {
            queryBuilder.andWhere('order.depositorName = :depositorName', {
                depositorName: filters.depositorName.trim(),
            });
        }
        if (filters.clientFilter && filters.clientFilter.trim()) {
            const clientCode = filters.clientFilter
                .trim()
                .split(' - ')[0]
                ?.trim()
                .toUpperCase();
            queryBuilder.andWhere('UPPER(order.externalCode) LIKE :clientPattern', {
                clientPattern: `${clientCode}%`,
            });
        }
        const total = await queryBuilder.getCount();
        const orders = await queryBuilder
            .orderBy('order.orderDate', 'DESC')
            .skip((page - 1) * limit)
            .take(limit)
            .getMany();
        const totalPages = Math.ceil(total / limit);
        return {
            orders,
            total,
            page: Number(page),
            limit: Number(limit),
            totalPages,
        };
    }
    async getStats() {
        const totalOrders = await this.repo.count({
            where: { status: (0, typeorm_2.In)(['PENDING', 'ROMANIO', 'LINKED']) },
        });
        const completedCount = await this.repo.count({
            where: { status: 'ROMANIO' },
        });
        const linkedCount = await this.repo.count({
            where: { status: 'LINKED' },
        });
        const pendingCount = await this.repo.count({
            where: { status: 'PENDING' },
        });
        const errorCount = await this.repo.count({
            where: { status: 'ERROR' },
        });
        return {
            totalOrders,
            completedCount,
            linkedCount,
            pendingCount,
            errorCount,
            completedPercentage: totalOrders > 0 ? Math.round((completedCount / totalOrders) * 100) : 0,
            linkedPercentage: totalOrders > 0 ? Math.round((linkedCount / totalOrders) * 100) : 0,
            pendingPercentage: totalOrders > 0 ? Math.round((pendingCount / totalOrders) * 100) : 0,
        };
    }
    async getFilterOptions() {
        const statusQuery = await this.repo
            .createQueryBuilder('order')
            .select('DISTINCT order.status', 'status')
            .where('order.status IN (:...statuses)', {
            statuses: ['PENDING', 'ROMANIO', 'LINKED'],
        })
            .getRawMany();
        const depositorQuery = await this.repo
            .createQueryBuilder('order')
            .select('DISTINCT order.depositorName', 'depositorName')
            .where('order.status IN (:...statuses)', {
            statuses: ['PENDING', 'ROMANIO', 'LINKED'],
        })
            .andWhere('order.depositorName IS NOT NULL')
            .getRawMany();
        const clientQuery = await this.repo
            .createQueryBuilder('order')
            .select('DISTINCT order.externalCode', 'externalCode')
            .where('order.status IN (:...statuses)', {
            statuses: ['PENDING', 'ROMANIO', 'LINKED'],
        })
            .andWhere('order.externalCode IS NOT NULL')
            .andWhere("order.externalCode != ''")
            .getRawMany();
        const clientsMap = new Map();
        clientQuery.forEach((item) => {
            const externalCode = item.externalCode?.trim();
            if (!externalCode)
                return;
            const codePart = externalCode.split(' - ')[0]?.trim().toUpperCase();
            if (!clientsMap.has(codePart) ||
                externalCode.length > (clientsMap.get(codePart)?.length || 0)) {
                clientsMap.set(codePart, externalCode);
            }
        });
        const uniqueClients = Array.from(clientsMap.values()).sort();
        return {
            statuses: statusQuery.map((item) => item.status),
            depositors: depositorQuery.map((item) => item.depositorName),
            clients: uniqueClients,
        };
    }
    async getProductDetails(internalCode) {
        const apiKey = process.env.WMS_API_KEY || '';
        const detailedProducts = [];
        const separationOrder = await this.repo.findOne({
            where: { internalCode: internalCode },
            select: ['depositorName'],
        });
        if (!separationOrder) {
            throw new common_1.NotFoundException('Pedido de separação não encontrado');
        }
        const depositorName = separationOrder.depositorName;
        const res = await axios_1.default.get('https://apigateway.smartgo.com.br/expedicao/extrato', {
            params: { CodigoInterno: internalCode },
            headers: {
                Accept: 'application/json',
                api_key: apiKey,
            },
        });
        const items = res.data?.model?.itens ?? [];
        for (const item of items) {
            const filteredProduct = await this.findProductByCode(item.codigoInternoProduto, depositorName, apiKey);
            if (filteredProduct) {
                detailedProducts.push({
                    code: filteredProduct.codigoInterno,
                    name: filteredProduct.nome,
                    category: filteredProduct.categoria,
                    withdrawalMethod: filteredProduct.metodoDeRetirada,
                    chargeType: filteredProduct.tipoCobranca,
                    quantity: item.quantidadeSolicitada,
                    protocoloDeposito: item.protocoloDeposito,
                    depositorName: depositorName,
                });
            }
        }
        const uniqueMap = new Map();
        for (const prod of detailedProducts) {
            const key = `${prod.code}-${prod.name}`;
            if (!uniqueMap.has(key)) {
                uniqueMap.set(key, prod);
            }
        }
        return Array.from(uniqueMap.values());
    }
    async linkOrder(internalCode, tipo) {
        const pedido = await this.repo.findOne({
            where: { internalCode: internalCode },
        });
        if (!pedido) {
            throw new common_1.NotFoundException('Pedido não encontrado');
        }
        pedido.typeOfBond = tipo;
        pedido.status = 'LINKED';
        return this.repo.save(pedido);
    }
    async linkOrderVolume(orderId, volume) {
        let updateFields = {};
        if (volume === null || volume === undefined) {
            updateFields = {
                volume: null,
                status: 'PENDING',
            };
        }
        else {
            updateFields = {
                volume: volume,
                status: 'LINKED',
            };
        }
        const result = await this.repo.update({ id: orderId }, updateFields);
        if (!result.affected || result.affected === 0) {
            throw new common_1.NotFoundException(`Pedido ${orderId} não encontrado.`);
        }
    }
};
exports.SeparationOrderService = SeparationOrderService;
exports.SeparationOrderService = SeparationOrderService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(separation_order_entity_1.SeparationOrder)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource])
], SeparationOrderService);
//# sourceMappingURL=separation-orders.service.js.map