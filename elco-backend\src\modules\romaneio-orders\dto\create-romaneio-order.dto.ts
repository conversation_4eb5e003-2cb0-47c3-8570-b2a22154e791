import { IsNumber, IsOptional, IsString } from 'class-validator';

export class CreateRomaneioOrderDto {
  @IsNumber()
  @IsOptional()
  readonly romaneioId?: number;

  @IsNumber()
  @IsOptional()
  readonly separationOrderId: number;

  @IsNumber()
  @IsOptional()
  readonly volume: number;

  @IsNumber()
  @IsOptional()
  readonly numberPackaging: number;

  @IsNumber()
  @IsOptional()
  readonly length: number;

  @IsNumber()
  @IsOptional()
  readonly width: number;

  @IsNumber()
  @IsOptional()
  readonly height: number;

  @IsNumber()
  @IsOptional()
  readonly netWeight: number;
}
