import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBody, ApiParam } from '@nestjs/swagger';
import { RomaneiosService } from './romaneios.service';
import { CreateRomaneioDto } from './dto/create-romaneio.dto';
import { UpdateRomaneioDto } from './dto/update-romaneio.dto';

@ApiTags('Romaneios')
@Controller('romaneios')
export class RomaneiosController {
  constructor(private readonly romaneiosService: RomaneiosService) { }

  @Post()
  @ApiOperation({
    summary: 'Criar novo romaneio',
    description: 'Cria um novo romaneio de carga no sistema.'
  })
  @ApiBody({
    type: CreateRomaneioDto,
    description: 'Dados do novo romaneio',
    examples: {
      create: {
        summary: 'Exemplo de romaneio',
        value: {
          externalCode: 'ROM001',
          driverId: 'uuid-driver',
          vehicleId: 1,
          transporterId: 'uuid-transporter',
          addressId: 1,
          scheduledDate: '2024-01-15T10:00:00Z',
          observations: 'Entrega prioritária'
        }
      }
    }
  })
  @ApiResponse({
    status: 201,
    description: 'Romaneio criado com sucesso',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'number', example: 1 },
        externalCode: { type: 'string', example: 'ROM001' },
        status: { type: 'string', example: 'PENDING' },
        scheduledDate: { type: 'string', format: 'date-time' },
        createdAt: { type: 'string', format: 'date-time' }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  async create(@Body() createRomaneioDto: CreateRomaneioDto) {
    return await this.romaneiosService.create(createRomaneioDto);
  }

  @Get()
  @ApiOperation({
    summary: 'Listar todos os romaneios',
    description: 'Retorna uma lista com todos os romaneios cadastrados.'
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de romaneios',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'number', example: 1 },
          externalCode: { type: 'string', example: 'ROM001' },
          status: { type: 'string', example: 'PENDING' },
          scheduledDate: { type: 'string', format: 'date-time' },
          driver: {
            type: 'object',
            properties: {
              id: { type: 'string', example: 'uuid-driver' },
              name: { type: 'string', example: 'João Silva' }
            }
          },
          vehicle: {
            type: 'object',
            properties: {
              id: { type: 'number', example: 1 },
              plate: { type: 'string', example: 'ABC-1234' }
            }
          },
          address: {
            type: 'object',
            properties: {
              id: { type: 'number', example: 1 },
              name: { type: 'string', example: 'Endereço Principal' }
            }
          }
        }
      }
    }
  })
  findAll() {
    return this.romaneiosService.findAll();
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Obter romaneio por ID',
    description: 'Retorna os detalhes completos de um romaneio específico.'
  })
  @ApiParam({ name: 'id', description: 'ID do romaneio', type: 'string', example: '1' })
  @ApiResponse({
    status: 200,
    description: 'Dados do romaneio',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'number', example: 1 },
        externalCode: { type: 'string', example: 'ROM001' },
        status: { type: 'string', example: 'PENDING' },
        scheduledDate: { type: 'string', format: 'date-time' },
        observations: { type: 'string', example: 'Entrega prioritária' },
        driver: { type: 'object' },
        vehicle: { type: 'object' },
        transporter: { type: 'object' },
        address: { type: 'object' },
        orders: { type: 'array', items: { type: 'object' } },
        createdAt: { type: 'string', format: 'date-time' }
      }
    }
  })
  @ApiResponse({ status: 404, description: 'Romaneio não encontrado' })
  findOne(@Param('id') id: string) {
    return this.romaneiosService.findOne(+id);
  }

  @Patch(':id')
  @ApiOperation({
    summary: 'Atualizar romaneio',
    description: 'Atualiza os dados de um romaneio existente.'
  })
  @ApiParam({ name: 'id', description: 'ID do romaneio', type: 'string', example: '1' })
  @ApiBody({ type: UpdateRomaneioDto })
  @ApiResponse({
    status: 200,
    description: 'Romaneio atualizado com sucesso'
  })
  @ApiResponse({ status: 404, description: 'Romaneio não encontrado' })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  update(
    @Param('id') id: string,
    @Body() updateRomaneioDto: UpdateRomaneioDto,
  ) {
    return this.romaneiosService.update(+id, updateRomaneioDto);
  }

  @Delete(':id')
  @ApiOperation({
    summary: 'Excluir romaneio completamente',
    description: 'Remove um romaneio do sistema, excluindo permanentemente todos os produtos relacionados, romaneio_orders e atualizando os separation_orders para status PENDING.'
  })
  @ApiParam({ name: 'id', description: 'ID do romaneio', type: 'string', example: '1' })
  @ApiResponse({
    status: 200,
    description: 'Romaneio excluído com sucesso',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'Romaneio excluído com sucesso' },
        deletedProducts: { type: 'boolean', example: true, description: 'Produtos excluídos' },
        deletedRomaneioOrders: { type: 'number', example: 3, description: 'Quantidade de romaneio_orders excluídos' },
        updatedSeparationOrders: { type: 'number', example: 2, description: 'Quantidade de separation_orders atualizados' }
      }
    }
  })
  @ApiResponse({ status: 404, description: 'Romaneio não encontrado' })
  @ApiResponse({ status: 400, description: 'Erro ao excluir romaneio' })
  remove(@Param('id') id: string) {
    return this.romaneiosService.deleteRomaneio(+id);
  }

  @Post('rascunho')
  @ApiOperation({
    summary: 'Salvar rascunho de romaneio',
    description: 'Salva um rascunho de romaneio para posterior finalização.'
  })
  @ApiBody({
    description: 'Dados do rascunho',
    schema: {
      type: 'object',
      properties: {
        externalCode: { type: 'string', example: 'DRAFT001' },
        data: { type: 'object', description: 'Dados do rascunho' }
      }
    }
  })
  @ApiResponse({
    status: 201,
    description: 'Rascunho salvo com sucesso',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string', example: 'uuid-draft' },
        externalCode: { type: 'string', example: 'DRAFT001' },
        data: { type: 'object' },
        createdAt: { type: 'string', format: 'date-time' }
      }
    }
  })
  async saveDraft(@Body() draftData: any) {
    return await this.romaneiosService.saveDraft(draftData);
  }

  @Get('rascunho/:externalCode')
  @ApiOperation({
    summary: 'Obter rascunho por código externo',
    description: 'Retorna um rascunho de romaneio pelo código externo.'
  })
  @ApiParam({
    name: 'externalCode',
    description: 'Código externo do rascunho',
    type: 'string',
    example: 'DRAFT001'
  })
  @ApiResponse({
    status: 200,
    description: 'Dados do rascunho',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'number', example: 1 },
        externalCode: { type: 'string', example: 'DRAFT001' },
        data: { type: 'object', description: 'Dados do rascunho' },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' }
      }
    }
  })
  @ApiResponse({ status: 404, description: 'Rascunho não encontrado' })
  async getDraftByExternalCode(@Param('externalCode') externalCode: string) {
    return await this.romaneiosService.getDraftByExternalCode(externalCode);
  }

  @Get(':id/peso')
  @ApiOperation({
    summary: 'Calcular peso do romaneio',
    description: 'Calcula o peso líquido e bruto do romaneio da mesma forma que é feito para a nota fiscal.'
  })
  @ApiParam({
    name: 'id',
    description: 'ID do romaneio',
    type: 'number',
    example: 1
  })
  @ApiResponse({
    status: 200,
    description: 'Peso calculado do romaneio',
    schema: {
      type: 'object',
      properties: {
        pesoLiquido: { type: 'number', example: 150.5, description: 'Peso líquido em kg' },
        pesoBruto: { type: 'number', example: 180.2, description: 'Peso bruto em kg' }
      }
    }
  })
  @ApiResponse({ status: 404, description: 'Romaneio não encontrado' })
  async calcularPesoRomaneio(@Param('id') id: string) {
    return await this.romaneiosService.calcularPesoRomaneio(+id);
  }
}
