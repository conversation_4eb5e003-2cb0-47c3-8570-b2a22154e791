import { Controller, Get, Query } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { InvoicesService } from './invoices.service';
import { InvoiceListResponseDto, InvoiceErrorListResponseDto } from './dto/invoice-list.dto';

@ApiTags('Notas Fiscais')
@Controller('invoices')
export class InvoicesController {
  constructor(private readonly invoicesService: InvoicesService) {}

  @Get()
  @ApiOperation({ summary: 'Listar todas as notas fiscais' })
  @ApiResponse({
    status: 200,
    description: 'Lista de notas fiscais com seus itens e erros',
    type: InvoiceListResponseDto
  })
  async findAll(
    @Query('page') page = 1,
    @Query('limit') limit = 10
  ): Promise<InvoiceListResponseDto> {
    return this.invoicesService.findAll(page, limit);
  }

  @Get('errors')
  @ApiOperation({ summary: 'Lista todos os erros de notas fiscais' })
  @ApiResponse({ status: 200, type: InvoiceErrorListResponseDto })
  async listInvoiceErrors(): Promise<InvoiceErrorListResponseDto> {
    return this.invoicesService.listInvoiceErrors();
  }
} 