import {
  IsArray,
  IsBoolean,
  IsInt,
  IsOptional,
  IsString,
} from 'class-validator';
import { Type } from 'class-transformer';

export class VerifyProductItemDto {
  @IsInt()
  id: number;

  @IsOptional()
  @IsString()
  userName?: string;

  @IsOptional()
  @IsString()
  verifiedAt?: string;

  @IsOptional()
  @IsBoolean()
  verified?: boolean;
}

export class VerifyMultipleProductsDto {
  @IsInt()
  romaneioId: number;

  @IsArray()
  @Type(() => VerifyProductItemDto)
  products: VerifyProductItemDto[];

  @IsOptional()
  @IsString()
  observations?: string;
}
