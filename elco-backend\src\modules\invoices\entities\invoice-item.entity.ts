import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedC<PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Invoice } from './invoice.entity';

@Entity('invoice_items')
export class InvoiceItem {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'invoice_id' })
  invoiceId: number;

  @ManyToOne(() => Invoice, (invoice) => invoice.items)
  @JoinColumn({ name: 'invoice_id' })
  invoice: Invoice;

  @Column({ name: 'not_in_codigo' })
  notInCodigo: number;

  @Column({ name: 'not_in_numero' })
  notInNumero: number;

  @Column({ name: 'itn_st_descricao' })
  itnStDescricao: string;

  @Column({
    name: 'itn_re_valorunitario',
    type: 'decimal',
    precision: 10,
    scale: 2,
  })
  itnReValorunitario: number;

  @Column({ name: 'itn_st_ncm_extenso' })
  itnStNcmExtenso: string;

  @Column({
    name: 'itn_re_valortotal',
    type: 'decimal',
    precision: 10,
    scale: 2,
  })
  itnReValortotal: number;

  @Column({ name: 'itn_re_quantidade' })
  itnReQuantidade: number;

  @Column({ name: 'stp_st_cstpis' })
  stpStCstpis: string;

  @Column({ name: 'stc_st_cstcofins' })
  stcStCstcofins: string;

  @Column({ name: 'id_romaneio' })
  idRomaneio: number;

  @Column({ name: 'id_ordem' })
  idOrdem: number;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @DeleteDateColumn({ name: 'deleted_at' })
  deletedAt: Date;
}
