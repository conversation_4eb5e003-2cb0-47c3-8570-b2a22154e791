// Script para testar os novos endpoints do cron job VNF
const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function testarEndpoints() {
    console.log('🧪 Testando endpoints do VNF Cron Job');
    console.log('==========================================\n');

    try {
        // 1. Testar status do processamento automático
        console.log('📊 1. Verificando status do processamento automático...');
        const statusResponse = await axios.get(`${BASE_URL}/vnf/status-processamento-automatico`);
        
        console.log('✅ Status obtido com sucesso:');
        console.log(`   🤖 Processando: ${statusResponse.data.isProcessing ? 'SIM' : 'NÃO'}`);
        console.log(`   📅 Última execução: ${statusResponse.data.lastExecution || 'Nunca'}`);
        console.log(`   ⏰ Próxima execução: ${new Date(statusResponse.data.nextExecution).toLocaleString('pt-BR')}`);
        console.log(`   🔄 Cron: ${statusResponse.data.cronExpression}`);
        console.log(`   📝 Descrição: ${statusResponse.data.description}\n`);

        // 2. Testar listagem de consultas processadas
        console.log('📋 2. Verificando consultas processadas...');
        const consultasResponse = await axios.get(`${BASE_URL}/vnf/consultas-processadas`);
        
        console.log(`✅ ${consultasResponse.data.length} consulta(s) encontrada(s)`);
        
        if (consultasResponse.data.length > 0) {
            console.log('   📄 Últimas 3 consultas:');
            consultasResponse.data.slice(0, 3).forEach((consulta, index) => {
                console.log(`   ${index + 1}. Chave: ${consulta.chaveAcesso.substring(0, 20)}...`);
                console.log(`      📝 Nota: ${consulta.numeroNota || 'N/A'}`);
                console.log(`      🏢 Fornecedor: ${consulta.nomeFornecedor || 'N/A'}`);
                console.log(`      📊 Status: ${consulta.statusConsulta}`);
                console.log(`      📅 Processado: ${new Date(consulta.dataProcessamento).toLocaleString('pt-BR')}`);
                console.log('');
            });
        }

        // 3. Testar processamento manual (opcional)
        console.log('🚀 3. Testando processamento manual para hoje...');
        const hoje = new Date();
        const dataHoje = `${hoje.getDate().toString().padStart(2, '0')}/${(hoje.getMonth() + 1).toString().padStart(2, '0')}/${hoje.getFullYear()}`;
        
        try {
            const processamentoResponse = await axios.post(`${BASE_URL}/vnf/processar-lote-por-data`, {
                data: dataHoje
            }, { timeout: 30000 });

            console.log('✅ Processamento manual executado:');
            console.log(`   📝 ${processamentoResponse.data.message}`);
            console.log(`   📊 Total: ${processamentoResponse.data.totalProcessados}`);
            console.log(`   ✅ Sucessos: ${processamentoResponse.data.sucessos}`);
            console.log(`   ❌ Erros: ${processamentoResponse.data.erros}`);
            
            if (processamentoResponse.data.chavesNovas !== undefined) {
                console.log(`   🆕 Chaves novas: ${processamentoResponse.data.chavesNovas}`);
                console.log(`   💾 Já processadas: ${processamentoResponse.data.chavesJaProcessadas}`);
            }
            
            if (processamentoResponse.data.limiteSefazAtingido) {
                console.log(`   ⚠️ Limite SEFAZ: ATINGIDO`);
            }

        } catch (processError) {
            if (processError.code === 'ECONNABORTED') {
                console.log('⏰ Processamento manual em andamento (timeout 30s atingido)');
            } else {
                console.log(`❌ Erro no processamento manual: ${processError.response?.data?.message || processError.message}`);
            }
        }

        console.log('\n🎉 Testes concluídos! O cron job está configurado e funcionando.');
        console.log(`⏰ Próxima execução automática: ${new Date(statusResponse.data.nextExecution).toLocaleString('pt-BR')}`);

    } catch (error) {
        console.error('❌ Erro nos testes:', error.response?.data || error.message);
        
        if (error.code === 'ECONNREFUSED') {
            console.log('\n💡 Dica: Certifique-se de que o backend está rodando em http://localhost:3000');
            console.log('   Execute: npm run start:dev');
        }
    }
}

// Executar testes
testarEndpoints(); 