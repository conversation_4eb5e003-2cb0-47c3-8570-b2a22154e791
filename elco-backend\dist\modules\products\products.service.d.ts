import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { Product } from './entities/product.entity';
import { Repository } from 'typeorm';
import { ProductWeights } from './entities/product_weights.entity';
import { CreateProductWeightDto } from './dto/create-product-weight.dto';
import { UpdateProductWeightDto } from './dto/update-product-weight.dto';
import { Romaneio } from '../romaneios/entities/romaneio.entity';
import { VerifyProductItemDto } from './dto/verify-multiple-products.dto';
export declare class ProductsService {
    private readonly productRepository;
    private readonly productWeightsRepository;
    private readonly romaneioRepository;
    private readonly productWeightRepository;
    constructor(productRepository: Repository<Product>, productWeightsRepository: Repository<ProductWeights>, romaneioRepository: Repository<Romaneio>, productWeightRepository: Repository<ProductWeights>);
    create(createProductDto: CreateProductDto): Promise<{
        id: number;
        idEnterpriseExternal: number;
        idDepositorExternal: number;
        depositor: string;
        idExternal: number;
        name: string;
        fullName: string;
        description: string;
    }>;
    findAll(): string;
    findOne(id: number): string;
    update(id: number, updateProductDto: UpdateProductDto): string;
    remove(id: number): string;
    verifyMultipleProducts(romaneioId: number, products: VerifyProductItemDto[], observations?: string): Promise<Product[]>;
    verifyManyProducts(ids: number[]): Promise<{
        updated: number;
    }>;
    syncProductPackaging(data: any): Promise<{
        updated: number;
    }>;
    updateWeight(id: number, weight: number): Promise<{
        updated: number;
    }>;
    createProductWeight(body: Partial<CreateProductWeightDto>): Promise<ProductWeights>;
    findAllProductWeights(): Promise<ProductWeights[]>;
    updateProductWeight(id: string, updateProductWeightDto: UpdateProductWeightDto): Promise<ProductWeights>;
    deleteProductWeight(id: string): Promise<{
        deleted: boolean;
    }>;
}
