import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Body,
  ParseIntPipe,
  NotFoundException,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBody, ApiParam, ApiConsumes } from '@nestjs/swagger';
import { VehicleService } from './vehicle.service';
import { CreateVehicleDto } from './dto/create-vehicle.dto';
import { UpdateVehicleDto } from './dto/update-vehicle.dto';
import { FileInterceptor } from '@nestjs/platform-express';

@ApiTags('Veículos')
@Controller('vehicles')
export class VehicleController {
  constructor(private readonly vehicleService: VehicleService) { }

  @Get('/list')
  @ApiOperation({
    summary: 'Listar todos os veículos',
    description: 'Retorna uma lista com todos os veículos cadastrados na frota.'
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de veículos',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'number', example: 1 },
          plate: { type: 'string', example: 'ABC-1234' },
          model: { type: 'string', example: 'Volvo FH' },
          brand: { type: 'string', example: 'Volvo' },
          year: { type: 'number', example: 2020 },
          capacity: { type: 'number', example: 25000 },
          isActive: { type: 'boolean', example: true }
        }
      }
    }
  })
  findAll() {
    return this.vehicleService.findAll();
  }

  @Get('/view/:id')
  @ApiOperation({
    summary: 'Obter veículo por ID',
    description: 'Retorna os detalhes completos de um veículo específico.'
  })
  @ApiParam({ name: 'id', description: 'ID do veículo', type: 'number', example: 1 })
  @ApiResponse({
    status: 200,
    description: 'Dados do veículo',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'number', example: 1 },
        plate: { type: 'string', example: 'ABC-1234' },
        model: { type: 'string', example: 'Volvo FH' },
        brand: { type: 'string', example: 'Volvo' },
        year: { type: 'number', example: 2020 },
        capacity: { type: 'number', example: 25000 },
        vehicleDocument: { type: 'string', example: 'path/to/document.pdf' },
        isActive: { type: 'boolean', example: true },
        createdAt: { type: 'string', format: 'date-time' }
      }
    }
  })
  @ApiResponse({ status: 404, description: 'Veículo não encontrado' })
  async findOne(@Param('id', ParseIntPipe) id: number) {
    return this.vehicleService.findOne(id);
  }

  @Post('/create')
  @ApiOperation({
    summary: 'Criar novo veículo',
    description: 'Adiciona um novo veículo à frota. Pode incluir upload de documentos.'
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Dados do veículo e documentos (opcional)',
    schema: {
      type: 'object',
      properties: {
        plate: { type: 'string', example: 'ABC-1234' },
        model: { type: 'string', example: 'Volvo FH' },
        brand: { type: 'string', example: 'Volvo' },
        year: { type: 'number', example: 2020 },
        capacity: { type: 'number', example: 25000 },
        vehicleDocument: {
          type: 'string',
          format: 'binary',
          description: 'Documento do veículo (PDF, JPG, PNG)'
        }
      },
      required: ['plate', 'model', 'brand', 'year']
    }
  })
  @ApiResponse({
    status: 201,
    description: 'Veículo criado com sucesso',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'number', example: 1 },
        plate: { type: 'string', example: 'ABC-1234' },
        model: { type: 'string', example: 'Volvo FH' },
        brand: { type: 'string', example: 'Volvo' },
        year: { type: 'number', example: 2020 },
        capacity: { type: 'number', example: 25000 }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @UseInterceptors(FileInterceptor('vehicleDocument'))
  create(
    @Body() dto: CreateVehicleDto,
    @UploadedFile() file?: Express.Multer.File,
  ) {
    return this.vehicleService.create(dto, file);
  }

  @Put('/update/:id')
  @ApiOperation({
    summary: 'Atualizar veículo',
    description: 'Atualiza os dados de um veículo existente.'
  })
  @ApiConsumes('multipart/form-data')
  @ApiParam({ name: 'id', description: 'ID do veículo', type: 'number', example: 1 })
  @ApiBody({
    description: 'Dados do veículo e documentos (opcional)',
    schema: {
      type: 'object',
      properties: {
        plate: { type: 'string', example: 'ABC-1234' },
        model: { type: 'string', example: 'Volvo FH' },
        brand: { type: 'string', example: 'Volvo' },
        vehicleDocument: {
          type: 'string',
          format: 'binary',
          description: 'Documento do veículo (PDF, JPG, PNG)'
        },
        keepExistingDocument: { type: 'string', example: 'true' }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: 'Veículo atualizado com sucesso'
  })
  @ApiResponse({ status: 404, description: 'Veículo não encontrado' })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @UseInterceptors(FileInterceptor('vehicleDocument'))
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() dto: UpdateVehicleDto,
    @UploadedFile() file?: Express.Multer.File,
  ) {
    return this.vehicleService.update(id, dto, file);
  }

  @Delete('/delete/:id')
  @ApiOperation({
    summary: 'Deletar veículo',
    description: 'Remove um veículo da frota.'
  })
  @ApiParam({ name: 'id', description: 'ID do veículo', type: 'number', example: 1 })
  @ApiResponse({
    status: 200,
    description: 'Veículo excluído com sucesso',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'Veículo excluído com sucesso' }
      }
    }
  })
  @ApiResponse({ status: 404, description: 'Veículo não encontrado' })
  async delete(@Param('id', ParseIntPipe) id: number) {
    const deleted = await this.vehicleService.delete(id);
    if (!deleted) throw new NotFoundException('Veículo não encontrado');
    return { message: 'Veículo excluído com sucesso' };
  }
}
