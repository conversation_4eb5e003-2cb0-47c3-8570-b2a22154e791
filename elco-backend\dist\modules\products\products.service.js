"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const product_entity_1 = require("./entities/product.entity");
const typeorm_2 = require("typeorm");
const product_weights_entity_1 = require("./entities/product_weights.entity");
const romaneio_entity_1 = require("../romaneios/entities/romaneio.entity");
let ProductsService = class ProductsService {
    productRepository;
    productWeightsRepository;
    romaneioRepository;
    productWeightRepository;
    constructor(productRepository, productWeightsRepository, romaneioRepository, productWeightRepository) {
        this.productRepository = productRepository;
        this.productWeightsRepository = productWeightsRepository;
        this.romaneioRepository = romaneioRepository;
        this.productWeightRepository = productWeightRepository;
    }
    async create(createProductDto) {
        const newData = new product_entity_1.Product();
        newData.idEnterpriseExternal = createProductDto.idEnterpriseExternal;
        newData.idDepositorExternal = createProductDto.idDepositorExternal;
        newData.depositor = createProductDto.depositor;
        newData.idExternal = createProductDto.idExternal;
        newData.name = createProductDto.name;
        newData.fullName = createProductDto.fullName;
        newData.description = createProductDto.description;
        const response = await this.productRepository.save(newData);
        return {
            id: response.id,
            idEnterpriseExternal: response.idEnterpriseExternal,
            idDepositorExternal: response.idDepositorExternal,
            depositor: response.depositor,
            idExternal: response.idExternal,
            name: response.name,
            fullName: response.fullName,
            description: response.description,
        };
    }
    findAll() {
        return `This action returns all products`;
    }
    findOne(id) {
        return `This action returns a #${id} product`;
    }
    update(id, updateProductDto) {
        return `This action updates a #${id} product`;
    }
    remove(id) {
        return `This action removes a #${id} product`;
    }
    async verifyMultipleProducts(romaneioId, products, observations) {
        let updatedCount = 0;
        const productIds = products.map((p) => p.id);
        const foundProducts = await this.productRepository.find({
            where: { id: (0, typeorm_2.In)(productIds) },
        });
        const now = new Date();
        for (const product of foundProducts) {
            const updateData = products.find((p) => p.id === product.id);
            if (!updateData)
                continue;
            const verifiedAtDate = updateData.verifiedAt
                ? new Date(updateData.verifiedAt)
                : now;
            const year = verifiedAtDate.getFullYear();
            const month = (verifiedAtDate.getMonth() + 1).toString().padStart(2, '0');
            const day = verifiedAtDate.getDate().toString().padStart(2, '0');
            const hours = verifiedAtDate.getHours().toString().padStart(2, '0');
            const minutes = verifiedAtDate.getMinutes().toString().padStart(2, '0');
            const seconds = verifiedAtDate.getSeconds().toString().padStart(2, '0');
            const verifiedDate = `${year}-${month}-${day}`;
            const verifiedTime = `${hours}:${minutes}:${seconds}`;
            product.verified =
                updateData.verified !== undefined ? updateData.verified : true;
            product.verifiedDate = verifiedDate;
            product.verifiedTime = verifiedTime;
            product.verifiedBy = updateData.userName || 'Sistema';
            product.updatedAt = now;
            updatedCount++;
        }
        if (updatedCount > 0) {
            await this.productRepository.save(foundProducts);
        }
        const romaneio = await this.romaneioRepository.findOneBy({
            id: romaneioId,
        });
        if (romaneio) {
            romaneio.statusId = 5;
            romaneio.verifiedObservations = observations || '';
            await this.romaneioRepository.save(romaneio);
        }
        return foundProducts;
    }
    async verifyManyProducts(ids) {
        const products = await this.productRepository.find({
            where: { id: (0, typeorm_2.In)(ids) },
        });
        let updatedCount = 0;
        for (const product of products) {
            product.verified = !product.verified;
            product.updatedAt = new Date();
            updatedCount++;
        }
        if (updatedCount > 0) {
            await this.productRepository.save(products);
        }
        return { updated: updatedCount };
    }
    async syncProductPackaging(data) {
        const { orderId, productId, packagingId } = data;
        const result = await this.productRepository.update({ id: productId, idSeparationOrder: orderId }, { idPackaging: packagingId });
        if (!result.affected || result.affected === 0) {
            throw new common_1.NotFoundException(`Produto ${productId} no pedido ${orderId} não encontrado.`);
        }
        return { updated: result.affected };
    }
    async updateWeight(id, weight) {
        const product = await this.productRepository.findOneBy({ id });
        if (!product) {
            throw new common_1.NotFoundException(`Produto ${id} não encontrado.`);
        }
        product.weight = weight;
        product.updatedAt = new Date();
        await this.productRepository.save(product);
        return { updated: 1 };
    }
    async createProductWeight(body) {
        if (!body.productCode && !body.productName) {
            throw new Error('É obrigatório informar o código OU o nome do produto.');
        }
        const newWeight = this.productWeightsRepository.create({
            productCode: body.productCode ?? undefined,
            productName: body.productName ?? undefined,
            weight: body.weight,
            unit: body.unit,
        });
        const saved = await this.productWeightsRepository.save(newWeight);
        return saved;
    }
    async findAllProductWeights() {
        return this.productWeightsRepository.find();
    }
    async updateProductWeight(id, updateProductWeightDto) {
        const weight = await this.productWeightsRepository.findOneBy({ id });
        if (!weight) {
            throw new common_1.NotFoundException(`Peso de produto com ID ${id} não encontrado.`);
        }
        Object.assign(weight, updateProductWeightDto);
        return this.productWeightsRepository.save(weight);
    }
    async deleteProductWeight(id) {
        const result = await this.productWeightsRepository.delete(id);
        if (result.affected === 0) {
            throw new common_1.NotFoundException(`Peso de produto com ID ${id} não encontrado.`);
        }
        return { deleted: true };
    }
};
exports.ProductsService = ProductsService;
exports.ProductsService = ProductsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(product_entity_1.Product)),
    __param(1, (0, typeorm_1.InjectRepository)(product_weights_entity_1.ProductWeights)),
    __param(2, (0, typeorm_1.InjectRepository)(romaneio_entity_1.Romaneio)),
    __param(3, (0, typeorm_1.InjectRepository)(product_weights_entity_1.ProductWeights)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], ProductsService);
//# sourceMappingURL=products.service.js.map