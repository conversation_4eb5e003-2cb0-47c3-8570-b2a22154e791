
import React from 'react';
import { cn } from '@/lib/utils';

const EngineeringLoader: React.FC = () => {
  return (
    <div className="flex flex-col items-center justify-center">
      <div className="relative">
        {/* Gear animation */}
        <div className="animate-spin">
          <svg
            width="60"
            height="60"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="text-elco-600"
          >
            <path
              d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M19.4 15C19.1277 15.6173 19.2583 16.3255 19.73 16.83L19.79 16.89C20.1428 17.2428 20.3368 17.7195 20.3368 18.2175C20.3368 18.7155 20.1428 19.1922 19.79 19.545C19.4372 19.8978 18.9605 20.0918 18.4625 20.0918C17.9645 20.0918 17.4878 19.8978 17.135 19.545L17.075 19.485C16.5705 19.0133 15.8623 18.8827 15.245 19.155C14.6428 19.4101 14.2479 20.0076 14.25 20.655V20.85C14.25 21.8717 13.4217 22.7 12.4 22.7C11.3783 22.7 10.55 21.8717 10.55 20.85V20.76C10.5421 20.0991 10.1323 19.5051 9.52 19.26C8.90275 18.9877 8.19449 19.1183 7.69 19.59L7.63 19.65C7.27722 20.0028 6.80052 20.1968 6.3025 20.1968C5.80448 20.1968 5.32778 20.0028 4.975 19.65C4.62222 19.2972 4.42824 18.8205 4.42824 18.3225C4.42824 17.8245 4.62222 17.3478 4.975 16.995L5.035 16.935C5.50675 16.4305 5.63731 15.7223 5.365 15.105C5.10988 14.5028 4.51237 14.1079 3.865 14.11H3.67C2.64831 14.11 1.82 13.2817 1.82 12.26C1.82 11.2383 2.64831 10.41 3.67 10.41H3.76C4.42094 10.4021 5.01489 9.99227 5.26 9.38C5.53231 8.76275 5.40175 8.05449 4.93 7.55L4.87 7.49C4.51722 7.13722 4.32324 6.66052 4.32324 6.1625C4.32324 5.66448 4.51722 5.18778 4.87 4.835C5.22278 4.48222 5.69948 4.28824 6.1975 4.28824C6.69552 4.28824 7.17222 4.48222 7.525 4.835L7.585 4.895C8.08951 5.36675 8.79777 5.49731 9.415 5.225H9.5C10.1022 4.96988 10.4971 4.37237 10.495 3.725V3.63C10.495 2.60831 11.3233 1.78 12.345 1.78C13.3667 1.78 14.195 2.60831 14.195 3.63V3.73C14.1929 4.37737 14.5878 4.97488 15.19 5.23C15.8072 5.50231 16.5155 5.37175 17.02 4.9L17.08 4.84C17.4328 4.48722 17.9095 4.29324 18.4075 4.29324C18.9055 4.29324 19.3822 4.48722 19.735 4.84C20.0878 5.19278 20.2818 5.66948 20.2818 6.1675C20.2818 6.66552 20.0878 7.14222 19.735 7.495L19.675 7.555C19.2033 8.05951 19.0727 8.76777 19.345 9.385V9.47C19.6001 10.0722 20.1976 10.4671 20.845 10.465H20.95C21.9717 10.465 22.8 11.2933 22.8 12.315C22.8 13.3367 21.9717 14.165 20.95 14.165H20.86C20.2126 14.1629 19.6151 14.5578 19.36 15.16L19.4 15Z"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </div>

        {/* Second gear */}
        <div className="absolute top-8 -right-5 animate-spin-slow">
          <svg
            width="40"
            height="40"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="text-elco-500"
          >
            <path
              d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M19.4 15C19.1277 15.6173 19.2583 16.3255 19.73 16.83L19.79 16.89C20.1428 17.2428 20.3368 17.7195 20.3368 18.2175C20.3368 18.7155 20.1428 19.1922 19.79 19.545C19.4372 19.8978 18.9605 20.0918 18.4625 20.0918C17.9645 20.0918 17.4878 19.8978 17.135 19.545L17.075 19.485C16.5705 19.0133 15.8623 18.8827 15.245 19.155C14.6428 19.4101 14.2479 20.0076 14.25 20.655V20.85C14.25 21.8717 13.4217 22.7 12.4 22.7C11.3783 22.7 10.55 21.8717 10.55 20.85V20.76C10.5421 20.0991 10.1323 19.5051 9.52 19.26C8.90275 18.9877 8.19449 19.1183 7.69 19.59L7.63 19.65C7.27722 20.0028 6.80052 20.1968 6.3025 20.1968C5.80448 20.1968 5.32778 20.0028 4.975 19.65C4.62222 19.2972 4.42824 18.8205 4.42824 18.3225C4.42824 17.8245 4.62222 17.3478 4.975 16.995L5.035 16.935C5.50675 16.4305 5.63731 15.7223 5.365 15.105C5.10988 14.5028 4.51237 14.1079 3.865 14.11H3.67C2.64831 14.11 1.82 13.2817 1.82 12.26C1.82 11.2383 2.64831 10.41 3.67 10.41H3.76C4.42094 10.4021 5.01489 9.99227 5.26 9.38C5.53231 8.76275 5.40175 8.05449 4.93 7.55L4.87 7.49C4.51722 7.13722 4.32324 6.66052 4.32324 6.1625C4.32324 5.66448 4.51722 5.18778 4.87 4.835C5.22278 4.48222 5.69948 4.28824 6.1975 4.28824C6.69552 4.28824 7.17222 4.48222 7.525 4.835L7.585 4.895C8.08951 5.36675 8.79777 5.49731 9.415 5.225H9.5C10.1022 4.96988 10.4971 4.37237 10.495 3.725V3.63C10.495 2.60831 11.3233 1.78 12.345 1.78C13.3667 1.78 14.195 2.60831 14.195 3.63V3.73C14.1929 4.37737 14.5878 4.97488 15.19 5.23C15.8072 5.50231 16.5155 5.37175 17.02 4.9L17.08 4.84C17.4328 4.48722 17.9095 4.29324 18.4075 4.29324C18.9055 4.29324 19.3822 4.48722 19.735 4.84C20.0878 5.19278 20.2818 5.66948 20.2818 6.1675C20.2818 6.66552 20.0878 7.14222 19.735 7.495L19.675 7.555C19.2033 8.05951 19.0727 8.76777 19.345 9.385V9.47C19.6001 10.0722 20.1976 10.4671 20.845 10.465H20.95C21.9717 10.465 22.8 11.2933 22.8 12.315C22.8 13.3367 21.9717 14.165 20.95 14.165H20.86C20.2126 14.1629 19.6151 14.5578 19.36 15.16L19.4 15Z"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </div>
      </div>
      <div className="mt-4 flex flex-col items-center">
        <span className="text-elco-800 font-bold text-lg">Carregando</span>
        <div className="mt-2 w-48 bg-gray-200 rounded-full h-2 overflow-hidden">
          <div className="bg-elco-500 h-2 rounded-full animate-pulse-width"></div>
        </div>
      </div>
    </div>
  );
};

export default EngineeringLoader;
