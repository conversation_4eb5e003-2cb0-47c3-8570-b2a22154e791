import { Test, TestingModule } from '@nestjs/testing';
import { MegaController } from '../mega.controller';
import { MegaService } from '../mega.service';

describe('MegaController', () => {
  let controller: MegaController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [MegaController],
      providers: [MegaService],
    }).compile();

    controller = module.get<MegaController>(MegaController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
