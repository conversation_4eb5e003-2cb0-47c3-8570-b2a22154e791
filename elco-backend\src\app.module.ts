import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserModule } from './modules/user/user.module';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { envSchema } from './config/env.validation';
import { AuthModule } from './modules/auth/auth.module';
import { SeedModule } from './seed/seed.module';
import { ScheduleModule } from '@nestjs/schedule';
import { SeparationOrderController } from './modules/separation-orders/separation-orders.controller';
import { SeparationOrderModule } from './modules/separation-orders/separation-orders.module';
import { SyncModule } from './modules/sync/sync.module';
import { RoleModule } from './modules/role/role.module';
import { RomaneiosModule } from './modules/romaneios/romaneios.module';
import { RomaneioOrdersModule } from './modules/romaneio-orders/romaneio-orders.module';
import { PackagingModule } from './modules/packaging/packaging.module';
import { DriverModule } from './modules/driver/driver.module';
import { TransporterModule } from './modules/transporter/transporter.module';
import { VehicleModule } from './modules/vehicle/vehicle.module';
import { ProductsModule } from './modules/products/products.module';
import { VnfModule } from './modules/vnf/vnf.module';
import { SupplierEmailModule } from './modules/supplier-email/supplier-email.module';
import { AddressesModule } from './modules/addresses/addresses.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      validationSchema: envSchema,
    }),
    ScheduleModule.forRoot(),
    TypeOrmModule.forRoot({
      type: 'mysql',
      host: process.env.DB_HOST,
      port: Number(process.env.DB_PORT),
      username: process.env.DB_USER,
      password: process.env.DB_PASS,
      database: process.env.DB_NAME,
      entities: [__dirname + '/**/*.entity{.ts,.js}'],
      synchronize: true,
      logging: false,
      extra: {
        connectionLimit: 20,
        acquireTimeout: 30000,
        timeout: 30000,
        reconnect: true,
        idleTimeout: 300000,
        charset: 'utf8mb4',
        sql_mode: 'TRADITIONAL',
        innodb_lock_wait_timeout: 10,
        transaction_isolation: 'READ-COMMITTED'
      },
      maxQueryExecutionTime: 30000,
      // Configuração para soft delete
      subscribers: [],
      migrations: [],
    }),
    UserModule,
    AuthModule,
    SeedModule,
    SeparationOrderModule,
    SyncModule,
    RoleModule,
    RomaneiosModule,
    RomaneioOrdersModule,
    PackagingModule,
    DriverModule,
    TransporterModule,
    VehicleModule,
    ProductsModule,
    VnfModule,
    SupplierEmailModule,
    AddressesModule,
  ],
  controllers: [AppController, SeparationOrderController],
  providers: [AppService],
})
export class AppModule {}
