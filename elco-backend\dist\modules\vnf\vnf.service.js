"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var VnfService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.VnfService = void 0;
const common_1 = require("@nestjs/common");
const xml2js_1 = require("xml2js");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const schedule_1 = require("@nestjs/schedule");
const axios_1 = require("axios");
const https = require("https");
const fs = require("fs");
const path = require("path");
const zlib = require("zlib");
const util_1 = require("util");
const invoice_entity_1 = require("../invoices/entities/invoice.entity");
const invoice_item_entity_1 = require("../invoices/entities/invoice-item.entity");
const product_entity_1 = require("../products/entities/product.entity");
const vnf_error_entity_1 = require("./vnf-error.entity");
const vnf_consulta_processada_entity_1 = require("./entities/vnf-consulta-processada.entity");
const separation_order_entity_1 = require("../separation-orders/entity/separation-order.entity");
const romaneio_entity_1 = require("../romaneios/entities/romaneio.entity");
const romaneio_order_entity_1 = require("../romaneio-orders/entities/romaneio-order.entity");
const supplier_email_entity_1 = require("../supplier-email/entity/supplier-email.entity");
const oracle_service_1 = require("../oracle/oracle.service");
const email_service_1 = require("./email.service");
let VnfService = VnfService_1 = class VnfService {
    invoiceRepository;
    invoiceItemRepository;
    productRepository;
    vnfErrorRepository;
    vnfConsultaProcessadaRepository;
    separationOrderRepository;
    romaneioRepository;
    romaneioOrderRepository;
    supplierEmailRepository;
    emailService;
    oracleService;
    logger = new common_1.Logger(VnfService_1.name);
    isProcessingCron = false;
    lastCronExecution = null;
    tokenPortalCache = null;
    tokenPortalExpiry = null;
    lastTokenRequest = null;
    constructor(invoiceRepository, invoiceItemRepository, productRepository, vnfErrorRepository, vnfConsultaProcessadaRepository, separationOrderRepository, romaneioRepository, romaneioOrderRepository, supplierEmailRepository, emailService, oracleService) {
        this.invoiceRepository = invoiceRepository;
        this.invoiceItemRepository = invoiceItemRepository;
        this.productRepository = productRepository;
        this.vnfErrorRepository = vnfErrorRepository;
        this.vnfConsultaProcessadaRepository = vnfConsultaProcessadaRepository;
        this.separationOrderRepository = separationOrderRepository;
        this.romaneioRepository = romaneioRepository;
        this.romaneioOrderRepository = romaneioOrderRepository;
        this.supplierEmailRepository = supplierEmailRepository;
        this.emailService = emailService;
        this.oracleService = oracleService;
    }
    async obterTokenPortalCompras() {
        try {
            const agora = new Date();
            if (this.tokenPortalCache &&
                this.tokenPortalExpiry &&
                agora < this.tokenPortalExpiry) {
                console.log('[VnfService] 💾 Usando token do portal em cache');
                return this.tokenPortalCache;
            }
            if (this.lastTokenRequest) {
                const timeSinceLastRequest = agora.getTime() - this.lastTokenRequest.getTime();
                const minInterval = 3000;
                if (timeSinceLastRequest < minInterval) {
                    const waitTime = minInterval - timeSinceLastRequest;
                    console.log(`[VnfService] ⏳ Aguardando ${waitTime}ms antes de solicitar novo token`);
                    await this.delay(waitTime);
                }
            }
            console.log('[VnfService] 🔑 Solicitando novo token do portal de compras');
            this.lastTokenRequest = new Date();
            const response = await axios_1.default.post('https://wsprd.portaldecompras.co/API/v1/Token', {
                usuario: process.env.PORTAL_COMPRAS_USUARIO || 'usuario',
                Senha: process.env.PORTAL_COMPRAS_SENHA || 'senha',
            }, {
                headers: {
                    'Content-Type': 'application/json',
                },
                timeout: 15000,
            });
            if (response.data && response.data.token) {
                this.tokenPortalCache = response.data.token;
                this.tokenPortalExpiry = new Date(Date.now() + 25 * 60 * 1000);
                console.log('[VnfService] ✅ Token obtido e armazenado em cache por 25 minutos');
                return response.data.token;
            }
            console.error('[VnfService] Token não encontrado na resposta:', response.data);
            return null;
        }
        catch (error) {
            console.error('[VnfService] Erro ao obter token do portal de compras:', error);
            this.tokenPortalCache = null;
            this.tokenPortalExpiry = null;
            return null;
        }
    }
    async consultarPedidoPortalCompras(numeroPedido, token) {
        try {
            await this.delay(1000);
            console.log(`[VnfService] 🔍 Consultando pedido ${numeroPedido} no portal de compras`);
            const response = await axios_1.default.get(`https://wsprd.portaldecompras.co/api/v1/Pedido/ConsultaPorCodigoERP/?codigoERP=${numeroPedido}`, {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                timeout: 15000,
            });
            return {
                success: true,
                data: response.data,
            };
        }
        catch (error) {
            console.error('[VnfService] Erro ao consultar pedido no portal de compras:', error);
            if (error.response && error.response.status === 404) {
                const errorMessage = error.response.data?.mensagem || error.response.data?.message;
                if (errorMessage &&
                    errorMessage.includes('não localizado no portal de compras')) {
                    return {
                        success: false,
                        error: `Pedido ${numeroPedido} não localizado no portal de compras. Verifique se o número do pedido está correto ou se o pedido ainda está ativo.`,
                    };
                }
            }
            if (error.response && error.response.status === 429) {
                console.warn(`[VnfService] ⚠️ Rate limit atingido no portal de compras para pedido ${numeroPedido}`);
                await this.delay(5000);
                return {
                    success: false,
                    error: 'Rate limit atingido no portal de compras. Tente novamente mais tarde.',
                };
            }
            if (error.response && error.response.status === 401) {
                console.warn(`[VnfService] 🔑 Token inválido, limpando cache`);
                this.tokenPortalCache = null;
                this.tokenPortalExpiry = null;
                return {
                    success: false,
                    error: 'Token do portal de compras expirado. Será renovado na próxima consulta.',
                };
            }
            return {
                success: false,
                error: `Erro ao consultar pedido: ${error.message}`,
            };
        }
    }
    extrairNumeroPedido(infCpl) {
        if (!infCpl)
            return {
                numero: null,
                erro: 'Número do pedido não encontrado nas observações da NF.',
            };
        const padraoPedido1 = /PEDIDO:\s*(\d+)\/(\d{5,})/i;
        const matchPedido1 = infCpl.match(padraoPedido1);
        if (matchPedido1 && matchPedido1[2]) {
            console.log(`[VnfService] Número do pedido encontrado via padrão 1: ${matchPedido1[2]} (de ${matchPedido1[1]}/${matchPedido1[2]})`);
            return { numero: matchPedido1[2] };
        }
        const padraoPedido2 = /(?:PEDIDO\s*(?:N\.?\s*)?(\d{5,}))/i;
        const matchPedido2 = infCpl.match(padraoPedido2);
        if (matchPedido2 && matchPedido2[1]) {
            console.log(`[VnfService] Número do pedido encontrado via padrão 2: ${matchPedido2[1]}`);
            return { numero: matchPedido2[1] };
        }
        const padraoPedido3 = /PEDIDO:\s*(\d{5,})/i;
        const matchPedido3 = infCpl.match(padraoPedido3);
        if (matchPedido3 && matchPedido3[1]) {
            console.log(`[VnfService] Número do pedido encontrado via padrão 3: ${matchPedido3[1]}`);
            return { numero: matchPedido3[1] };
        }
        const padraoPedido4 = /PEDIDO\s+(\d+)\/(\d{5,})/i;
        const matchPedido4 = infCpl.match(padraoPedido4);
        if (matchPedido4 && matchPedido4[2]) {
            console.log(`[VnfService] Número do pedido encontrado via padrão 4: ${matchPedido4[2]} (de ${matchPedido4[1]}/${matchPedido4[2]})`);
            return { numero: matchPedido4[2] };
        }
        const padraoPedido5 = /PEDIDO:\s*(\d{1,4})\/(\d{5,})/i;
        const matchPedido5 = infCpl.match(padraoPedido5);
        if (matchPedido5 && matchPedido5[2]) {
            console.log(`[VnfService] Número do pedido encontrado via padrão 5: ${matchPedido5[2]} (de ${matchPedido5[1]}/${matchPedido5[2]})`);
            return { numero: matchPedido5[2] };
        }
        const linhas = infCpl.split('\n');
        for (const linha of linhas) {
            const numerosLinha = linha.match(/(\d{5,})/g);
            if (numerosLinha) {
                for (const numero of numerosLinha) {
                    if (numero.length === 8 && /^\d{8}$/.test(numero)) {
                        continue;
                    }
                    if (numero.length >= 10 &&
                        numero.length <= 11 &&
                        /^\d+$/.test(numero)) {
                        continue;
                    }
                    if (numero.length === 14 && /^\d{14}$/.test(numero)) {
                        continue;
                    }
                    if (numero.length === 11 && /^\d{11}$/.test(numero)) {
                        continue;
                    }
                    console.log(`[VnfService] Número do pedido encontrado via análise de linha: ${numero}`);
                    return { numero: numero };
                }
            }
        }
        const regexNumeros = /(\d{5,})/g;
        const numeros = infCpl.match(regexNumeros);
        if (!numeros || numeros.length === 0) {
            return {
                numero: null,
                erro: 'Número do pedido não encontrado nas observações da NF.',
            };
        }
        const numerosUnicos = [...new Set(numeros)].filter((numero) => {
            if (numero.length === 8 && /^\d{8}$/.test(numero)) {
                return false;
            }
            if (numero.length >= 10 && numero.length <= 11 && /^\d+$/.test(numero)) {
                return false;
            }
            if (numero.length === 14 && /^\d{14}$/.test(numero)) {
                return false;
            }
            if (numero.length === 11 && /^\d{11}$/.test(numero)) {
                return false;
            }
            return true;
        });
        if (numerosUnicos.length === 0) {
            return {
                numero: null,
                erro: 'Número do pedido não encontrado nas observações da NF.',
            };
        }
        if (numerosUnicos.length === 1) {
            return { numero: numerosUnicos[0] };
        }
        console.log(`[VnfService] Múltiplos números encontrados após filtro, usando o primeiro: ${numerosUnicos[0]}`);
        return { numero: numerosUnicos[0] };
    }
    async buscarEmailFornecedor(nomeFornecedor) {
        if (!nomeFornecedor)
            return null;
        try {
            const nomeNormalizado = nomeFornecedor.trim().toUpperCase();
            const fornecedor = await this.supplierEmailRepository
                .createQueryBuilder('supplier')
                .where('UPPER(supplier.supplierName) LIKE :nome', {
                nome: `%${nomeNormalizado}%`,
            })
                .getOne();
            if (fornecedor) {
                console.log(`[VnfService] Email do fornecedor encontrado: ${fornecedor.email} para ${nomeFornecedor}`);
                return fornecedor.email;
            }
            console.log(`[VnfService] Email do fornecedor não encontrado para: ${nomeFornecedor}`);
            return null;
        }
        catch (error) {
            console.error('[VnfService] Erro ao buscar email do fornecedor:', error);
            return null;
        }
    }
    async consultarXmlSefaz(chaveAcesso) {
        const pfxPath = path.resolve(__dirname, '../../../certs/ELCO_ENGENHARIA_LTDA_77521375000121.pfx');
        const gunzip = (0, util_1.promisify)(zlib.gunzip);
        const httpsAgent = new https.Agent({
            pfx: fs.readFileSync(pfxPath),
            passphrase: process.env.PFX_PASSPHRASE,
        });
        const envelope = `<?xml version="1.0" encoding="UTF-8"?>
<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Header/>
    <soap:Body>
        <nfeDistDFeInteresse xmlns="http://www.portalfiscal.inf.br/nfe/wsdl/NFeDistribuicaoDFe">
            <nfeDadosMsg>
                <distDFeInt versao="1.01" xmlns="http://www.portalfiscal.inf.br/nfe">
                    <tpAmb>1</tpAmb>
                    <CNPJ>77521375000121</CNPJ>
                    <consChNFe>
                        <chNFe>${chaveAcesso}</chNFe>
                    </consChNFe>
                </distDFeInt>
            </nfeDadosMsg>
        </nfeDistDFeInteresse>
    </soap:Body>
</soap:Envelope>`;
        const url = 'https://www1.nfe.fazenda.gov.br/NFeDistribuicaoDFe/NFeDistribuicaoDFe.asmx';
        try {
            const { data } = await axios_1.default.post(url, envelope, {
                httpsAgent,
                headers: {
                    'Content-Type': 'text/xml;charset=utf-8',
                    SOAPAction: 'http://www.portalfiscal.inf.br/nfe/wsdl/NFeDistribuicaoDFe/nfeDistDFeInteresse',
                },
                timeout: 30000,
            });
            const parsed = await (0, xml2js_1.parseStringPromise)(data, { explicitArray: false });
            const retDist = parsed['soap:Envelope']['soap:Body']['nfeDistDFeInteresseResponse']['nfeDistDFeInteresseResult']['retDistDFeInt'];
            const cStat = retDist.cStat;
            const xMotivo = retDist.xMotivo;
            if (cStat !== '138') {
                return {
                    success: false,
                    status: cStat,
                    message: xMotivo,
                    xml: null,
                    pdfBase64: null,
                };
            }
            const rawDocZip = retDist?.loteDistDFeInt?.docZip;
            const docZipArr = Array.isArray(rawDocZip)
                ? rawDocZip
                : rawDocZip
                    ? [rawDocZip]
                    : [];
            let procNfeXml = null;
            for (const item of docZipArr) {
                try {
                    const base64 = typeof item === 'string' ? item : item._;
                    const xml = (await gunzip(Buffer.from(base64, 'base64'))).toString('utf-8');
                    if (xml.includes('<nfeProc')) {
                        procNfeXml = xml;
                        break;
                    }
                }
                catch {
                    continue;
                }
            }
            if (!procNfeXml) {
                return {
                    success: true,
                    status: cStat,
                    message: 'NF-e não encontrada no lote.',
                    xml: null,
                    pdfBase64: null,
                };
            }
            return {
                success: true,
                status: cStat,
                message: xMotivo,
                xml: procNfeXml,
                pdfBase64: null,
            };
        }
        catch (error) {
            console.error('[VnfService] Erro ao consultar XML na SEFAZ:', error);
            return {
                success: false,
                message: `Erro ao consultar XML na SEFAZ: ${error.message}`,
            };
        }
    }
    async consultarXmlPorChave(chaveAcesso) {
        try {
            if (!chaveAcesso || chaveAcesso.length !== 44) {
                return {
                    success: false,
                    message: 'Chave de acesso inválida. Deve conter 44 dígitos.',
                };
            }
            const resultado = await this.consultarXmlSefaz(chaveAcesso);
            if (!resultado.success) {
                return {
                    success: false,
                    message: resultado.message,
                    status: resultado.status,
                };
            }
            if (resultado.xml) {
                try {
                    const xmlJson = await (0, xml2js_1.parseStringPromise)(resultado.xml, {
                        explicitArray: false,
                    });
                    const notaFiscal = this.mapearCampos(xmlJson);
                    let dadosPedido = null;
                    const resultadoExtracao = this.extrairNumeroPedido(notaFiscal.informacoesAdicionais?.infCpl);
                    if (resultadoExtracao.numero) {
                        console.log(`[VnfService] Número do pedido extraído: ${resultadoExtracao.numero}`);
                        const token = await this.obterTokenPortalCompras();
                        if (token) {
                            dadosPedido = await this.consultarPedidoPortalCompras(resultadoExtracao.numero, token);
                            if (dadosPedido && dadosPedido.success && dadosPedido.data) {
                                console.log('[VnfService] Iniciando comparação entre pedido e nota fiscal');
                                const resultadoComparacao = await this.processarComparacaoPedidoNotaFiscal(dadosPedido.data, notaFiscal, notaFiscal.numeroNota, resultadoExtracao.numero);
                                if (!resultadoComparacao.success) {
                                    console.log(`[VnfService] Encontradas ${resultadoComparacao.errors.length} divergências na comparação`);
                                    dadosPedido.error = `Encontradas ${resultadoComparacao.errors.length} divergências na comparação`;
                                }
                                else {
                                    console.log('[VnfService] Nota fiscal aprovada na comparação com o pedido');
                                }
                            }
                        }
                        else {
                            console.error('[VnfService] Não foi possível obter token do portal de compras');
                        }
                    }
                    else {
                        console.log('[VnfService] Número do pedido não encontrado no XML');
                    }
                    return {
                        success: true,
                        message: 'XML consultado com sucesso',
                        xml: resultado.xml,
                        pdfBase64: resultado.pdfBase64,
                        notaFiscal: notaFiscal,
                        chaveAcesso: chaveAcesso,
                        numeroPedido: resultadoExtracao.numero,
                        dadosPedido: dadosPedido,
                    };
                }
                catch (parseError) {
                    console.error('[VnfService] Erro ao processar XML:', parseError);
                    return {
                        success: true,
                        message: 'XML consultado, mas houve erro no processamento',
                        xml: resultado.xml,
                        pdfBase64: resultado.pdfBase64,
                        chaveAcesso: chaveAcesso,
                    };
                }
            }
            return {
                success: false,
                message: 'XML não encontrado para a chave de acesso informada',
            };
        }
        catch (error) {
            console.error('[VnfService] Erro ao consultar XML por chave:', error);
            return {
                success: false,
                message: `Erro ao consultar XML: ${error.message}`,
            };
        }
    }
    async processarXmlNfe(params) {
        const { xml, ...rest } = params;
        const xmlJson = await (0, xml2js_1.parseStringPromise)(xml, { explicitArray: false });
        const friendlyJson = this.mapearCampos(xmlJson);
        const resultadoVerificacao = await this.verificarNotaFiscal({
            ...rest,
            notaFiscal: friendlyJson,
        });
        return {
            ...rest,
            notaFiscal: friendlyJson,
            resultadoVerificacao,
        };
    }
    mapearCampos(xmlJson) {
        const proc = xmlJson.nfeProc || xmlJson['nfeProc'];
        if (!proc)
            return xmlJson;
        const nfe = proc.NFe || proc['NFe'];
        const protNFe = proc.protNFe || proc['protNFe'];
        const infNFe = nfe?.infNFe || nfe?.['infNFe'];
        const ide = infNFe?.ide;
        const emit = infNFe?.emit;
        const dest = infNFe?.dest;
        const produtos = Array.isArray(infNFe?.det) ? infNFe.det : [infNFe?.det];
        return {
            versao: proc.$?.versao,
            numeroNota: ide?.nNF,
            dataEmissao: ide?.dhEmi,
            naturezaOperacao: ide?.natOp,
            modelo: ide?.mod,
            serie: ide?.serie,
            emitente: {
                cnpj: emit?.CNPJ,
                nome: emit?.xNome,
                fantasia: emit?.xFant,
                endereco: emit?.enderEmit,
                inscricaoEstadual: emit?.IE,
                regimeTributario: emit?.CRT,
                email: emit?.email,
            },
            destinatario: {
                cnpj: dest?.CNPJ,
                nome: dest?.xNome,
                endereco: dest?.enderDest,
                inscricaoEstadual: dest?.IE,
                indicadorIE: dest?.indIEDest,
                email: dest?.email,
            },
            produtos: produtos?.map((item) => ({
                numeroItem: item.$?.nItem,
                codigo: item.prod?.cProd,
                descricao: item.prod?.xProd,
                ncm: item.prod?.NCM,
                cfop: item.prod?.CFOP,
                unidade: item.prod?.uCom,
                quantidade: item.prod?.qCom,
                valorUnitario: item.prod?.vUnCom,
                valorTotal: item.prod?.vProd,
                ean: item.prod?.cEAN,
                volume: item.prod?.volume,
            })),
            total: infNFe?.total?.ICMSTot,
            transporte: infNFe?.transp,
            pagamento: infNFe?.pag,
            informacoesAdicionais: infNFe?.infAdic,
            protocolo: protNFe?.infProt,
        };
    }
    async verificarNotaFiscal(params) {
        const { idNota, idOrdem, idRomaneio, notaFiscal } = params;
        const invoice = await this.invoiceRepository.findOne({
            where: { id: idNota },
        });
        const separationOrder = await this.separationOrderRepository.findOne({
            where: { id: idOrdem },
        });
        const romaneio = await this.romaneioRepository.findOne({
            where: { id: idRomaneio },
        });
        const romaneioOrder = await this.romaneioOrderRepository.findOne({
            where: { separationOrderId: idOrdem, romaneioId: idRomaneio },
        });
        const invoiceItems = await this.invoiceItemRepository.find({
            where: { invoiceId: idNota },
        });
        const erros = [];
        const validarCampo = (campo, valorSistema, valorXml) => {
            const v1 = valorSistema ?? '';
            const v2 = valorXml ?? '';
            if (!v1 && v2) {
                erros.push(`Campo ${campo} está preenchido no XML mas ausente no sistema`);
            }
            else if (v1 && !v2) {
                erros.push(`Campo ${campo} está ausente no XML mas preenchido no sistema`);
            }
            else if (v1?.toString().trim() !== v2?.toString().trim()) {
                erros.push(`Divergência no campo ${campo}: sistema='${v1}' vs XML='${v2}'`);
            }
        };
        if (invoice) {
            validarCampo('numeroNota', invoice.notInNumero, notaFiscal.numeroNota);
            validarCampo('dataEmissao', invoice.notDtEmissao?.toISOString(), notaFiscal.dataEmissao);
            validarCampo('horaEmissao', invoice.notHrHoraemissao, notaFiscal.dataEmissao?.split('T')?.[1]?.substring(0, 8));
            validarCampo('cnpjEmitente', invoice.notStCgc, notaFiscal.emitente?.cnpj);
            validarCampo('ufEmitente', invoice.notStUf, notaFiscal.emitente?.endereco?.UF);
            validarCampo('municipioEmitente', invoice.notStMunicipio, notaFiscal.emitente?.endereco?.xMun);
            validarCampo('inscricaoEstadual', invoice.notStIncrestadual, notaFiscal.emitente?.inscricaoEstadual);
            validarCampo('cfop', invoice.cfopStDescricao, notaFiscal.produtos?.[0]?.cfop);
            validarCampo('chaveAcesso', invoice.notStChaveacesso, notaFiscal.protocolo?.chNFe);
            validarCampo('numeroDanfe', invoice.numeroDanfe, notaFiscal.numeroNota);
            validarCampo('status', invoice.status, notaFiscal.protocolo?.cStat === '100' ? 'APROVADA' : 'REJEITADA');
            validarCampo('xml', invoice.xml ? 'ok' : '', notaFiscal ? 'ok' : '');
        }
        if (romaneio && notaFiscal?.transporte?.vol) {
            const vol = notaFiscal.transporte.vol;
            validarCampo('pesoLiquido', romaneio.totalWeight, parseInt(vol.pesoL));
            validarCampo('pesoBruto', romaneio.totalWeight, parseInt(vol.pesoB));
            validarCampo('volumeQtd', romaneio.totalVolume, parseInt(vol.qVol));
            validarCampo('dataRomaneio', romaneio.dateIssue?.toISOString()?.split('T')?.[0], notaFiscal.dataEmissao?.split('T')?.[0]);
            const enderecoXml = notaFiscal.informacoesAdicionais?.infCpl || '';
            if (enderecoXml && romaneio.address) {
                const enderecoSistema = romaneio.address.trim().toLowerCase();
                const enderecoXmlTxt = enderecoXml.trim().toLowerCase();
                if (!enderecoXmlTxt.includes(enderecoSistema)) {
                    erros.push(`Endereço do romaneio não encontrado no XML (romaneio='${romaneio.address}')`);
                }
            }
        }
        const produtosXml = notaFiscal.produtos ?? [];
        for (const item of invoiceItems) {
            const xml = produtosXml.find((p) => p.codigo === item.notInCodigo?.toString());
            if (!xml) {
                erros.push(`Produto com código ${item.notInCodigo} não encontrado no XML`);
                continue;
            }
            validarCampo(`Descrição do produto ${item.notInCodigo}`, item.itnStDescricao, xml.descricao);
            validarCampo(`Quantidade ${item.notInCodigo}`, item.itnReQuantidade, xml.quantidade);
            validarCampo(`Valor unitário ${item.notInCodigo}`, item.itnReValorunitario, xml.valorUnitario);
            validarCampo(`Valor total ${item.notInCodigo}`, item.itnReValortotal, xml.valorTotal);
        }
        for (const erro of erros) {
            await this.salvarErroVnf(notaFiscal.numeroNota, separationOrder?.externalCode || null, erro, notaFiscal.destinatario?.email || null, notaFiscal.emitente?.email || null, notaFiscal.emitente?.nome || null);
        }
        return {
            invoice,
            separationOrder,
            invoiceItems,
            romaneio,
            romaneioOrder,
            produtos: produtosXml,
            erros,
        };
    }
    async salvarErroVnf(numeroNota, clienteExternalCode, tipoErro, compradorEmail, fornecedorEmail, nomeFornecedor, podeRecusar = true) {
        const erro = this.vnfErrorRepository.create({
            numeroNota: numeroNota || '',
            clienteExternalCode: clienteExternalCode || null,
            nomeFornecedor: nomeFornecedor || null,
            tipoErro: tipoErro || '',
            compradorEmail: compradorEmail || null,
            fornecedorEmail: fornecedorEmail || null,
            dataEmailEnviado: undefined,
            podeRecusar: podeRecusar,
        });
        await this.vnfErrorRepository.save(erro);
    }
    validarCNPJ(cnpj) {
        cnpj = cnpj.replace(/[\D]/g, '');
        if (cnpj.length !== 14)
            return false;
        let tamanho = cnpj.length - 2;
        let numeros = cnpj.substring(0, tamanho);
        const digitos = cnpj.substring(tamanho);
        let soma = 0;
        let pos = tamanho - 7;
        for (let i = tamanho; i >= 1; i--) {
            soma += +numeros.charAt(tamanho - i) * pos--;
            if (pos < 2)
                pos = 9;
        }
        let resultado = soma % 11 < 2 ? 0 : 11 - (soma % 11);
        if (resultado !== +digitos.charAt(0))
            return false;
        tamanho = tamanho + 1;
        numeros = cnpj.substring(0, tamanho);
        soma = 0;
        pos = tamanho - 7;
        for (let i = tamanho; i >= 1; i--) {
            soma += +numeros.charAt(tamanho - i) * pos--;
            if (pos < 2)
                pos = 9;
        }
        resultado = soma % 11 < 2 ? 0 : 11 - (soma % 11);
        if (resultado !== +digitos.charAt(1))
            return false;
        return true;
    }
    async listarErrosVnf() {
        return this.vnfErrorRepository.find({
            where: { deletedAt: (0, typeorm_2.IsNull)() },
            order: { createdAt: 'DESC' },
        });
    }
    async compararPedidoComNotaFiscal(pedido, notaFiscal) {
        const errors = [];
        this.validarCabecalhos(pedido, notaFiscal, errors);
        this.validarProdutos(pedido, notaFiscal, errors);
        this.validarDataEntrega(pedido, notaFiscal, errors);
        this.validarPrazoLimiteEmissao(notaFiscal, errors);
        this.validarCoerenciaValores(notaFiscal, errors);
        this.validarRegrasFinais(notaFiscal, pedido, errors);
        return errors;
    }
    validarCabecalhos(pedido, notaFiscal, errors) {
        const resultadoExtracao = this.extrairNumeroPedido(notaFiscal.informacoesAdicionais?.infCpl);
        if (resultadoExtracao.erro) {
            errors.push(`NF Emitida Incoerente! ${resultadoExtracao.erro}`);
            return;
        }
        const pedidoEsperado = pedido.pedidoERP
            ? pedido.pedidoERP.toString().trim()
            : pedido.pedidoERP.toString().trim();
        const numeroPedidoExtraido = resultadoExtracao.numero
            ? resultadoExtracao.numero.toString().trim()
            : null;
        if (!numeroPedidoExtraido || numeroPedidoExtraido !== pedidoEsperado) {
            errors.push(`NF Emitida Incoerente! Número do pedido não encontrado ou divergente. Esperado: ${pedidoEsperado}, Encontrado: ${numeroPedidoExtraido || 'não encontrado'}`);
        }
        if (pedido.cnpjFornecedor !== notaFiscal.emitente.cnpj) {
            errors.push(`NF Emitida Incoerente! CNPJ do fornecedor divergente. Pedido: ${pedido.cnpjFornecedor}, NF: ${notaFiscal.emitente.cnpj}`);
        }
        if (pedido.cnpjPlanta !== notaFiscal.destinatario.cnpj) {
            errors.push(`NF Emitida Incoerente! CNPJ da planta divergente. Pedido: ${pedido.cnpjPlanta}, NF: ${notaFiscal.destinatario.cnpj}`);
        }
        const nomeFornecedorNormalizado = this.normalizarTexto(pedido.nomeFornecedor);
        const nomeEmitenteNormalizado = this.normalizarTexto(notaFiscal.emitente.nome);
        if (nomeFornecedorNormalizado !== nomeEmitenteNormalizado) {
            errors.push(`NF Emitida Incoerente! Nome do fornecedor divergente. Pedido: ${pedido.nomeFornecedor}, NF: ${notaFiscal.emitente.nome}`);
        }
        const condicaoPagamentoEncontrada = this.extrairCondicaoPagamento(notaFiscal.informacoesAdicionais);
        const condicaoPedidoNormalizada = pedido.condicaoPagamento.replace(/\D/g, '').replace(/^0+/, '') || '0';
        const condicaoNFNormalizada = condicaoPagamentoEncontrada
            ? condicaoPagamentoEncontrada.replace(/^0+/, '') || '0'
            : '';
        if (condicaoNFNormalizada &&
            condicaoPedidoNormalizada !== condicaoNFNormalizada) {
            errors.push(`NF Emitida Incoerente! Condição de pagamento divergente. Pedido: ${pedido.condicaoPagamento}, NF: ${condicaoPagamentoEncontrada || 'não encontrada'}`);
        }
    }
    validarProdutos(pedido, notaFiscal, errors) {
        const produtosPedido = pedido.itens;
        const produtosNF = notaFiscal.produtos;
        if (produtosNF.length > 30) {
            errors.push(`NF Emitida Incoerente! Nota fiscal possui mais de 30 itens (${produtosNF.length})`);
            return;
        }
        for (const itemPedido of produtosPedido) {
            const produtoNF = produtosNF.find((p) => parseInt(p.numeroItem) === itemPedido.sequencial);
            if (!produtoNF) {
                errors.push(`NF Emitida Incoerente! Item ${itemPedido.sequencial} do pedido não encontrado na nota fiscal`);
                continue;
            }
            if (parseInt(produtoNF.numeroItem) !== itemPedido.sequencial) {
                errors.push(`NF Emitida Incoerente! Sequencial do item ${itemPedido.sequencial} divergente. Pedido: ${itemPedido.sequencial}, NF: ${produtoNF.numeroItem}`);
            }
            if (produtoNF.unidade !== itemPedido.codigoUnidadeMedida) {
                errors.push(`NF Emitida Incoerente! Unidade de medida do item ${itemPedido.sequencial} divergente. Pedido: ${itemPedido.codigoUnidadeMedida}, NF: ${produtoNF.unidade}`);
            }
            if (produtoNF.ncm !== itemPedido.nmc) {
                errors.push(`NF Emitida Incoerente! NCM do item ${itemPedido.sequencial} divergente. Pedido: ${itemPedido.nmc}, NF: ${produtoNF.ncm}`);
            }
            const valorUnitarioPedido = itemPedido.valorUnitario;
            const valorUnitarioNF = parseFloat(produtoNF.valorUnitario);
            const diferenca = Math.abs(valorUnitarioPedido - valorUnitarioNF);
            if (diferenca > 0.01) {
                errors.push(`NF Emitida Incoerente! Valor unitário do item ${itemPedido.sequencial} divergente. Pedido: ${valorUnitarioPedido}, NF: ${valorUnitarioNF}`);
            }
            const quantidadePedido = itemPedido.quantidade;
            const quantidadeNF = parseFloat(produtoNF.quantidade);
            if (quantidadeNF > quantidadePedido) {
                errors.push(`NF Emitida Incoerente! Quantidade do item ${itemPedido.sequencial} excede o pedido. Pedido: ${quantidadePedido}, NF: ${quantidadeNF}`);
            }
        }
    }
    validarRegrasFinais(notaFiscal, pedido, errors) {
        const valorDesconto = parseFloat(notaFiscal.total.vDesc);
        if (valorDesconto > 0) {
            errors.push(`NF Emitida Incoerente! Nota fiscal possui desconto (${valorDesconto})`);
        }
        const valorTotalNF = parseFloat(notaFiscal.total.vNF);
        const valorTotalPedido = pedido.valorTotalComFrete;
        const diferenca = Math.abs(valorTotalNF - valorTotalPedido);
        if (diferenca > 0.01) {
            errors.push(`NF Emitida Incoerente! Valor total da nota fiscal divergente. Pedido: ${valorTotalPedido}, NF: ${valorTotalNF}`);
        }
    }
    extrairCondicaoPagamento(informacoesAdicionais) {
        if (!informacoesAdicionais?.obsCont)
            return null;
        const obsCont = Array.isArray(informacoesAdicionais.obsCont)
            ? informacoesAdicionais.obsCont
            : [informacoesAdicionais.obsCont];
        for (const obs of obsCont) {
            if (obs.$?.xCampo === 'Condicao Pgto:' && obs.xTexto) {
                const match = obs.xTexto.match(/^(\d+)/);
                if (match) {
                    const numero = match[1].replace(/^0+/, '');
                    return numero || '0';
                }
                const numeros = obs.xTexto.replace(/\D/g, '');
                return numeros.replace(/^0+/, '') || '0';
            }
        }
        return null;
    }
    normalizarTexto(texto) {
        return texto
            .normalize('NFD')
            .replace(/[\u0300-\u036f]/g, '')
            .toLowerCase()
            .trim();
    }
    validarDataEntrega(pedido, notaFiscal, errors) {
        if (!pedido.itens || !notaFiscal.dataEmissao)
            return;
        const dataEmissaoNF = new Date(notaFiscal.dataEmissao);
        for (const item of pedido.itens) {
            if (!item.entrega || !Array.isArray(item.entrega))
                continue;
            for (const entrega of item.entrega) {
                if (!entrega.dataDeEntrega)
                    continue;
                const dataEntrega = new Date(entrega.dataDeEntrega);
                const diferencaDias = Math.ceil((dataEmissaoNF.getTime() - dataEntrega.getTime()) /
                    (1000 * 60 * 60 * 24));
                if (diferencaDias > 10) {
                    errors.push(`NF Emitida Incoerente! Data de entrega do item ${item.sequencial} antecipada em ${diferencaDias} dias (limite = 10).`);
                }
            }
        }
    }
    validarPrazoLimiteEmissao(notaFiscal, errors) {
        if (!notaFiscal.dataEmissao)
            return;
        const dataEmissaoNF = new Date(notaFiscal.dataEmissao);
        const diaEmissao = dataEmissaoNF.getDate();
        if (diaEmissao > 25) {
            const mesEmissao = dataEmissaoNF.toLocaleDateString('pt-BR', {
                month: 'long',
                year: 'numeric',
            });
            errors.push(`NF Emitida Incoerente! Nota fiscal emitida após o prazo limite. Emitida no dia ${diaEmissao} de ${mesEmissao} (limite: dia 25).`);
        }
    }
    validarCoerenciaValores(notaFiscal, errors) {
        if (!notaFiscal.produtos)
            return;
        for (const produto of notaFiscal.produtos) {
            const valorUnitario = parseFloat(produto.valorUnitario);
            const quantidade = parseFloat(produto.quantidade);
            const valorTotalInformado = parseFloat(produto.valorTotal);
            const valorTotalCalculado = valorUnitario * quantidade;
            const diferenca = Math.abs(valorTotalCalculado - valorTotalInformado);
            if (diferenca > 0.01) {
                errors.push(`NF Emitida Incoerente! Valor total inconsistente no item ${produto.numeroItem} (esperado ${valorTotalCalculado.toFixed(2)}, informado ${valorTotalInformado.toFixed(2)}).`);
            }
        }
    }
    async processarComparacaoPedidoNotaFiscal(pedido, notaFiscal, numeroNota, clienteExternalCode) {
        try {
            const errors = await this.compararPedidoComNotaFiscal(pedido, notaFiscal);
            const fornecedorEmail = await this.buscarEmailFornecedor(pedido.nomeFornecedor);
            const hoje = new Date();
            const diaDoMes = hoje.getDate();
            const ultimoDiaDoMes = new Date(hoje.getFullYear(), hoje.getMonth() + 1, 0).getDate();
            const estaNaJanelaDeRecusa = diaDoMes >= 26 && diaDoMes <= ultimoDiaDoMes;
            for (const error of errors) {
                await this.salvarErroVnf(numeroNota, clienteExternalCode || pedido.pedidoERP, error, pedido.loginComprador, fornecedorEmail, pedido.nomeFornecedor, !estaNaJanelaDeRecusa);
            }
            return {
                success: errors.length === 0,
                errors,
            };
        }
        catch (error) {
            console.error('[VnfService] Erro ao processar comparação:', error);
            return {
                success: false,
                errors: [`Erro interno ao processar comparação: ${error.message}`],
            };
        }
    }
    async enviarEmailDivergencia(erroId) {
        try {
            const erro = await this.vnfErrorRepository.findOne({
                where: { id: erroId },
            });
            if (!erro) {
                return { success: false, message: 'Erro não encontrado' };
            }
            if (!erro.fornecedorEmail) {
                return {
                    success: false,
                    message: 'Email do fornecedor não encontrado',
                };
            }
            const emailContent = this.gerarConteudoEmail(erro);
            const resultadoEmail = await this.emailService.enviarEmailDivergencia(erro.fornecedorEmail, erro.numeroNota, emailContent, erro.nomeFornecedor);
            if (!resultadoEmail.success) {
                return {
                    success: false,
                    message: resultadoEmail.message,
                };
            }
            erro.dataEmailEnviado = new Date();
            await this.vnfErrorRepository.save(erro);
            return {
                success: true,
                message: `Email enviado com sucesso para ${erro.fornecedorEmail}`,
            };
        }
        catch (error) {
            console.error('[VnfService] Erro ao enviar email:', error);
            return {
                success: false,
                message: `Erro ao enviar email: ${error.message}`,
            };
        }
    }
    async enviarEmailsEmLote(erroIds, emailsAdicionais) {
        const results = [];
        try {
            const erros = await this.vnfErrorRepository.find({
                where: erroIds.map((id) => ({ id })),
                order: { createdAt: 'ASC' },
            });
            if (erros.length === 0) {
                return {
                    success: false,
                    results: erroIds.map((id) => ({
                        id,
                        success: false,
                        message: 'Erro não encontrado',
                    })),
                };
            }
            const grupos = new Map();
            for (const erro of erros) {
                const chave = `${erro.fornecedorEmail || 'sem-email'}_${erro.numeroNota}`;
                if (!grupos.has(chave)) {
                    grupos.set(chave, []);
                }
                grupos.get(chave).push(erro);
            }
            for (const [chave, errosGrupo] of grupos) {
                const primeiroErro = errosGrupo[0];
                if (!primeiroErro.fornecedorEmail) {
                    for (const erro of errosGrupo) {
                        results.push({
                            id: erro.id,
                            success: false,
                            message: 'Email do fornecedor não encontrado',
                        });
                    }
                    continue;
                }
                const emailContent = this.gerarConteudoEmailAgrupado(errosGrupo);
                const emailsParaEnviar = [primeiroErro.fornecedorEmail];
                if (emailsAdicionais && emailsAdicionais.length > 0) {
                    emailsParaEnviar.push(...emailsAdicionais);
                }
                console.log(`[VnfService] Emails adicionais recebidos:`, emailsAdicionais);
                console.log(`[VnfService] Email do fornecedor: ${primeiroErro.fornecedorEmail}`);
                console.log(`[VnfService] Lista final de emails para enviar:`, emailsParaEnviar);
                let todosEnviadosComSucesso = true;
                const mensagensEnvio = [];
                console.log(`[VnfService] Enviando emails para: ${emailsParaEnviar.join(', ')}`);
                console.log(`[VnfService] Conteúdo do email: ${emailContent.substring(0, 200)}...`);
                for (const emailDestinatario of emailsParaEnviar) {
                    try {
                        console.log(`[VnfService] Tentando enviar email para: ${emailDestinatario}`);
                        const resultadoEmail = await this.emailService.enviarEmailDivergencia(emailDestinatario, primeiroErro.numeroNota, emailContent, primeiroErro.nomeFornecedor);
                        console.log(`[VnfService] Resultado do envio para ${emailDestinatario}:`, resultadoEmail);
                        if (resultadoEmail.success) {
                            mensagensEnvio.push(`Email enviado com sucesso para ${emailDestinatario}`);
                        }
                        else {
                            todosEnviadosComSucesso = false;
                            mensagensEnvio.push(`Falha ao enviar para ${emailDestinatario}: ${resultadoEmail.message}`);
                        }
                    }
                    catch (error) {
                        console.error(`[VnfService] Erro ao enviar para ${emailDestinatario}:`, error);
                        todosEnviadosComSucesso = false;
                        mensagensEnvio.push(`Erro ao enviar para ${emailDestinatario}: ${error.message}`);
                    }
                }
                if (todosEnviadosComSucesso) {
                    for (const erro of errosGrupo) {
                        erro.dataEmailEnviado = new Date();
                        await this.vnfErrorRepository.save(erro);
                        results.push({
                            id: erro.id,
                            success: true,
                            message: mensagensEnvio.join('; '),
                        });
                    }
                }
                else {
                    for (const erro of errosGrupo) {
                        results.push({
                            id: erro.id,
                            success: false,
                            message: mensagensEnvio.join('; '),
                        });
                    }
                }
            }
            const successCount = results.filter((r) => r.success).length;
            const totalCount = results.length;
            return {
                success: successCount > 0,
                results,
            };
        }
        catch (error) {
            console.error('[VnfService] Erro ao enviar emails em lote:', error);
            return {
                success: false,
                results: erroIds.map((id) => ({
                    id,
                    success: false,
                    message: `Erro interno: ${error.message}`,
                })),
            };
        }
    }
    gerarConteudoEmail(erro) {
        const numeroNota = erro.numeroNota;
        const fornecedorNome = this.extrairNomeFornecedorDoErro(erro.tipoErro);
        const detalhesErro = this.extrairDetalhesDoErro(erro.tipoErro);
        return `Prezado Fornecedor,

Durante o processo automático de conferência da Nota Fiscal nº ${numeroNota}, foram identificadas divergências no arquivo XML em relação ao nosso Pedido de Compra, conforme detalhado abaixo:

${detalhesErro}

Solicitamos a revisão e o reenvio imediato do arquivo XML corrigido para o e-mail: <EMAIL>

Informamos que, caso não haja retorno com a devida correção no prazo máximo de 24 horas, a NF-e será recusada automaticamente e o recebimento, rejeitado.

Importante: Notas Fiscais emitidas sem autorização formal do Departamento de Compras, por meio do Pedido de Compra, serão recusadas automaticamente, sem possibilidade de revisão.

Atenciosamente,

Departamento de Compras
Elco Engenharia`;
    }
    gerarConteudoEmailAgrupado(erros) {
        const primeiroErro = erros[0];
        const numeroNota = primeiroErro.numeroNota;
        const fornecedorNome = this.extrairNomeFornecedorDoErro(primeiroErro.tipoErro);
        const detalhesErros = erros
            .map((erro) => this.extrairDetalhesDoErro(erro.tipoErro))
            .join('\n\n');
        return `Prezado Fornecedor,

Durante o processo automático de conferência da Nota Fiscal nº ${numeroNota}, foram identificadas ${erros.length} divergência(s) no arquivo XML em relação ao nosso Pedido de Compra, conforme detalhado abaixo:

${detalhesErros}

Solicitamos a revisão e o reenvio imediato do arquivo XML corrigido para o e-mail: <EMAIL>

Informamos que, caso não haja retorno com a devida correção no prazo máximo de 24 horas, a NF-e será recusada automaticamente e o recebimento, rejeitado.

Importante: Notas Fiscais emitidas sem autorização formal do Departamento de Compras, por meio do Pedido de Compra, serão recusadas automaticamente, sem possibilidade de revisão.

Atenciosamente,

Departamento de Compras
Elco Engenharia`;
    }
    extrairNomeFornecedorDoErro(tipoErro) {
        const match = tipoErro.match(/fornecedor divergente\. Pedido: ([^,]+)/);
        if (match) {
            return match[1].trim();
        }
        return 'Fornecedor';
    }
    extrairDetalhesDoErro(tipoErro) {
        if (tipoErro.includes('Quantidade')) {
            const match = tipoErro.match(/Quantidade do item (\d+) excede o pedido\. Pedido: ([\d.]+), NF: ([\d.]+)/);
            if (match) {
                const [, item, pedido, nf] = match;
                return `• Quantidade informada: ${nf} peças
• Quantidade correta: ${pedido} peças, conforme descrito no Pedido de Compra.`;
            }
        }
        if (tipoErro.includes('Valor unitário')) {
            const match = tipoErro.match(/Valor unitário do item (\d+) divergente\. Pedido: ([\d.]+), NF: ([\d.]+)/);
            if (match) {
                const [, item, pedido, nf] = match;
                return `• Valor unitário informado: R$ ${nf}
• Valor unitário correto: R$ ${pedido}, conforme descrito no Pedido de Compra.`;
            }
        }
        if (tipoErro.includes('CNPJ do fornecedor')) {
            const match = tipoErro.match(/CNPJ do fornecedor divergente\. Pedido: ([\d.]+), NF: ([\d.]+)/);
            if (match) {
                const [, pedido, nf] = match;
                return `• CNPJ informado: ${nf}
• CNPJ correto: ${pedido}, conforme descrito no Pedido de Compra.`;
            }
        }
        return `• ${tipoErro}`;
    }
    async aceitarErro(erroId) {
        try {
            const erro = await this.vnfErrorRepository.findOne({
                where: { id: erroId },
            });
            if (!erro) {
                return { success: false, message: 'Erro não encontrado' };
            }
            if (!erro.fornecedorEmail) {
                return {
                    success: false,
                    message: 'Email do fornecedor não encontrado',
                };
            }
            const emailContent = this.gerarConteudoEmail(erro);
            const resultadoEmail = await this.emailService.enviarEmailDivergencia(erro.fornecedorEmail, erro.numeroNota, emailContent, erro.nomeFornecedor);
            if (!resultadoEmail.success) {
                return {
                    success: false,
                    message: resultadoEmail.message,
                };
            }
            erro.dataEmailEnviado = new Date();
            await this.vnfErrorRepository.save(erro);
            return {
                success: true,
                message: `Email enviado com sucesso para ${erro.fornecedorEmail}`,
            };
        }
        catch (error) {
            console.error('[VnfService] Erro ao aceitar erro:', error);
            return {
                success: false,
                message: `Erro ao aceitar erro: ${error.message}`,
            };
        }
    }
    async recusarErro(erroId) {
        try {
            const erro = await this.vnfErrorRepository.findOne({
                where: { id: erroId },
            });
            if (!erro) {
                return { success: false, message: 'Erro não encontrado' };
            }
            await this.vnfErrorRepository.softDelete(erroId);
            return {
                success: true,
                message: 'Erro recusado com sucesso',
            };
        }
        catch (error) {
            console.error('[VnfService] Erro ao recusar erro:', error);
            return {
                success: false,
                message: `Erro ao recusar erro: ${error.message}`,
            };
        }
    }
    async testarConexaoEmail() {
        return this.emailService.testarConexao();
    }
    async testarEnvioEmail(email) {
        try {
            console.log(`[VnfService] Testando envio de email para: ${email}`);
            const resultado = await this.emailService.enviarEmailDivergencia(email, 'TESTE-001', `Este é um email de teste do sistema VNF.`, 'Fornecedor Teste');
            return resultado;
        }
        catch (error) {
            console.error('[VnfService] Erro ao testar envio de email:', error);
            return {
                success: false,
                message: `Erro ao testar envio: ${error.message}`,
            };
        }
    }
    async salvarEmailFornecedor(nomeFornecedor, email) {
        try {
            if (!nomeFornecedor || !email) {
                return {
                    success: false,
                    message: 'Nome do fornecedor e email são obrigatórios',
                };
            }
            const nomeNormalizado = nomeFornecedor.trim().toUpperCase();
            const fornecedorExistente = await this.supplierEmailRepository
                .createQueryBuilder('supplier')
                .where('UPPER(supplier.supplierName) = :nome', {
                nome: nomeNormalizado,
            })
                .getOne();
            let fornecedor;
            if (fornecedorExistente) {
                fornecedorExistente.email = email;
                fornecedor =
                    await this.supplierEmailRepository.save(fornecedorExistente);
                console.log(`[VnfService] Email do fornecedor atualizado: ${email} para ${nomeNormalizado}`);
            }
            else {
                fornecedor = await this.supplierEmailRepository.save({
                    supplierName: nomeNormalizado,
                    email: email,
                });
                console.log(`[VnfService] Novo fornecedor criado: ${nomeNormalizado} com email ${email}`);
            }
            const errosParaAtualizar = await this.vnfErrorRepository
                .createQueryBuilder('erro')
                .where('UPPER(erro.nomeFornecedor) = :nome', { nome: nomeNormalizado })
                .getMany();
            if (errosParaAtualizar.length > 0) {
                for (const erro of errosParaAtualizar) {
                    erro.fornecedorEmail = email;
                }
                await this.vnfErrorRepository.save(errosParaAtualizar);
                console.log(`[VnfService] ${errosParaAtualizar.length} erro(s) VNF atualizado(s) com o novo email`);
            }
            return {
                success: true,
                message: fornecedorExistente
                    ? `Email do fornecedor atualizado com sucesso. ${errosParaAtualizar.length} erro(s) VNF atualizado(s).`
                    : `Email do fornecedor salvo com sucesso. ${errosParaAtualizar.length} erro(s) VNF atualizado(s).`,
                fornecedor: {
                    id: fornecedor.id,
                    supplierName: fornecedor.supplierName,
                    email: fornecedor.email,
                },
            };
        }
        catch (error) {
            console.error('[VnfService] Erro ao salvar email do fornecedor:', error);
            return {
                success: false,
                message: `Erro ao salvar email do fornecedor: ${error.message}`,
            };
        }
    }
    async buscarChavesAcessoOracle(data) {
        try {
            const query = `
                SELECT RCNF_ST_CHAVEXML
                FROM ELCO.est_recebimentonfe@ELCO
                WHERE TRUNC(RCB_DT_DOCUMENTO) = TO_DATE(:data, 'DD/MM/YYYY')
                AND RCNF_ST_CHAVEXML IS NOT NULL
            `;
            const resultado = await this.oracleService.executeQuery(query, [data]);
            const chavesValidas = resultado
                .map((row) => row[0])
                .filter((chave) => chave && chave.length === 44 && /^\d{44}$/.test(chave));
            console.log(`[VnfService] Encontradas ${chavesValidas.length} chaves de acesso válidas no Oracle para a data ${data}`);
            const chavesNaoProcessadas = [];
            for (const chave of chavesValidas) {
                const jaProcessada = await this.verificarChaveJaProcessada(chave);
                if (!jaProcessada) {
                    chavesNaoProcessadas.push(chave);
                }
            }
            const chavesProcessadas = chavesValidas.length - chavesNaoProcessadas.length;
            console.log(`[VnfService] ${chavesProcessadas} chaves já foram processadas anteriormente`);
            console.log(`[VnfService] ${chavesNaoProcessadas.length} chaves novas para processar`);
            return {
                chavesNovas: chavesNaoProcessadas,
                totalChaves: chavesValidas.length,
                chavesJaProcessadas: chavesProcessadas,
            };
        }
        catch (error) {
            console.error('[VnfService] Erro ao buscar chaves de acesso no Oracle:', error);
            throw new Error(`Erro ao consultar Oracle: ${error.message}`);
        }
    }
    async processarLoteXmlsPorData(data) {
        try {
            console.log(`[VnfService] Iniciando processamento em lote para a data: ${data}`);
            const chavesAcesso = await this.buscarChavesAcessoOracle(data);
            if (chavesAcesso.totalChaves === 0) {
                return {
                    success: true,
                    message: `Nenhuma chave de acesso encontrada para a data ${data}`,
                    totalProcessados: 0,
                    sucessos: 0,
                    erros: 0,
                    detalhes: [],
                };
            }
            console.log(`[VnfService] Processando ${chavesAcesso.totalChaves} chaves de acesso`);
            const detalhes = [];
            let sucessos = 0;
            let erros = 0;
            for (let i = 0; i < chavesAcesso.chavesNovas.length; i++) {
                const chaveAcesso = chavesAcesso.chavesNovas[i];
                try {
                    console.log(`[VnfService] Processando ${i + 1}/${chavesAcesso.totalChaves}: ${chaveAcesso}`);
                    if (i > 0) {
                        const baseDelay = 5000;
                        const progressiveDelay = Math.floor(i / 10) * 2000;
                        const totalDelay = baseDelay + progressiveDelay;
                        console.log(`[VnfService] ⏳ Aguardando ${totalDelay / 1000}s antes da próxima consulta`);
                        await this.delay(totalDelay);
                    }
                    const resultado = await this.consultarXmlPorChave(chaveAcesso);
                    if (resultado.success) {
                        sucessos++;
                        let temErros = false;
                        let quantidadeErros = 0;
                        const statusConsulta = 'sucesso';
                        let observacoes = '';
                        if (!resultado.numeroPedido) {
                            await this.criarErroVnf(resultado.notaFiscal?.numeroNota || 'N/A', chaveAcesso, 'Número do pedido não encontrado no XML', resultado.notaFiscal);
                            temErros = true;
                            quantidadeErros++;
                            observacoes = 'Número do pedido não encontrado no XML';
                        }
                        else if (resultado.dadosPedido &&
                            !resultado.dadosPedido.success &&
                            resultado.dadosPedido.error &&
                            resultado.dadosPedido.error.includes('não localizado no portal de compras')) {
                            await this.criarErroVnf(resultado.notaFiscal?.numeroNota || 'N/A', chaveAcesso, 'Número do pedido não encontrado no XML', resultado.notaFiscal);
                            temErros = true;
                            quantidadeErros++;
                            observacoes = `Pedido ${resultado.numeroPedido} não localizado no portal de compras`;
                        }
                        else if (resultado.dadosPedido && resultado.dadosPedido.error) {
                            temErros = true;
                            quantidadeErros = 1;
                            observacoes =
                                'Divergências encontradas na comparação pedido x nota fiscal';
                        }
                        else {
                            observacoes = 'Processado com sucesso sem divergências';
                        }
                        await this.salvarConsultaProcessada(chaveAcesso, resultado, statusConsulta, observacoes, new Date(data.split('/').reverse().join('-')));
                        detalhes.push({
                            chaveAcesso,
                            numeroNota: resultado.notaFiscal?.numeroNota,
                            status: 'sucesso',
                            message: temErros
                                ? quantidadeErros > 0
                                    ? 'Processado com erros registrados na tabela'
                                    : 'Processado com divergências encontradas'
                                : 'Processado com sucesso',
                            temErros,
                            quantidadeErros,
                        });
                        console.log(`[VnfService] ✅ Sucesso: ${chaveAcesso} - Nota: ${resultado.notaFiscal?.numeroNota}${temErros ? ' (com erros)' : ''}`);
                    }
                    else {
                        erros++;
                        let statusConsulta = 'erro';
                        let observacoes = resultado.message || 'Erro desconhecido';
                        if (resultado.message &&
                            (resultado.message.toLowerCase().includes('consumo indevido') ||
                                resultado.message.toLowerCase().includes('limite') ||
                                resultado.message.toLowerCase().includes('ultrapassou'))) {
                            statusConsulta = 'limite_atingido';
                            observacoes = 'Limite de consultas SEFAZ atingido';
                        }
                        await this.salvarConsultaProcessada(chaveAcesso, null, statusConsulta, observacoes, new Date(data.split('/').reverse().join('-')));
                        detalhes.push({
                            chaveAcesso,
                            status: 'erro',
                            message: resultado.message || 'Erro desconhecido',
                        });
                        console.log(`[VnfService] ❌ Erro: ${chaveAcesso} - ${resultado.message}`);
                        if (statusConsulta === 'limite_atingido') {
                            console.log(`[VnfService] 🚫 LIMITE DO SEFAZ ATINGIDO! Interrompendo processamento.`);
                            for (let j = i + 1; j < chavesAcesso.chavesNovas.length; j++) {
                                await this.salvarConsultaProcessada(chavesAcesso.chavesNovas[j], null, 'limite_atingido', 'Não processado devido ao limite do SEFAZ', new Date(data.split('/').reverse().join('-')));
                            }
                            break;
                        }
                    }
                }
                catch (error) {
                    erros++;
                    const isLimiteError = this.verificarErroLimiteSefaz(error);
                    const statusConsulta = isLimiteError
                        ? 'limite_atingido'
                        : 'erro_sefaz';
                    const observacoes = isLimiteError
                        ? 'Limite de consultas SEFAZ atingido'
                        : `Erro interno: ${error.message}`;
                    await this.salvarConsultaProcessada(chaveAcesso, null, statusConsulta, observacoes, new Date(data.split('/').reverse().join('-')));
                    detalhes.push({
                        chaveAcesso,
                        status: 'erro',
                        message: isLimiteError
                            ? 'Limite de consultas SEFAZ atingido'
                            : `Erro interno: ${error.message}`,
                    });
                    console.error(`[VnfService] ❌ Erro interno ao processar ${chaveAcesso}:`, error);
                    if (isLimiteError) {
                        console.log(`[VnfService] 🚫 LIMITE DO SEFAZ ATINGIDO! Interrompendo processamento.`);
                        for (let j = i + 1; j < chavesAcesso.chavesNovas.length; j++) {
                            await this.salvarConsultaProcessada(chavesAcesso.chavesNovas[j], null, 'limite_atingido', 'Não processado devido ao limite do SEFAZ', new Date(data.split('/').reverse().join('-')));
                        }
                        break;
                    }
                }
            }
            let message = '';
            let limiteSefazAtingido = false;
            const errosLimite = detalhes.filter((d) => d.message &&
                (d.message.includes('Limite de consultas SEFAZ') ||
                    d.message.includes('Consumo Indevido')));
            if (errosLimite.length > 0) {
                limiteSefazAtingido = true;
                message = `⚠️ LIMITE DO SEFAZ ATINGIDO! Processamento interrompido. ${sucessos} sucessos, ${erros} erros de ${chavesAcesso.chavesNovas.length} chaves novas processadas. ${chavesAcesso.chavesJaProcessadas} chaves já foram processadas anteriormente.`;
            }
            else if (chavesAcesso.chavesJaProcessadas > 0) {
                message = `Processamento concluído: ${sucessos} sucessos, ${erros} erros de ${chavesAcesso.chavesNovas.length} chaves novas processadas. ${chavesAcesso.chavesJaProcessadas} chaves já foram processadas anteriormente (total: ${chavesAcesso.totalChaves}).`;
            }
            else {
                message = `Processamento concluído: ${sucessos} sucessos, ${erros} erros de ${chavesAcesso.totalChaves} chaves processadas.`;
            }
            console.log(`[VnfService] ${message}`);
            return {
                success: true,
                message,
                totalProcessados: chavesAcesso.totalChaves,
                chavesNovas: chavesAcesso.chavesNovas.length,
                chavesJaProcessadas: chavesAcesso.chavesJaProcessadas,
                limiteSefazAtingido,
                sucessos,
                erros,
                detalhes,
            };
        }
        catch (error) {
            console.error('[VnfService] Erro no processamento em lote:', error);
            return {
                success: false,
                message: `Erro no processamento em lote: ${error.message}`,
                totalProcessados: 0,
                sucessos: 0,
                erros: 0,
                detalhes: [],
            };
        }
    }
    delay(ms) {
        return new Promise((resolve) => setTimeout(resolve, ms));
    }
    async criarErroVnf(numeroNota, chaveAcesso, tipoErro, notaFiscal) {
        try {
            let nomeFornecedor = null;
            const clienteExternalCode = null;
            let fornecedorEmail = null;
            if (notaFiscal?.emitente?.nome) {
                nomeFornecedor = notaFiscal.emitente.nome;
                if (nomeFornecedor) {
                    fornecedorEmail = await this.buscarEmailFornecedor(nomeFornecedor);
                }
            }
            const erro = this.vnfErrorRepository.create({
                numeroNota: numeroNota || chaveAcesso.substring(25, 34),
                clienteExternalCode: clienteExternalCode || undefined,
                nomeFornecedor: nomeFornecedor || undefined,
                tipoErro: tipoErro,
                compradorEmail: undefined,
                fornecedorEmail: fornecedorEmail || undefined,
                dataEmailEnviado: undefined,
                podeRecusar: true,
            });
            await this.vnfErrorRepository.save(erro);
            console.log(`[VnfService] 📝 Erro VNF criado: ${tipoErro} - Nota: ${numeroNota}`);
        }
        catch (error) {
            console.error(`[VnfService] Erro ao criar erro VNF:`, error);
        }
    }
    async verificarChaveJaProcessada(chaveAcesso) {
        try {
            return await this.vnfConsultaProcessadaRepository.findOne({
                where: { chaveAcesso },
            });
        }
        catch (error) {
            console.error(`[VnfService] Erro ao verificar chave processada:`, error);
            return null;
        }
    }
    async salvarConsultaProcessada(chaveAcesso, resultado, statusConsulta, observacoes, dataDocumento) {
        try {
            const existente = await this.verificarChaveJaProcessada(chaveAcesso);
            if (existente) {
                existente.statusConsulta = statusConsulta;
                existente.observacoes = observacoes || existente.observacoes;
                if (resultado?.notaFiscal) {
                    existente.numeroNota =
                        resultado.notaFiscal.numeroNota || existente.numeroNota;
                    existente.nomeFornecedor =
                        resultado.notaFiscal.emitente?.nome || existente.nomeFornecedor;
                }
                if (resultado?.numeroPedido) {
                    existente.numeroPedido = resultado.numeroPedido;
                }
                if (resultado?.dadosPedido?.error) {
                    existente.temDivergencias = true;
                }
                await this.vnfConsultaProcessadaRepository.save(existente);
            }
            else {
                const consulta = this.vnfConsultaProcessadaRepository.create({
                    chaveAcesso,
                    numeroNota: resultado?.notaFiscal?.numeroNota,
                    nomeFornecedor: resultado?.notaFiscal?.emitente?.nome,
                    numeroPedido: resultado?.numeroPedido,
                    statusConsulta,
                    temDivergencias: resultado?.dadosPedido?.error ? true : false,
                    observacoes,
                    dataDocumento: dataDocumento || new Date(),
                });
                await this.vnfConsultaProcessadaRepository.save(consulta);
            }
            console.log(`[VnfService] 💾 Consulta salva: ${chaveAcesso} - Status: ${statusConsulta}`);
        }
        catch (error) {
            console.error(`[VnfService] Erro ao salvar consulta processada:`, error);
        }
    }
    verificarErroLimiteSefaz(error) {
        const errorMessage = error?.message?.toLowerCase() || '';
        const responseData = error?.response?.data?.toString?.()?.toLowerCase() || '';
        return (errorMessage.includes('consumo indevido') ||
            errorMessage.includes('limite') ||
            errorMessage.includes('ultrapassou') ||
            responseData.includes('consumo indevido') ||
            responseData.includes('limite') ||
            responseData.includes('ultrapassou'));
    }
    async listarConsultasProcessadas(data) {
        try {
            const whereCondition = {};
            if (data) {
                const dateParts = data.split('/');
                if (dateParts.length === 3) {
                    const dataFormatada = `${dateParts[2]}-${dateParts[1]}-${dateParts[0]}`;
                    whereCondition.dataDocumento = dataFormatada;
                }
            }
            const consultas = await this.vnfConsultaProcessadaRepository.find({
                where: whereCondition,
                order: {
                    dataProcessamento: 'DESC',
                },
            });
            return consultas;
        }
        catch (error) {
            console.error('[VnfService] Erro ao listar consultas processadas:', error);
            throw new Error(`Erro ao listar consultas processadas: ${error.message}`);
        }
    }
    async processarXmlsAutomaticamente() {
        if (this.isProcessingCron) {
            this.logger.warn('⏳ Processamento anterior ainda em execução. Pulando esta execução.');
            return;
        }
        this.isProcessingCron = true;
        this.lastCronExecution = new Date();
        this.logger.log('🤖 Iniciando processamento automático de XMLs...');
        try {
            const timeout = new Promise((_, reject) => setTimeout(() => reject(new Error('Timeout no processamento automático')), 14 * 60 * 1000));
            const processamento = async () => {
                const agora = new Date();
                const dia = agora.getDate().toString().padStart(2, '0');
                const mes = (agora.getMonth() + 1).toString().padStart(2, '0');
                const ano = agora.getFullYear();
                const dataHoje = `${dia}/${mes}/${ano}`;
                this.logger.log(`📅 Processando XMLs para a data: ${dataHoje}`);
                const resultado = await this.processarLoteXmlsPorData(dataHoje);
                if (resultado.success) {
                    if (resultado.limiteSefazAtingido) {
                        this.logger.warn(`⚠️ ${resultado.message}`);
                    }
                    else if (resultado.chavesJaProcessadas &&
                        resultado.chavesJaProcessadas > 0) {
                        this.logger.log(`📊 ${resultado.message}`);
                    }
                    else if (resultado.totalProcessados === 0) {
                        this.logger.log(`📭 Nenhuma chave nova encontrada para ${dataHoje}`);
                    }
                    else {
                        this.logger.log(`✅ ${resultado.message}`);
                    }
                    if (resultado.totalProcessados > 0) {
                        this.logger.log(`📈 Estatísticas: ${resultado.sucessos} sucessos, ${resultado.erros} erros, ${resultado.chavesJaProcessadas || 0} já processadas`);
                        const errosLimite = resultado.detalhes.filter((d) => d.message && d.message.includes('Limite de consultas SEFAZ')).length;
                        if (errosLimite > 0) {
                            this.logger.warn(`🚫 ${errosLimite} chave(s) não processada(s) devido ao limite do SEFAZ`);
                        }
                    }
                }
                else {
                    this.logger.error(`❌ Erro no processamento automático: ${resultado.message}`);
                }
                return resultado;
            };
            await Promise.race([processamento(), timeout]);
        }
        catch (error) {
            if (error.message === 'Timeout no processamento automático') {
                this.logger.error('⏰ Timeout no processamento automático após 14 minutos');
            }
            else {
                this.logger.error(`❌ Erro no processamento automático: ${error.message}`);
            }
        }
        finally {
            this.isProcessingCron = false;
            const duracao = Date.now() - this.lastCronExecution.getTime();
            this.logger.log(`🏁 Processamento automático finalizado em ${Math.round(duracao / 1000)}s`);
        }
    }
    getStatusProcessamentoAutomatico() {
        const agora = new Date();
        const minutos = agora.getMinutes();
        const proximoMultiplo = Math.ceil(minutos / 15) * 15;
        const proximaExecucao = new Date(agora);
        if (proximoMultiplo >= 60) {
            proximaExecucao.setHours(proximaExecucao.getHours() + 1);
            proximaExecucao.setMinutes(0);
        }
        else {
            proximaExecucao.setMinutes(proximoMultiplo);
        }
        proximaExecucao.setSeconds(0);
        proximaExecucao.setMilliseconds(0);
        return {
            isProcessing: this.isProcessingCron,
            lastExecution: this.lastCronExecution,
            nextExecution: proximaExecucao,
        };
    }
};
exports.VnfService = VnfService;
__decorate([
    (0, schedule_1.Cron)('0 */15 * * * *'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], VnfService.prototype, "processarXmlsAutomaticamente", null);
exports.VnfService = VnfService = VnfService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(invoice_entity_1.Invoice)),
    __param(1, (0, typeorm_1.InjectRepository)(invoice_item_entity_1.InvoiceItem)),
    __param(2, (0, typeorm_1.InjectRepository)(product_entity_1.Product)),
    __param(3, (0, typeorm_1.InjectRepository)(vnf_error_entity_1.VnfError)),
    __param(4, (0, typeorm_1.InjectRepository)(vnf_consulta_processada_entity_1.VnfConsultaProcessada)),
    __param(5, (0, typeorm_1.InjectRepository)(separation_order_entity_1.SeparationOrder)),
    __param(6, (0, typeorm_1.InjectRepository)(romaneio_entity_1.Romaneio)),
    __param(7, (0, typeorm_1.InjectRepository)(romaneio_order_entity_1.RomaneioOrder)),
    __param(8, (0, typeorm_1.InjectRepository)(supplier_email_entity_1.SupplierEmail)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        email_service_1.EmailService,
        oracle_service_1.OracleService])
], VnfService);
//# sourceMappingURL=vnf.service.js.map