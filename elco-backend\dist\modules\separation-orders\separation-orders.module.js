"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SeparationOrderModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const separation_orders_service_1 = require("./separation-orders.service");
const separation_orders_controller_1 = require("./separation-orders.controller");
const separation_order_entity_1 = require("./entity/separation-order.entity");
let SeparationOrderModule = class SeparationOrderModule {
};
exports.SeparationOrderModule = SeparationOrderModule;
exports.SeparationOrderModule = SeparationOrderModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([separation_order_entity_1.SeparationOrder])],
        providers: [separation_orders_service_1.SeparationOrderService],
        controllers: [separation_orders_controller_1.SeparationOrderController],
        exports: [separation_orders_service_1.SeparationOrderService],
    })
], SeparationOrderModule);
//# sourceMappingURL=separation-orders.module.js.map