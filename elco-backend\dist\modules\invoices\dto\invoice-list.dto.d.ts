export declare class InvoiceItemDto {
    id: number;
}
export declare class InvoiceErrorDto {
    id: number;
    errorMessage: string;
    data: Date;
    xmlEnviado?: string;
    soapEnvelope?: string;
    idRomaneio: number;
    idOrdem: number;
}
export declare class InvoiceDto {
    id: number;
    filInCodigo: number;
    notInCodigo: number;
    notInNumero: string;
    tpdInCodigo: number;
    notDtEmissao: Date;
    notHrHoraemissao: string;
    notStChaveacesso: string;
    notStCgc: string;
    notStIncrestadual: string;
    notStMunicipio: string;
    notStUf: string;
    items: InvoiceItemDto[];
    errors: InvoiceErrorDto[];
    idRomaneio: number;
    idOrdem: number;
}
export declare class InvoiceListResponseDto {
    invoices: InvoiceDto[];
    total: number;
}
export declare class InvoiceErrorListResponseDto {
    errors: InvoiceErrorDto[];
    total: number;
}
