"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.seedRoles = seedRoles;
async function seedRoles(repo) {
    const roles = [
        { key: 'ADMIN', name: '<PERSON>ministra<PERSON>' },
        { key: 'FISCAL', name: '<PERSON>sca<PERSON>' },
        { key: 'COORD', name: '<PERSON>ordena<PERSON>' },
        { key: 'OPERADOR_OBRA', name: 'Operador de Obra' },
        { key: 'ESTOQUISTA', name: '<PERSON><PERSON>quist<PERSON>' },
    ];
    for (const role of roles) {
        const exists = await repo.findOne({ where: { key: role.key } });
        if (!exists) {
            await repo.save(repo.create(role));
        }
    }
}
//# sourceMappingURL=role.seeder.js.map