import { SeparationOrderService } from './separation-orders.service';
export declare class SeparationOrderController {
    private readonly service;
    constructor(service: SeparationOrderService);
    getAll(page?: number, limit?: number, search?: string, status?: string, depositorName?: string, clientFilter?: string): Promise<{
        orders: import("./entity/separation-order.entity").SeparationOrder[];
        total: number;
        page: number;
        limit: number;
        totalPages: number;
    }>;
    getStats(): Promise<{
        totalOrders: number;
        completedCount: number;
        linkedCount: number;
        pendingCount: number;
        errorCount: number;
        completedPercentage: number;
        linkedPercentage: number;
        pendingPercentage: number;
    }>;
    getFilterOptions(): Promise<{
        statuses: any[];
        depositors: any[];
        clients: string[];
    }>;
    fetchExternalProducts(internalCode: string): Promise<any[]>;
    linkOrder(internalCode: string, body: {
        tipo: 'MATRIZ' | 'FILIAL';
    }): Promise<import("./entity/separation-order.entity").SeparationOrder>;
    syncOrderVolume(body: {
        orderId: number;
        volume: number;
    }): Promise<void>;
}
