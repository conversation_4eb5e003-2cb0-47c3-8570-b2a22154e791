export interface Rateio {
  codigoCentroCusto: string;
  codigoContaContabil: string;
  percentualRateio: number;
}

export interface Entrega {
  dataDeEntrega: string;
  diasParaEntrega: number | null;
  quantidadeEntrega: number;
  parcela: number;
}

export interface ItemPedido {
  sequencial: number;
  codigoFamiliaDeProduto: string;
  codigoUnidadeMedida: string;
  codigoProduto: string;
  descricaoBreveDoProduto: string;
  descricaoDetalhadaDoProduto: string;
  baseDeCalculoICMS: number | null;
  aliquitaICMS: number | null;
  aliquitaIPI: number | null;
  aliquotaPIS: number | null;
  aliquotaCOFINS: number | null;
  quantidade: number;
  valorUnitario: number;
  valorTotal: number;
  valorLiquido: number;
  situacaoDoItem: string;
  aplicacao: string;
  iva: string;
  nmc: string;
  contrato: string;
  sequencialDoItemDeContrato: number | null;
  requisicao: string;
  loginRequisitante: string;
  tipoRequisicaoERP: string;
  sequencialDoItemDeRequisicao: number;
  valorDaUltimaCompra: number;
  valorDaUltimaCompraParaPlanta: number;
  valorLiquidoDaUltimaCompra: number;
  valorLiquidoDaUltimaCompraParaPlanta: number;
  valorDoDesconto: number;
  cotacaoDeReferencia: string;
  sequencialDoItemDaCotacao: number | null;
  utilizacao: string;
  observacoes: string[];
  rateio: Rateio[];
  entrega: Entrega[];
}

export interface PedidoPortal {
  cliente: string;
  pedidoEproc: number | null;
  pedidoERP: string;
  condicaoPagamento: string;
  formaPagamento: string;
  codigoPlanta: string;
  nomePlanta: string;
  cnpjPlanta: string;
  codigoFornecedor: string;
  cnpjFornecedor: string;
  nomeFornecedor: string;
  tipoPedidoERP: string;
  codigoTransportadora: string;
  nomeTransportadora: string;
  codigoFrete: string;
  valorFrete: number;
  dataDeCriacao: string;
  dataDeEmissao: string;
  loginComprador: string;
  situacaoPedido: string;
  enderecoEntrega: string;
  enderecoEntregaNumero: string;
  enderecoEntregaComplemento: string;
  enderecoEntregaCep: string;
  enderecoEntregaCidade: string;
  enderecoEntregaBairro: string;
  enderecoEntregaEstado: string;
  enderecoEntregaPais: string;
  codigoMoeda: string;
  valorTotalComFrete: number;
  tipoDeCompra: string;
  observacoes: string[];
  itens: ItemPedido[];
  tags: string[];
  sDsMensagemRetorno: string | null;
}
