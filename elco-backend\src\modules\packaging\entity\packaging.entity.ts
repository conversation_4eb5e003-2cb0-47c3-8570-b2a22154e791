import {
  <PERSON><PERSON>ty,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  DeleteDateColumn,
} from 'typeorm';

@Entity('packagings')
export class Packaging {
  @PrimaryGeneratedColumn()
  id: string;

  @Column()
  name: string;

  @Column('float')
  weight: number;

  @Column('float')
  height: number;

  @Column('float')
  length: number;

  @Column('float')
  width: number;

  @Column('float')
  volume: number;

  @Column()
  type: string;

  @CreateDateColumn()
  createdAt: Date;

  @DeleteDateColumn({ name: 'deleted_at' })
  deleted_at?: Date;
}
