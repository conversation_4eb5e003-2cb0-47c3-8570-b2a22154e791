"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InvoiceItem = void 0;
const typeorm_1 = require("typeorm");
const invoice_entity_1 = require("./invoice.entity");
let InvoiceItem = class InvoiceItem {
    id;
    invoiceId;
    invoice;
    notInCodigo;
    notInNumero;
    itnStDescricao;
    itnReValorunitario;
    itnStNcmExtenso;
    itnReValortotal;
    itnReQuantidade;
    stpStCstpis;
    stcStCstcofins;
    idRomaneio;
    idOrdem;
    createdAt;
    updatedAt;
    deletedAt;
};
exports.InvoiceItem = InvoiceItem;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], InvoiceItem.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'invoice_id' }),
    __metadata("design:type", Number)
], InvoiceItem.prototype, "invoiceId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => invoice_entity_1.Invoice, invoice => invoice.items),
    (0, typeorm_1.JoinColumn)({ name: 'invoice_id' }),
    __metadata("design:type", invoice_entity_1.Invoice)
], InvoiceItem.prototype, "invoice", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'not_in_codigo' }),
    __metadata("design:type", Number)
], InvoiceItem.prototype, "notInCodigo", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'not_in_numero' }),
    __metadata("design:type", Number)
], InvoiceItem.prototype, "notInNumero", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'itn_st_descricao' }),
    __metadata("design:type", String)
], InvoiceItem.prototype, "itnStDescricao", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'itn_re_valorunitario', type: 'decimal', precision: 10, scale: 2 }),
    __metadata("design:type", Number)
], InvoiceItem.prototype, "itnReValorunitario", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'itn_st_ncm_extenso' }),
    __metadata("design:type", String)
], InvoiceItem.prototype, "itnStNcmExtenso", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'itn_re_valortotal', type: 'decimal', precision: 10, scale: 2 }),
    __metadata("design:type", Number)
], InvoiceItem.prototype, "itnReValortotal", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'itn_re_quantidade' }),
    __metadata("design:type", Number)
], InvoiceItem.prototype, "itnReQuantidade", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'stp_st_cstpis' }),
    __metadata("design:type", String)
], InvoiceItem.prototype, "stpStCstpis", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'stc_st_cstcofins' }),
    __metadata("design:type", String)
], InvoiceItem.prototype, "stcStCstcofins", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'id_romaneio' }),
    __metadata("design:type", Number)
], InvoiceItem.prototype, "idRomaneio", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'id_ordem' }),
    __metadata("design:type", Number)
], InvoiceItem.prototype, "idOrdem", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", Date)
], InvoiceItem.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", Date)
], InvoiceItem.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.DeleteDateColumn)({ name: 'deleted_at' }),
    __metadata("design:type", Date)
], InvoiceItem.prototype, "deletedAt", void 0);
exports.InvoiceItem = InvoiceItem = __decorate([
    (0, typeorm_1.Entity)('invoice_items')
], InvoiceItem);
//# sourceMappingURL=invoice-item.entity.js.map