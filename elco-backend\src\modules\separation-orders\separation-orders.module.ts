import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SeparationOrderService } from './separation-orders.service';
import { SeparationOrderController } from './separation-orders.controller';
import { SeparationOrder } from './entity/separation-order.entity';

@Module({
  imports: [TypeOrmModule.forFeature([SeparationOrder])],
  providers: [SeparationOrderService],
  controllers: [SeparationOrderController],
  exports: [SeparationOrderService],
})
export class SeparationOrderModule {}
