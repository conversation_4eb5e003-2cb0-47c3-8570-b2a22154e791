import React from 'react';
import { cn } from '@/lib/utils';

type StatusType = 'PENDING' | 'LINKED' | 'ROMANIO' | 'ERROR' | 'ISSUED';

interface StatusBadgeProps {
  status: StatusType;
  className?: string;
}

const statusConfig = {
  PENDING: { 
    label: 'Pendente', 
    className: 'bg-amber-50 text-amber-700 border border-amber-200 shadow-sm',
    dotColor: 'bg-amber-400'
  },
  LINKED: { 
    label: 'Vinculado', 
    className: 'bg-blue-50 text-blue-700 border border-blue-200 shadow-sm',
    dotColor: 'bg-blue-400'
  },
  ROMANIO: { 
    label: 'Aguardando Notas', 
    className: 'bg-green-50 text-green-700 border border-green-200 shadow-sm',
    dotColor: 'bg-green-400'
  },
  ERROR: { 
    label: 'Erro', 
    className: 'bg-red-50 text-red-700 border border-red-200 shadow-sm',
    dotColor: 'bg-red-400'
  },
  ISSUED: {
    label: 'Emitida',
    className: 'bg-green-50 text-green-700 border border-green-200 shadow-sm',
    dotColor: 'bg-green-400'
  }
};

const StatusBadge: React.FC<StatusBadgeProps> = ({ status, className }) => {
  const config = statusConfig[status];

  return (
    <span
      className={cn(
        'inline-flex items-center gap-2 rounded-full px-3 py-1.5 text-xs font-medium transition-all duration-200 hover:shadow-md',
        config.className,
        className
      )}
    >
      <div className={cn('w-2 h-2 rounded-full', config.dotColor)} />
      {config.label}
    </span>
  );
};

export default StatusBadge;
