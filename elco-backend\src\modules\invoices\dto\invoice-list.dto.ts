import { ApiProperty } from '@nestjs/swagger';

export class InvoiceItemDto {
  @ApiProperty()
  id: number;
}

export class InvoiceErrorDto {
  @ApiProperty()
  id: number;

  @ApiProperty()
  errorMessage: string;

  @ApiProperty()
  data: Date;

  @ApiProperty({ required: false })
  xmlEnviado?: string;

  @ApiProperty({ required: false })
  soapEnvelope?: string;

  @ApiProperty()
  idRomaneio: number;

  @ApiProperty()
  idOrdem: number;
}

export class InvoiceDto {
  @ApiProperty()
  id: number;

  @ApiProperty()
  filInCodigo: number;

  @ApiProperty()
  notInCodigo: number;

  @ApiProperty()
  notInNumero: string;

  @ApiProperty()
  tpdInCodigo: number;

  @ApiProperty()
  notDtEmissao: Date;

  @ApiProperty()
  notHrHoraemissao: string;

  @ApiProperty()
  notStChaveacesso: string;

  @ApiProperty()
  notStCgc: string;

  @ApiProperty()
  notStIncrestadual: string;

  @ApiProperty()
  notStMunicipio: string;

  @ApiProperty()
  notStUf: string;

  @ApiProperty({ type: [InvoiceItemDto] })
  items: InvoiceItemDto[];

  @ApiProperty({ type: [InvoiceErrorDto] })
  errors: InvoiceErrorDto[];

  @ApiProperty()
  idRomaneio: number;

  @ApiProperty()
  idOrdem: number;
}

export class InvoiceListResponseDto {
  @ApiProperty({ type: [InvoiceDto] })
  invoices: InvoiceDto[];

  @ApiProperty()
  total: number;
}

export class InvoiceErrorListResponseDto {
  @ApiProperty({ type: [InvoiceErrorDto] })
  errors: InvoiceErrorDto[];

  @ApiProperty()
  total: number;
} 