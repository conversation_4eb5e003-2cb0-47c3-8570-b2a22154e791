"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RomaneioOrdersController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const romaneio_orders_service_1 = require("./romaneio-orders.service");
const create_romaneio_order_dto_1 = require("./dto/create-romaneio-order.dto");
const update_romaneio_order_dto_1 = require("./dto/update-romaneio-order.dto");
let RomaneioOrdersController = class RomaneioOrdersController {
    romaneioOrdersService;
    constructor(romaneioOrdersService) {
        this.romaneioOrdersService = romaneioOrdersService;
    }
    create(createRomaneioOrderDto) {
        return this.romaneioOrdersService.create(createRomaneioOrderDto);
    }
    findAll() {
        return this.romaneioOrdersService.findAll();
    }
    findOne(id) {
        return this.romaneioOrdersService.findOne(+id);
    }
    update(id, updateRomaneioOrderDto) {
        return this.romaneioOrdersService.update(+id, updateRomaneioOrderDto);
    }
    remove(id) {
        return this.romaneioOrdersService.remove(+id);
    }
};
exports.RomaneioOrdersController = RomaneioOrdersController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Criar novo pedido de romaneio',
        description: 'Cria um novo pedido vinculado a um romaneio.',
    }),
    (0, swagger_1.ApiBody)({
        type: create_romaneio_order_dto_1.CreateRomaneioOrderDto,
        description: 'Dados do novo pedido de romaneio',
        examples: {
            create: {
                summary: 'Exemplo de pedido',
                value: {
                    romaneioId: 1,
                    orderNumber: 'PED-001',
                    clientName: 'Cliente ABC Ltda',
                    products: [
                        {
                            productId: 1,
                            quantity: 10,
                            unitPrice: 25.5,
                        },
                    ],
                    observations: 'Entrega urgente',
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Pedido de romaneio criado com sucesso',
        schema: {
            type: 'object',
            properties: {
                id: { type: 'number', example: 1 },
                romaneioId: { type: 'number', example: 1 },
                orderNumber: { type: 'string', example: 'PED-001' },
                clientName: { type: 'string', example: 'Cliente ABC Ltda' },
                status: { type: 'string', example: 'PENDING' },
                totalValue: { type: 'number', example: 255.0 },
                createdAt: { type: 'string', format: 'date-time' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dados inválidos' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_romaneio_order_dto_1.CreateRomaneioOrderDto]),
    __metadata("design:returntype", void 0)
], RomaneioOrdersController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Listar todos os pedidos de romaneio',
        description: 'Retorna uma lista com todos os pedidos de romaneio cadastrados.',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de pedidos de romaneio',
        schema: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    id: { type: 'number', example: 1 },
                    romaneioId: { type: 'number', example: 1 },
                    orderNumber: { type: 'string', example: 'PED-001' },
                    clientName: { type: 'string', example: 'Cliente ABC Ltda' },
                    status: { type: 'string', example: 'PENDING' },
                    totalValue: { type: 'number', example: 255.0 },
                    romaneio: {
                        type: 'object',
                        properties: {
                            id: { type: 'number', example: 1 },
                            externalCode: { type: 'string', example: 'ROM001' },
                        },
                    },
                    createdAt: { type: 'string', format: 'date-time' },
                },
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], RomaneioOrdersController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Obter pedido de romaneio por ID',
        description: 'Retorna os detalhes completos de um pedido de romaneio específico.',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID do pedido de romaneio',
        type: 'string',
        example: '1',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Dados do pedido de romaneio',
        schema: {
            type: 'object',
            properties: {
                id: { type: 'number', example: 1 },
                romaneioId: { type: 'number', example: 1 },
                orderNumber: { type: 'string', example: 'PED-001' },
                clientName: { type: 'string', example: 'Cliente ABC Ltda' },
                status: { type: 'string', example: 'PENDING' },
                totalValue: { type: 'number', example: 255.0 },
                observations: { type: 'string', example: 'Entrega urgente' },
                romaneio: { type: 'object' },
                products: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            productId: { type: 'number', example: 1 },
                            quantity: { type: 'number', example: 10 },
                            unitPrice: { type: 'number', example: 25.5 },
                            product: { type: 'object' },
                        },
                    },
                },
                createdAt: { type: 'string', format: 'date-time' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Pedido de romaneio não encontrado',
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], RomaneioOrdersController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Atualizar pedido de romaneio',
        description: 'Atualiza os dados de um pedido de romaneio existente.',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID do pedido de romaneio',
        type: 'string',
        example: '1',
    }),
    (0, swagger_1.ApiBody)({ type: update_romaneio_order_dto_1.UpdateRomaneioOrderDto }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Pedido de romaneio atualizado com sucesso',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Pedido de romaneio não encontrado',
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dados inválidos' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_romaneio_order_dto_1.UpdateRomaneioOrderDto]),
    __metadata("design:returntype", void 0)
], RomaneioOrdersController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Deletar pedido de romaneio',
        description: 'Remove um pedido de romaneio do sistema.',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID do pedido de romaneio',
        type: 'string',
        example: '1',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Pedido de romaneio removido com sucesso',
        schema: {
            type: 'object',
            properties: {
                message: {
                    type: 'string',
                    example: 'Pedido de romaneio removido com sucesso',
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Pedido de romaneio não encontrado',
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], RomaneioOrdersController.prototype, "remove", null);
exports.RomaneioOrdersController = RomaneioOrdersController = __decorate([
    (0, swagger_1.ApiTags)('Pedidos de Romaneio'),
    (0, common_1.Controller)('romaneio-orders'),
    __metadata("design:paramtypes", [romaneio_orders_service_1.RomaneioOrdersService])
], RomaneioOrdersController);
//# sourceMappingURL=romaneio-orders.controller.js.map