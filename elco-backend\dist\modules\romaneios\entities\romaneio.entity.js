"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Romaneio = void 0;
const typeorm_1 = require("typeorm");
let Romaneio = class Romaneio {
    id;
    dateIssue;
    address;
    carrierId;
    driverId;
    vehicleId;
    totalLength;
    totalWidth;
    totalHeight;
    totalWeight;
    totalVolume;
    mainPackagingId;
    secondaryPackagingId;
    userId;
    statusId;
    linking;
    observations;
    verifiedObservations;
    createdAt;
    updatedAt;
    deletedAt;
    romaneioOrders;
};
exports.Romaneio = Romaneio;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], Romaneio.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'date_issue',
        type: 'timestamp',
        nullable: false,
        default: () => 'CURRENT_TIMESTAMP',
    }),
    __metadata("design:type", Date)
], Romaneio.prototype, "dateIssue", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 500, nullable: false }),
    __metadata("design:type", String)
], Romaneio.prototype, "address", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'carrier_id', type: 'integer', nullable: false }),
    __metadata("design:type", Number)
], Romaneio.prototype, "carrierId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'driver_id', type: 'integer', nullable: false }),
    __metadata("design:type", Number)
], Romaneio.prototype, "driverId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'vehicle_id', type: 'integer', nullable: false }),
    __metadata("design:type", Number)
], Romaneio.prototype, "vehicleId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'total_length', type: 'integer', nullable: false }),
    __metadata("design:type", Number)
], Romaneio.prototype, "totalLength", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'total_width', type: 'integer', nullable: false }),
    __metadata("design:type", Number)
], Romaneio.prototype, "totalWidth", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'total_height', type: 'integer', nullable: false }),
    __metadata("design:type", Number)
], Romaneio.prototype, "totalHeight", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'total_weight', type: 'integer', nullable: false }),
    __metadata("design:type", Number)
], Romaneio.prototype, "totalWeight", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'total_volume', type: 'integer', nullable: false }),
    __metadata("design:type", Number)
], Romaneio.prototype, "totalVolume", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'main_packaging_id', type: 'integer', nullable: false }),
    __metadata("design:type", Number)
], Romaneio.prototype, "mainPackagingId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'secondary_packaging_id', type: 'integer', nullable: true }),
    __metadata("design:type", Number)
], Romaneio.prototype, "secondaryPackagingId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'user_id', type: 'integer', nullable: false }),
    __metadata("design:type", Number)
], Romaneio.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'status_id', type: 'integer', nullable: false, default: 0 }),
    __metadata("design:type", Number)
], Romaneio.prototype, "statusId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', nullable: true }),
    __metadata("design:type", String)
], Romaneio.prototype, "linking", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', nullable: true }),
    __metadata("design:type", String)
], Romaneio.prototype, "observations", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', name: 'verified_observations', nullable: true }),
    __metadata("design:type", String)
], Romaneio.prototype, "verifiedObservations", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'created_at',
        type: 'timestamp',
        nullable: false,
        default: () => 'CURRENT_TIMESTAMP',
    }),
    __metadata("design:type", Date)
], Romaneio.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'updated_at',
        type: 'timestamp',
        nullable: true,
    }),
    __metadata("design:type", Date)
], Romaneio.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'deleted_at',
        type: 'timestamp',
        nullable: true,
    }),
    __metadata("design:type", Date)
], Romaneio.prototype, "deletedAt", void 0);
__decorate([
    (0, typeorm_1.OneToMany)('RomaneioOrder', (romaneioOrder) => romaneioOrder.romaneioId),
    __metadata("design:type", Array)
], Romaneio.prototype, "romaneioOrders", void 0);
exports.Romaneio = Romaneio = __decorate([
    (0, typeorm_1.Entity)()
], Romaneio);
//# sourceMappingURL=romaneio.entity.js.map