
import React from 'react';
import { cn } from '@/lib/utils';
import { Separator } from './ui/separator';
import { Bell, CheckCircle2, Clock, AlertTriangle } from 'lucide-react';
import { ScrollArea } from './ui/scroll-area';
import { Button } from './ui/button';

interface Notification {
  id: string;
  title: string;
  description: string;
  time: string;
  read: boolean;
  type: 'info' | 'success' | 'warning' | 'error';
}

// Mock notifications
const notifications: Notification[] = [
  {
    id: '1',
    title: 'Novo pedido recebido',
    description: 'Pedido #123 foi adicionado e aguarda processamento',
    time: '5 minutos atrás',
    read: false,
    type: 'info'
  },
  {
    id: '2',
    title: 'Romaneio gerado',
    description: 'Romaneio do pedido #121 foi gerado com sucesso',
    time: '30 minutos atrás',
    read: false,
    type: 'success'
  },
  {
    id: '3',
    title: 'Atraso na entrega',
    description: 'Pedido #119 com atraso previsto de 2 horas',
    time: '1 hora atrás',
    read: true,
    type: 'warning'
  },
  {
    id: '4',
    title: 'Erro na expedição',
    description: 'Nota fiscal do pedido #118 com divergência',
    time: '3 horas atrás',
    read: true,
    type: 'error'
  },
  {
    id: '5',
    title: 'Pedido entregue',
    description: 'Pedido #115 foi entregue com sucesso',
    time: '5 horas atrás',
    read: true,
    type: 'success'
  }
];

const getNotificationIcon = (type: Notification['type']) => {
  switch (type) {
    case 'info':
      return <Bell className="h-5 w-5 text-blue-500" />;
    case 'success':
      return <CheckCircle2 className="h-5 w-5 text-green-500" />;
    case 'warning':
      return <Clock className="h-5 w-5 text-amber-500" />;
    case 'error':
      return <AlertTriangle className="h-5 w-5 text-red-500" />;
    default:
      return <Bell className="h-5 w-5 text-gray-500" />;
  }
};

const NotificationsPanel: React.FC = () => {
  return (
    <div>
      <ScrollArea className="h-[350px]">
        {notifications.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-[200px] text-muted-foreground">
            <Bell className="h-10 w-10 mb-2 opacity-20" />
            <p>Nenhuma notificação</p>
          </div>
        ) : (
          <div className="divide-y">
            {notifications.map((notification) => (
              <div 
                key={notification.id}
                className={cn(
                  "p-4 hover:bg-gray-50 transition-colors cursor-pointer",
                  !notification.read && "bg-elco-50"
                )}
              >
                <div className="flex gap-3">
                  <div className="flex-shrink-0 mt-1">
                    {getNotificationIcon(notification.type)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between">
                      <p className={cn("text-sm font-medium", !notification.read && "text-elco-800")}>
                        {notification.title}
                      </p>
                      <span className="text-xs text-muted-foreground ml-2 whitespace-nowrap">
                        {notification.time}
                      </span>
                    </div>
                    <p className="text-sm text-muted-foreground line-clamp-2 mt-1">
                      {notification.description}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </ScrollArea>
      <div className="p-2 border-t">
        <Button variant="outline" className="w-full text-sm h-9" size="sm">
          Ver todas as notificações
        </Button>
      </div>
    </div>
  );
};

export default NotificationsPanel;
