import { MegaService } from './mega.service';
export declare class MegaController {
    private readonly megaService;
    constructor(megaService: MegaService);
    consultarXml(chaveAcesso: string): Promise<any>;
    enviarNotaFiscal(dados: any, idOrdem: number, idRomaneio: number): Promise<any>;
    reenviarXML(body: {
        errorId: number;
        xmlEnviado: string;
    }): Promise<any>;
    consultarXmls(body: {
        chavesAcesso: string[];
    }): Promise<any>;
    consultarProdutosRomaneio(romaneioId: number): Promise<{
        success: boolean;
        produtos: any[];
    }>;
}
