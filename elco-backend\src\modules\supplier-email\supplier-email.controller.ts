import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Put,
  Delete,
  Query,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBody,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { SupplierEmailService } from './supplier-email.service';
import { SupplierEmail } from './entity/supplier-email.entity';

@ApiTags('Fornecedores')
@Controller('supplier-emails')
export class SupplierEmailController {
  constructor(private readonly supplierEmailService: SupplierEmailService) {}

  @Get()
  @ApiOperation({
    summary: 'Listar todos os emails de fornecedores',
    description:
      'Retorna uma lista com todos os emails de fornecedores cadastrados.',
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de emails de fornecedores',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'number', example: 1 },
          supplierCode: { type: 'number', example: 12345 },
          supplierName: { type: 'string', example: 'Fornecedor ABC Ltda' },
          email: { type: 'string', example: '<EMAIL>' },
          isActive: { type: 'boolean', example: true },
          createdAt: { type: 'string', format: 'date-time' },
        },
      },
    },
  })
  findAll(): Promise<SupplierEmail[]> {
    return this.supplierEmailService.findAll();
  }

  @Post()
  @ApiOperation({
    summary: 'Criar email de fornecedor (método genérico)',
    description:
      'Cria um novo registro de email de fornecedor usando dados parciais.',
  })
  @ApiBody({
    description: 'Dados parciais do fornecedor',
    schema: {
      type: 'object',
      properties: {
        supplierCode: { type: 'number', example: 12345 },
        supplierName: { type: 'string', example: 'Fornecedor XYZ' },
        email: { type: 'string', example: '<EMAIL>' },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Email de fornecedor criado com sucesso',
  })
  create(@Body() data: Partial<SupplierEmail>): Promise<SupplierEmail> {
    return this.supplierEmailService.create(data);
  }

  @Post('create-supplier-email')
  @ApiOperation({
    summary: 'Criar email de fornecedor',
    description: 'Cria um novo email de fornecedor no sistema.',
  })
  @ApiBody({
    description: 'Dados do fornecedor',
    schema: {
      type: 'object',
      properties: {
        supplierCode: { type: 'number', example: 12345 },
        supplierName: { type: 'string', example: 'Fornecedor ABC Ltda' },
        email: { type: 'string', example: '<EMAIL>' },
      },
      required: ['supplierCode', 'supplierName', 'email'],
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Email de fornecedor criado com sucesso',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'number', example: 1 },
        supplierCode: { type: 'number', example: 12345 },
        supplierName: { type: 'string', example: 'Fornecedor ABC Ltda' },
        email: { type: 'string', example: '<EMAIL>' },
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  async createSupplierEmail(
    @Body() body: { supplierCode: number; supplierName: string; email: string },
  ) {
    return await this.supplierEmailService.createSupplierEmail(body);
  }

  @Put(':id')
  @ApiOperation({
    summary: 'Atualizar email de fornecedor',
    description: 'Atualiza os dados de um email de fornecedor existente.',
  })
  @ApiParam({
    name: 'id',
    description: 'ID do email do fornecedor',
    type: 'string',
    example: '1',
  })
  @ApiBody({
    description: 'Novos dados do fornecedor',
    schema: {
      type: 'object',
      properties: {
        supplierCode: { type: 'number', example: 12345 },
        supplierName: { type: 'string', example: 'Fornecedor ABC Ltda' },
        email: { type: 'string', example: '<EMAIL>' },
      },
      required: ['supplierCode', 'supplierName', 'email'],
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Email de fornecedor atualizado com sucesso',
  })
  @ApiResponse({
    status: 404,
    description: 'Email de fornecedor não encontrado',
  })
  async updateSupplierEmail(
    @Param('id') id: string,
    @Body() body: { supplierCode: number; supplierName: string; email: string },
  ) {
    return await this.supplierEmailService.updateSupplierEmail(
      Number(id),
      body,
    );
  }

  @Delete(':id')
  @ApiOperation({
    summary: 'Deletar email de fornecedor (soft delete)',
    description: 'Desativa um email de fornecedor (soft delete).',
  })
  @ApiParam({
    name: 'id',
    description: 'ID do email do fornecedor',
    type: 'string',
    example: '1',
  })
  @ApiResponse({
    status: 200,
    description: 'Email de fornecedor desativado com sucesso',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Email de fornecedor não encontrado',
  })
  async removeSupplierEmail(@Param('id') id: string) {
    await this.supplierEmailService.removeSupplierEmail(Number(id));
    return { success: true };
  }

  @Delete('hard/:id')
  @ApiOperation({
    summary: 'Deletar email de fornecedor permanentemente',
    description: 'Remove permanentemente um email de fornecedor do sistema.',
  })
  @ApiParam({
    name: 'id',
    description: 'ID do email do fornecedor',
    type: 'string',
    example: '1',
  })
  @ApiResponse({
    status: 200,
    description: 'Email de fornecedor removido permanentemente',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Email de fornecedor não encontrado',
  })
  async hardDeleteSupplierEmail(@Param('id') id: string) {
    await this.supplierEmailService.hardDeleteSupplierEmail(Number(id));
    return { success: true };
  }

  @Get('projects')
  @ApiOperation({
    summary: 'Buscar projetos',
    description: 'Busca projetos disponíveis, com opção de filtro por texto.',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Termo de busca para filtrar projetos',
    example: 'projeto',
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de projetos encontrados',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'string', example: 'proj-001' },
          name: { type: 'string', example: 'Projeto Alpha' },
          description: { type: 'string', example: 'Descrição do projeto' },
        },
      },
    },
  })
  async fetchProjects(@Query('search') search?: string) {
    return await this.supplierEmailService.fetchProjects(search);
  }

  @Get('list')
  @ApiOperation({
    summary: 'Listar emails de fornecedores (método alternativo)',
    description: 'Retorna uma lista formatada dos emails de fornecedores.',
  })
  @ApiResponse({
    status: 200,
    description: 'Lista formatada de emails de fornecedores',
  })
  async listSupplierEmails() {
    return await this.supplierEmailService.listSupplierEmails();
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Obter email de fornecedor por ID',
    description: 'Retorna os detalhes de um email de fornecedor específico.',
  })
  @ApiParam({
    name: 'id',
    description: 'ID do email do fornecedor',
    type: 'string',
    example: '1',
  })
  @ApiResponse({
    status: 200,
    description: 'Dados do email do fornecedor',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'number', example: 1 },
        supplierCode: { type: 'number', example: 12345 },
        supplierName: { type: 'string', example: 'Fornecedor ABC Ltda' },
        email: { type: 'string', example: '<EMAIL>' },
        isActive: { type: 'boolean', example: true },
        createdAt: { type: 'string', format: 'date-time' },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Email de fornecedor não encontrado',
  })
  findOne(@Param('id') id: string): Promise<SupplierEmail | null> {
    return this.supplierEmailService.findOne(Number(id));
  }
}
