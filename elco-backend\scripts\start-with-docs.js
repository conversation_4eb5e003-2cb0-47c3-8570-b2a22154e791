#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  reset: '\x1b[0m',
  bright: '\x1b[1m'
};

function log(message, color = 'white') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function showBanner() {
  console.clear();
  log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━', 'cyan');
  log('                                        🚀 ELCO API SERVER 🚀                                            ', 'bright');
  log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━', 'cyan');
  log('');
  log('📖 Documentação Swagger: http://localhost:3000/api', 'green');
  log('🔗 API Base URL: http://localhost:3000', 'blue');
  log('📝 Documentação Completa: README_API.md', 'yellow');
  log('');
  log('🔐 ENDPOINTS PRINCIPAIS:', 'bright');
  log('   • POST /auth/login - Fazer login (obter token JWT)', 'white');
  log('   • GET  /users/me - Perfil do usuário logado', 'white');
  log('   • GET  /drivers/list - Listar motoristas', 'white');
  log('   • GET  /vehicles/list - Listar veículos', 'white');
  log('   • GET  /romaneios - Listar romaneios', 'white');
  log('   • GET  /products - Listar produtos', 'white');
  log('');
  log('💡 COMO USAR:', 'bright');
  log('   1. Acesse http://localhost:3000/api no navegador', 'white');
  log('   2. Teste o login em /auth/login', 'white');
  log('   3. Copie o token JWT retornado', 'white');
  log('   4. Clique em "Authorize" e cole o token', 'white');
  log('   5. Teste todos os endpoints documentados!', 'white');
  log('');
  log('📊 TOTAL DE ENDPOINTS DOCUMENTADOS: 60+', 'magenta');
  log('🏷️  TAGS ORGANIZADAS: 12 categorias', 'magenta');
  log('');
  log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━', 'cyan');
  log('');
  log('🚀 Iniciando servidor...', 'yellow');
  log('');
}

function showServerStarted() {
  log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━', 'green');
  log('✅ SERVIDOR INICIADO COM SUCESSO!', 'bright');
  log('');
  log('🌐 Links Úteis:', 'bright');
  log('   📖 Swagger UI: http://localhost:3000/api', 'green');
  log('   🔗 API Base: http://localhost:3000', 'blue');
  log('   📋 Health Check: http://localhost:3000/health (se disponível)', 'blue');
  log('');
  log('🎯 Endpoints Mais Usados:', 'bright');
  log('   • http://localhost:3000/auth/login', 'white');
  log('   • http://localhost:3000/users', 'white');
  log('   • http://localhost:3000/drivers/list', 'white');
  log('   • http://localhost:3000/vehicles/list', 'white');
  log('   • http://localhost:3000/romaneios', 'white');
  log('');
  log('⚡ Pronto para testar! Use Ctrl+C para parar o servidor', 'yellow');
  log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━', 'green');
  log('');
}

showBanner();

// Iniciar o servidor NestJS
const server = spawn('npm', ['run', 'start:dev'], {
  cwd: process.cwd(),
  stdio: ['inherit', 'pipe', 'pipe']
});

let serverStarted = false;

server.stdout.on('data', (data) => {
  const output = data.toString();
  
  // Detectar quando o servidor iniciou
  if (output.includes('Application is running on') || output.includes('Nest application successfully started')) {
    if (!serverStarted) {
      showServerStarted();
      serverStarted = true;
    }
  } else {
    // Mostrar outros logs do servidor
    process.stdout.write(output);
  }
});

server.stderr.on('data', (data) => {
  const output = data.toString();
  
  // Não mostrar warnings desnecessários
  if (!output.includes('ExperimentalWarning') && !output.includes('Warning:')) {
    log(output, 'red');
  }
});

server.on('close', (code) => {
  log('');
  log('🛑 Servidor parado', 'yellow');
  if (code !== 0) {
    log(`❌ Servidor finalizou com código de erro: ${code}`, 'red');
  }
  process.exit(code);
});

// Capturar Ctrl+C para finalizar graciosamente
process.on('SIGINT', () => {
  log('');
  log('🛑 Parando servidor...', 'yellow');
  server.kill('SIGINT');
});

process.on('SIGTERM', () => {
  log('');
  log('🛑 Parando servidor...', 'yellow');
  server.kill('SIGTERM');
}); 