import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SupplierEmail } from './entity/supplier-email.entity';
import { SupplierEmailService } from './supplier-email.service';
import { SupplierEmailController } from './supplier-email.controller';
import { OracleModule } from '../oracle/oracle.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([SupplierEmail]),
    OracleModule
  ],
  providers: [SupplierEmailService],
  controllers: [SupplierEmailController],
})
export class SupplierEmailModule {} 