"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PackagingService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const packaging_entity_1 = require("./entity/packaging.entity");
let PackagingService = class PackagingService {
    packagingRepository;
    constructor(packagingRepository) {
        this.packagingRepository = packagingRepository;
    }
    async findAll() {
        return this.packagingRepository.find({
            where: { deleted_at: (0, typeorm_2.IsNull)() },
            order: { name: 'ASC' },
        });
    }
    async create(dto) {
        const packaging = this.packagingRepository.create(dto);
        return this.packagingRepository.save(packaging);
    }
    async update(id, dto) {
        const packaging = await this.packagingRepository.findOne({
            where: { id, deleted_at: (0, typeorm_2.IsNull)() },
        });
        if (!packaging) {
            throw new common_1.NotFoundException('Embalagem não encontrada');
        }
        Object.assign(packaging, dto);
        return this.packagingRepository.save(packaging);
    }
    async delete(id) {
        const packaging = await this.packagingRepository.findOne({
            where: { id, deleted_at: (0, typeorm_2.IsNull)() },
        });
        if (!packaging) {
            throw new common_1.NotFoundException('Embalagem não encontrada');
        }
        await this.packagingRepository.softDelete(id);
        return true;
    }
    async findOne(id) {
        return this.packagingRepository.findOne({
            where: { id, deleted_at: (0, typeorm_2.IsNull)() },
        });
    }
};
exports.PackagingService = PackagingService;
exports.PackagingService = PackagingService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(packaging_entity_1.Packaging)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], PackagingService);
//# sourceMappingURL=packaging.service.js.map