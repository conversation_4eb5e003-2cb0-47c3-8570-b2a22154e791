import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, Index } from 'typeorm';

@Entity('vnf_consultas_processadas')
export class VnfConsultaProcessada {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'chave_acesso', type: 'varchar', length: 44, unique: true })
  @Index('idx_chave_acesso')
  chaveAcesso: string;

  @Column({ name: 'numero_nota', type: 'varchar', length: 50, nullable: true })
  numeroNota: string;

  @Column({ name: 'nome_fornecedor', type: 'varchar', length: 255, nullable: true })
  nomeFornecedor: string;

  @Column({ name: 'numero_pedido', type: 'varchar', length: 50, nullable: true })
  numeroPedido: string;

  @Column({ name: 'status_consulta', type: 'enum', enum: ['sucesso', 'erro', 'limite_atingido', 'erro_sefaz'] })
  statusConsulta: 'sucesso' | 'erro' | 'limite_atingido' | 'erro_sefaz';

  @Column({ name: 'tem_divergencias', type: 'boolean', default: false })
  temDivergencias: boolean;

  @Column({ name: 'observacoes', type: 'text', nullable: true })
  observacoes: string;

  @CreateDateColumn({ name: 'data_processamento' })
  dataProcessamento: Date;

  @Column({ name: 'data_documento', type: 'date', nullable: true })
  dataDocumento: Date;
} 