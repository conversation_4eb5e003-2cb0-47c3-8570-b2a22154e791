import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RomaneiosService } from './romaneios.service';
import { RomaneiosController } from './romaneios.controller';
import { Romaneio } from './entities/romaneio.entity';
import { RomaneioDraft } from './entities/romaneio-draft.entity';
import { MegaModule } from '../mega/mega.module';
import { InvoicesModule } from '../invoices/invoices.module';
import { OracleModule } from '../oracle/oracle.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Romaneio, RomaneioDraft]),
    MegaModule,
    InvoicesModule,
    OracleModule,
  ],
  controllers: [RomaneiosController],
  providers: [RomaneiosService],
  exports: [RomaneiosService, TypeOrmModule],
})
export class RomaneiosModule {}
