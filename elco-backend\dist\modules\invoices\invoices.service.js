"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InvoicesService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const invoice_entity_1 = require("./entities/invoice.entity");
const invoice_item_entity_1 = require("./entities/invoice-item.entity");
const invoice_error_entity_1 = require("./entities/invoice-error.entity");
let InvoicesService = class InvoicesService {
    invoiceRepository;
    invoiceItemRepository;
    invoiceErrorRepository;
    constructor(invoiceRepository, invoiceItemRepository, invoiceErrorRepository) {
        this.invoiceRepository = invoiceRepository;
        this.invoiceItemRepository = invoiceItemRepository;
        this.invoiceErrorRepository = invoiceErrorRepository;
    }
    async findAll(page = 1, limit = 10) {
        const [invoices, total] = await this.invoiceRepository.findAndCount({
            skip: (page - 1) * limit,
            take: limit,
            order: {
                createdAt: 'DESC',
            },
        });
        const invoicesWithDetails = await Promise.all(invoices.map(async (invoice) => {
            const [items, errors] = await Promise.all([
                this.invoiceItemRepository.find({
                    where: { invoiceId: invoice.id },
                }),
                this.invoiceErrorRepository.find({
                    where: {
                        idRomaneio: invoice.idRomaneio,
                        idOrdem: invoice.idOrdem,
                    },
                }),
            ]);
            return {
                ...invoice,
                items,
                errors,
            };
        }));
        return {
            invoices: invoicesWithDetails,
            total,
        };
    }
    async listInvoices() {
        const invoices = await this.invoiceRepository.find({
            relations: ['items'],
            order: {
                createdAt: 'DESC',
            },
        });
        const normalInvoices = invoices.map((invoice) => ({
            id: invoice.id,
            filInCodigo: invoice.filInCodigo,
            notInCodigo: invoice.notInCodigo,
            notInNumero: invoice.notInNumero,
            tpdInCodigo: invoice.tpdInCodigo,
            notDtEmissao: invoice.notDtEmissao,
            notHrHoraemissao: invoice.notHrHoraemissao,
            notStChaveacesso: invoice.notStChaveacesso,
            notStCgc: invoice.notStCgc,
            notStIncrestadual: invoice.notStIncrestadual,
            notStMunicipio: invoice.notStMunicipio,
            notStUf: invoice.notStUf,
            items: invoice.items.map((item) => ({
                id: item.id,
            })),
            errors: [],
            idRomaneio: invoice.idRomaneio,
            idOrdem: invoice.idOrdem,
        }));
        return {
            invoices: normalInvoices,
            total: normalInvoices.length,
        };
    }
    async listInvoiceErrors() {
        const errors = await this.invoiceErrorRepository.find({
            order: {
                createdAt: 'DESC',
            },
        });
        const errorList = errors.map((error) => ({
            id: error.id,
            errorMessage: error.errorMessage,
            data: error.data || error.createdAt,
            xmlEnviado: error.xmlEnviado,
            soapEnvelope: error.soapEnvelope,
            idRomaneio: error.idRomaneio,
            idOrdem: error.idOrdem,
        }));
        return {
            errors: errorList,
            total: errorList.length,
        };
    }
};
exports.InvoicesService = InvoicesService;
exports.InvoicesService = InvoicesService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(invoice_entity_1.Invoice)),
    __param(1, (0, typeorm_1.InjectRepository)(invoice_item_entity_1.InvoiceItem)),
    __param(2, (0, typeorm_1.InjectRepository)(invoice_error_entity_1.InvoiceError)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], InvoicesService);
//# sourceMappingURL=invoices.service.js.map