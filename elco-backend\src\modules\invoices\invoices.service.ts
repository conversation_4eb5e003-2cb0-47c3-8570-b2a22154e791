import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Invoice } from './entities/invoice.entity';
import { InvoiceItem } from './entities/invoice-item.entity';
import { InvoiceError } from './entities/invoice-error.entity';
import { InvoiceDto, InvoiceListResponseDto, InvoiceErrorDto, InvoiceErrorListResponseDto } from './dto/invoice-list.dto';

@Injectable()
export class InvoicesService {
  constructor(
    @InjectRepository(Invoice)
    private readonly invoiceRepository: Repository<Invoice>,
    @InjectRepository(InvoiceItem)
    private readonly invoiceItemRepository: Repository<InvoiceItem>,
    @InjectRepository(InvoiceError)
    private readonly invoiceErrorRepository: Repository<InvoiceError>,
  ) {}

  async findAll(page = 1, limit = 10): Promise<InvoiceListResponseDto> {
    const [invoices, total] = await this.invoiceRepository.findAndCount({
      skip: (page - 1) * limit,
      take: limit,
      order: {
        createdAt: 'DESC'
      }
    });

    const invoicesWithDetails = await Promise.all(
      invoices.map(async (invoice) => {
        const [items, errors] = await Promise.all([
          this.invoiceItemRepository.find({
            where: { invoiceId: invoice.id }
          }),
          this.invoiceErrorRepository.find({
            where: { 
              idRomaneio: invoice.idRomaneio,
              idOrdem: invoice.idOrdem
            }
          })
        ]);

        return {
          ...invoice,
          items,
          errors
        };
      })
    );

    return {
      invoices: invoicesWithDetails,
      total
    };
  }

  async listInvoices(): Promise<InvoiceListResponseDto> {
    const invoices = await this.invoiceRepository.find({
      relations: ['items'],
      order: {
        createdAt: 'DESC'
      }
    });

    const normalInvoices: InvoiceDto[] = invoices.map(invoice => ({
      id: invoice.id,
      filInCodigo: invoice.filInCodigo,
      notInCodigo: invoice.notInCodigo,
      notInNumero: invoice.notInNumero,
      tpdInCodigo: invoice.tpdInCodigo,
      notDtEmissao: invoice.notDtEmissao,
      notHrHoraemissao: invoice.notHrHoraemissao,
      notStChaveacesso: invoice.notStChaveacesso,
      notStCgc: invoice.notStCgc,
      notStIncrestadual: invoice.notStIncrestadual,
      notStMunicipio: invoice.notStMunicipio,
      notStUf: invoice.notStUf,
      items: invoice.items.map(item => ({
        id: item.id,
      })),
      errors: [],
      idRomaneio: invoice.idRomaneio,
      idOrdem: invoice.idOrdem
    }));

    return {
      invoices: normalInvoices,
      total: normalInvoices.length
    };
  }

  async listInvoiceErrors(): Promise<InvoiceErrorListResponseDto> {
    const errors = await this.invoiceErrorRepository.find({
      order: {
        createdAt: 'DESC'
      }
    });

    const errorList: InvoiceErrorDto[] = errors.map(error => ({
      id: error.id,
      errorMessage: error.errorMessage,
      data: error.data || error.createdAt,
      xmlEnviado: error.xmlEnviado,
      soapEnvelope: error.soapEnvelope,
      idRomaneio: error.idRomaneio,
      idOrdem: error.idOrdem
    }));

    return {
      errors: errorList,
      total: errorList.length
    };
  }

  
} 