import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Body,
  ParseUUIDPipe,
  NotFoundException,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBody, ApiParam } from '@nestjs/swagger';
import { PackagingService } from './packaging.service';
import { CreatePackagingDto } from './dto/create-packaging.dto';
import { UpdatePackagingDto } from './dto/update-packaging.dto';

@ApiTags('Embalagens')
@Controller('packaging')
export class PackagingController {
  constructor(private readonly packagingService: PackagingService) { }

  @Get('/list')
  @ApiOperation({
    summary: 'Listar todas as embalagens',
    description: 'Retorna uma lista com todas as embalagens cadastradas no sistema.'
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de embalagens',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'string', example: 'uuid-string' },
          name: { type: 'string', example: 'Cai<PERSON> Pa<PERSON> Média' },
          type: { type: 'string', example: 'BOX' },
          dimensions: { type: 'string', example: '30x20x15 cm' },
          weight: { type: 'number', example: 0.5 },
          capacity: { type: 'number', example: 10 },
          isActive: { type: 'boolean', example: true }
        }
      }
    }
  })
  async findAll() {
    return this.packagingService.findAll();
  }

  @Post('/create')
  @ApiOperation({
    summary: 'Criar nova embalagem',
    description: 'Cadastra um novo tipo de embalagem no sistema.'
  })
  @ApiBody({
    type: CreatePackagingDto,
    description: 'Dados da nova embalagem',
    examples: {
      create: {
        summary: 'Exemplo de embalagem',
        value: {
          name: 'Caixa Papelão Grande',
          type: 'BOX',
          dimensions: '50x40x30 cm',
          weight: 1.2,
          capacity: 25
        }
      }
    }
  })
  @ApiResponse({
    status: 201,
    description: 'Embalagem criada com sucesso',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string', example: 'uuid-string' },
        name: { type: 'string', example: 'Caixa Papelão Grande' },
        type: { type: 'string', example: 'BOX' },
        dimensions: { type: 'string', example: '50x40x30 cm' },
        weight: { type: 'number', example: 1.2 },
        capacity: { type: 'number', example: 25 }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  async create(@Body() dto: CreatePackagingDto) {
    return this.packagingService.create(dto);
  }

  @Put('/update/:id')
  @ApiOperation({
    summary: 'Atualizar embalagem',
    description: 'Atualiza os dados de uma embalagem existente.'
  })
  @ApiParam({ name: 'id', description: 'ID da embalagem', type: 'string' })
  @ApiBody({ type: UpdatePackagingDto })
  @ApiResponse({
    status: 200,
    description: 'Embalagem atualizada com sucesso'
  })
  @ApiResponse({ status: 404, description: 'Embalagem não encontrada' })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  async update(
    @Param('id') id: string,
    @Body() dto: UpdatePackagingDto,
  ) {
    const updated = await this.packagingService.update(id, dto);
    if (!updated) {
      throw new NotFoundException('Embalagem não encontrada');
    }
    return updated;
  }

  @Delete('/delete/:id')
  @ApiOperation({
    summary: 'Deletar embalagem',
    description: 'Remove uma embalagem do sistema.'
  })
  @ApiParam({ name: 'id', description: 'ID da embalagem', type: 'string' })
  @ApiResponse({
    status: 200,
    description: 'Embalagem excluída com sucesso',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'Embalagem excluída com sucesso' }
      }
    }
  })
  @ApiResponse({ status: 404, description: 'Embalagem não encontrada' })
  async delete(@Param('id') id: string) {
    const deleted = await this.packagingService.delete(id);
    if (!deleted) {
      throw new NotFoundException('Embalagem não encontrada');
    }
    return { message: 'Embalagem excluída com sucesso' };
  }

  @Get('/view/:id')
  @ApiOperation({
    summary: 'Obter embalagem por ID',
    description: 'Retorna os detalhes completos de uma embalagem específica.'
  })
  @ApiParam({ name: 'id', description: 'ID da embalagem', type: 'string' })
  @ApiResponse({
    status: 200,
    description: 'Dados da embalagem',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string', example: 'uuid-string' },
        name: { type: 'string', example: 'Caixa Papelão Média' },
        type: { type: 'string', example: 'BOX' },
        dimensions: { type: 'string', example: '30x20x15 cm' },
        weight: { type: 'number', example: 0.5 },
        capacity: { type: 'number', example: 10 },
        isActive: { type: 'boolean', example: true },
        createdAt: { type: 'string', format: 'date-time' }
      }
    }
  })
  @ApiResponse({ status: 404, description: 'Embalagem não encontrada' })
  async findOne(@Param('id') id: string) {
    const packaging = await this.packagingService.findOne(id);
    if (!packaging) {
      throw new NotFoundException('Embalagem não encontrada');
    }
    return packaging;
  }
}
