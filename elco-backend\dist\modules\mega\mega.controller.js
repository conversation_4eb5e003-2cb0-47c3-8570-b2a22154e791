"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MegaController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const mega_service_1 = require("./mega.service");
let MegaController = class MegaController {
    megaService;
    constructor(megaService) {
        this.megaService = megaService;
    }
    async consultarXml(chaveAcesso) {
        const teste = this.megaService.consultarXml(chaveAcesso);
        return teste;
    }
    async enviarNotaFiscal(dados, idOrdem, idRomaneio) {
        return this.megaService.enviarNotaFiscal(dados, idOrdem, idRomaneio);
    }
    async reenviarXML(body) {
        return this.megaService.reenviarXML(body.errorId, body.xmlEnviado);
    }
    async consultarXmls(body) {
        return this.megaService.consultarXmls(body.chavesAcesso);
    }
    async consultarProdutosRomaneio(romaneioId) {
        return this.megaService.consultarProdutosRomaneio(romaneioId);
    }
};
exports.MegaController = MegaController;
__decorate([
    (0, common_1.Get)('consultar-xml/:chaveAcesso'),
    (0, swagger_1.ApiOperation)({
        summary: 'Consultar XML por chave de acesso',
        description: 'Consulta dados de uma nota fiscal no sistema Mega através da chave de acesso.'
    }),
    (0, swagger_1.ApiParam)({
        name: 'chaveAcesso',
        description: 'Chave de acesso da nota fiscal (44 dígitos)',
        type: 'string',
        example: '35200714200166000187550010000000007907734918'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Dados da nota fiscal consultada',
        schema: {
            type: 'object',
            properties: {
                chaveAcesso: { type: 'string', example: '35200714200166000187550010000000007907734918' },
                status: { type: 'string', example: 'AUTORIZADA' },
                numero: { type: 'string', example: '000000007' },
                serie: { type: 'string', example: '001' },
                dataEmissao: { type: 'string', format: 'date-time' },
                valorTotal: { type: 'number', example: 1250.50 },
                fornecedor: {
                    type: 'object',
                    properties: {
                        cnpj: { type: 'string', example: '14.200.166/0001-87' },
                        razaoSocial: { type: 'string', example: 'Empresa Fornecedora Ltda' }
                    }
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Nota fiscal não encontrada' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Chave de acesso inválida' }),
    __param(0, (0, common_1.Param)('chaveAcesso')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], MegaController.prototype, "consultarXml", null);
__decorate([
    (0, common_1.Post)('nota-fiscal/:idOrdem/:idRomaneio'),
    (0, swagger_1.ApiOperation)({
        summary: 'Enviar nota fiscal',
        description: 'Envia uma nota fiscal para o sistema Mega vinculada a uma ordem e romaneio.'
    }),
    (0, swagger_1.ApiParam)({
        name: 'idOrdem',
        description: 'ID da ordem',
        type: 'number',
        example: 123
    }),
    (0, swagger_1.ApiParam)({
        name: 'idRomaneio',
        description: 'ID do romaneio',
        type: 'number',
        example: 456
    }),
    (0, swagger_1.ApiBody)({
        description: 'Dados da nota fiscal para envio',
        schema: {
            type: 'object',
            properties: {
                chaveAcesso: { type: 'string', example: '35200714200166000187550010000000007907734918' },
                xmlContent: { type: 'string', description: 'Conteúdo XML da nota fiscal' },
                observacoes: { type: 'string', example: 'Nota fiscal de produtos especiais' }
            },
            required: ['chaveAcesso', 'xmlContent']
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Nota fiscal enviada com sucesso',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                protocolo: { type: 'string', example: 'PROT123456789' },
                message: { type: 'string', example: 'Nota fiscal processada com sucesso' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dados inválidos ou erro no processamento' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Param)('idOrdem')),
    __param(2, (0, common_1.Param)('idRomaneio')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, Number]),
    __metadata("design:returntype", Promise)
], MegaController.prototype, "enviarNotaFiscal", null);
__decorate([
    (0, common_1.Post)('errors/send-xml'),
    (0, swagger_1.ApiOperation)({
        summary: 'Reenviar XML com erro',
        description: 'Reenvia um XML que apresentou erro durante o processamento.'
    }),
    (0, swagger_1.ApiBody)({
        description: 'Dados para reenvio do XML',
        schema: {
            type: 'object',
            properties: {
                errorId: { type: 'number', example: 789 },
                xmlEnviado: { type: 'string', description: 'Conteúdo XML corrigido para reenvio' }
            },
            required: ['errorId', 'xmlEnviado']
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'XML reenviado com sucesso',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                errorId: { type: 'number', example: 789 },
                message: { type: 'string', example: 'XML reenviado e processado com sucesso' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Erro não encontrado' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'XML inválido' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], MegaController.prototype, "reenviarXML", null);
__decorate([
    (0, common_1.Post)('consultar-xmls'),
    (0, swagger_1.ApiOperation)({
        summary: 'Consultar múltiplos XMLs',
        description: 'Consulta informações de múltiplas notas fiscais através de suas chaves de acesso.'
    }),
    (0, swagger_1.ApiBody)({
        description: 'Lista de chaves de acesso para consulta',
        schema: {
            type: 'object',
            properties: {
                chavesAcesso: {
                    type: 'array',
                    items: { type: 'string' },
                    example: [
                        '35200714200166000187550010000000007907734918',
                        '35200714200166000187550010000000008907734919'
                    ],
                    description: 'Array com chaves de acesso das notas fiscais'
                }
            },
            required: ['chavesAcesso']
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Consulta realizada com sucesso',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                total: { type: 'number', example: 2 },
                results: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            chaveAcesso: { type: 'string', example: '35200714200166000187550010000000007907734918' },
                            status: { type: 'string', example: 'ENCONTRADA' },
                            dados: { type: 'object', description: 'Dados da nota fiscal' }
                        }
                    }
                },
                errors: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            chaveAcesso: { type: 'string', example: '35200714200166000187550010000000008907734919' },
                            error: { type: 'string', example: 'Nota fiscal não encontrada' }
                        }
                    }
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Lista de chaves inválida ou vazia' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], MegaController.prototype, "consultarXmls", null);
__decorate([
    (0, common_1.Get)('produtos-romaneio/:romaneioId'),
    (0, swagger_1.ApiOperation)({
        summary: 'Consultar produtos de um romaneio',
        description: 'Consulta todos os produtos de um romaneio específico para debug.'
    }),
    (0, swagger_1.ApiParam)({
        name: 'romaneioId',
        description: 'ID do romaneio',
        type: 'number',
        example: 1
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Produtos do romaneio',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                produtos: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            id: { type: 'number' },
                            idRomanio: { type: 'number' },
                            idSeparationOrder: { type: 'number' },
                            name: { type: 'string' },
                            taxNoteNumber: { type: 'string' }
                        }
                    }
                }
            }
        }
    }),
    __param(0, (0, common_1.Param)('romaneioId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], MegaController.prototype, "consultarProdutosRomaneio", null);
exports.MegaController = MegaController = __decorate([
    (0, swagger_1.ApiTags)('Mega/XML'),
    (0, common_1.Controller)('mega'),
    __metadata("design:paramtypes", [mega_service_1.MegaService])
], MegaController);
//# sourceMappingURL=mega.controller.js.map