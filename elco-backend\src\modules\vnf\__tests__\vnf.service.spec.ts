import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { VnfService } from '../vnf.service';
import { Invoice } from '../../invoices/entities/invoice.entity';
import { InvoiceItem } from '../../invoices/entities/invoice-item.entity';
import { Product } from '../../products/entities/product.entity';
import { VnfError } from '../vnf-error.entity';
import { SeparationOrder } from '../../separation-orders/entity/separation-order.entity';
import { Romaneio } from '../../romaneios/entities/romaneio.entity';
import { RomaneioOrder } from '../../romaneio-orders/entities/romaneio-order.entity';
import { SupplierEmail } from '../../supplier-email/entity/supplier-email.entity';
import { MegaService } from '../../mega/mega.service';
import { createPedidoPortalFixture, createNotaFiscalFixture } from './fixtures/vnf-test-fixtures';
import { PedidoPortal, NotaFiscal } from '../interfaces';

describe('VnfService', () => {
  let service: VnfService;
  let vnfErrorRepository: Repository<VnfError>;

  const mockInvoiceRepository = {
    findOne: jest.fn(),
  };

  const mockInvoiceItemRepository = {
    find: jest.fn(),
  };

  const mockProductRepository = {
    findOne: jest.fn(),
  };

  const mockVnfErrorRepository = {
    create: jest.fn(),
    save: jest.fn(),
    find: jest.fn(),
    findOne: jest.fn(),
  };

  const mockSeparationOrderRepository = {
    findOne: jest.fn(),
  };

  const mockRomaneioRepository = {
    findOne: jest.fn(),
  };

  const mockRomaneioOrderRepository = {
    findOne: jest.fn(),
  };

  const mockSupplierEmailRepository = {
    createQueryBuilder: jest.fn(),
  };

  const mockMegaService = {
    consultarXml: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        VnfService,
        {
          provide: getRepositoryToken(Invoice),
          useValue: mockInvoiceRepository,
        },
        {
          provide: getRepositoryToken(InvoiceItem),
          useValue: mockInvoiceItemRepository,
        },
        {
          provide: getRepositoryToken(Product),
          useValue: mockProductRepository,
        },
        {
          provide: getRepositoryToken(VnfError),
          useValue: mockVnfErrorRepository,
        },
        {
          provide: getRepositoryToken(SeparationOrder),
          useValue: mockSeparationOrderRepository,
        },
        {
          provide: getRepositoryToken(Romaneio),
          useValue: mockRomaneioRepository,
        },
        {
          provide: getRepositoryToken(RomaneioOrder),
          useValue: mockRomaneioOrderRepository,
        },
        {
          provide: getRepositoryToken(SupplierEmail),
          useValue: mockSupplierEmailRepository,
        },
        {
          provide: MegaService,
          useValue: mockMegaService,
        },
      ],
    }).compile();

    service = module.get<VnfService>(VnfService);
    vnfErrorRepository = module.get<Repository<VnfError>>(getRepositoryToken(VnfError));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Busca de email do fornecedor', () => {
    it('deve buscar email do fornecedor com sucesso', async () => {
      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue({
          id: 1,
          supplierName: 'PERFISAELETRO INDUSTRIA DE ELETROCALHAS LTDA',
          email: '<EMAIL>'
        })
      };

      mockSupplierEmailRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);

      const pedido = createPedidoPortalFixture({
        nomeFornecedor: 'PERFISAELETRO INDUSTRIA DE ELETROCALHAS LTDA'
      });

      const notaFiscal = createNotaFiscalFixture({
        informacoesAdicionais: {
          infCpl: 'PEDIDO N. 40514 - ENTREGA ELCO CIC.'
        }
      });

      const resultado = await service.processarComparacaoPedidoNotaFiscal(
        pedido,
        notaFiscal,
        '12345',
        '40514'
      );

      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        'UPPER(supplier.supplierName) LIKE :nome',
        { nome: '%PERFISAELETRO INDUSTRIA DE ELETROCALHAS LTDA%' }
      );
      expect(mockQueryBuilder.getOne).toHaveBeenCalled();
    });

    it('deve retornar null quando fornecedor não for encontrado', async () => {
      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(null)
      };

      mockSupplierEmailRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);

      const pedido = createPedidoPortalFixture({
        nomeFornecedor: 'FORNECEDOR INEXISTENTE'
      });

      const notaFiscal = createNotaFiscalFixture({
        informacoesAdicionais: {
          infCpl: 'PEDIDO N. 40514 - ENTREGA ELCO CIC.'
        }
      });

      const resultado = await service.processarComparacaoPedidoNotaFiscal(
        pedido,
        notaFiscal,
        '12345',
        '40514'
      );

      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        'UPPER(supplier.supplierName) LIKE :nome',
        { nome: '%FORNECEDOR INEXISTENTE%' }
      );
      expect(mockQueryBuilder.getOne).toHaveBeenCalled();
    });
  });

  describe('Extração de número do pedido', () => {
    it('deve extrair número do pedido quando há apenas um número', () => {
      const resultado = service['extrairNumeroPedido']('PEDIDO N. 40514 - ENTREGA ELCO CIC.');
      expect(resultado.numero).toBe('40514');
      expect(resultado.erro).toBeUndefined();
    });

    it('deve extrair número do pedido quando há múltiplos números mas um padrão específico', () => {
      const resultado = service['extrairNumeroPedido']('PEDIDO N. 40514 - ENTREGA ELCO CIC. REF: 12345');
      expect(resultado.numero).toBe('40514');
      expect(resultado.erro).toBeUndefined();
    });

    it('deve usar o primeiro número quando há múltiplos sem padrão específico', () => {
      const resultado = service['extrairNumeroPedido']('REF: 40514 - ENTREGA ELCO CIC. REF: 12345');
      expect(resultado.numero).toBe('40514');
      expect(resultado.erro).toBeUndefined();
    });

    it('deve retornar erro quando não há números', () => {
      const resultado = service['extrairNumeroPedido']('ENTREGA ELCO CIC.');
      expect(resultado.numero).toBeNull();
      expect(resultado.erro).toBe('Número do pedido não encontrado nas observações da NF.');
    });

    it('deve retornar erro quando infCpl está vazio', () => {
      const resultado = service['extrairNumeroPedido']('');
      expect(resultado.numero).toBeNull();
      expect(resultado.erro).toBe('Número do pedido não encontrado nas observações da NF.');
    });
  });

  describe('Regra 1: Validação de data de entrega', () => {
    it('deve detectar erro quando data de entrega está mais de 10 dias antes da emissão', async () => {
      const pedido = createPedidoPortalFixture({
        itens: [
          {
            sequencial: 1,
            codigoFamiliaDeProduto: 'FAM001',
            codigoUnidadeMedida: 'PC',
            codigoProduto: 'PROD001',
            descricaoBreveDoProduto: 'Produto Teste',
            descricaoDetalhadaDoProduto: 'Produto de teste para validação',
            baseDeCalculoICMS: null,
            aliquitaICMS: null,
            aliquitaIPI: null,
            aliquotaPIS: null,
            aliquotaCOFINS: null,
            quantidade: 10.00,
            valorUnitario: 1.00,
            valorTotal: 10.00,
            valorLiquido: 10.00,
            situacaoDoItem: 'ATIVO',
            aplicacao: 'TESTE',
            iva: 'S',
            nmc: '0000.00.00',
            contrato: 'CONT001',
            sequencialDoItemDeContrato: null,
            requisicao: 'REQ001',
            loginRequisitante: '<EMAIL>',
            tipoRequisicaoERP: 'COMPRA',
            sequencialDoItemDeRequisicao: 1,
            valorDaUltimaCompra: 1.00,
            valorDaUltimaCompraParaPlanta: 1.00,
            valorLiquidoDaUltimaCompra: 1.00,
            valorLiquidoDaUltimaCompraParaPlanta: 1.00,
            valorDoDesconto: 0.00,
            cotacaoDeReferencia: 'COT001',
            sequencialDoItemDaCotacao: null,
            utilizacao: 'TESTE',
            observacoes: [],
            rateio: [],
            entrega: [
              {
                dataDeEntrega: '2024-01-01T00:00:00Z', // 15 dias antes da emissão
                diasParaEntrega: 15,
                quantidadeEntrega: 10,
                parcela: 1
              }
            ]
          }
        ]
      });

      const notaFiscal = createNotaFiscalFixture({
        dataEmissao: '2024-01-16T00:00:00Z', // 15 dias depois
        produtos: [
          {
            numeroItem: '1',
            codigo: 'PROD001',
            descricao: 'Produto Teste',
            ncm: '0000.00.00',
            cfop: '5102',
            unidade: 'PC',
            quantidade: '10.0000',
            valorUnitario: '1.0000000000',
            valorTotal: '10.00'
          }
        ],
        informacoesAdicionais: {
          infCpl: 'PEDIDO N. 40514 - ENTREGA ELCO CIC.'
        }
      });

      const errors = await service.compararPedidoComNotaFiscal(pedido, notaFiscal);
      
      const erroDataEntrega = errors.find(error => error.includes('Data de entrega do item 1 antecipada em 15 dias'));
      expect(erroDataEntrega).toBeDefined();
    });

    it('não deve detectar erro quando data de entrega está dentro do limite de 10 dias', async () => {
      const pedido = createPedidoPortalFixture({
        itens: [
          {
            sequencial: 1,
            codigoFamiliaDeProduto: 'FAM001',
            codigoUnidadeMedida: 'PC',
            codigoProduto: 'PROD001',
            descricaoBreveDoProduto: 'Produto Teste',
            descricaoDetalhadaDoProduto: 'Produto de teste para validação',
            baseDeCalculoICMS: null,
            aliquitaICMS: null,
            aliquitaIPI: null,
            aliquotaPIS: null,
            aliquotaCOFINS: null,
            quantidade: 10.00,
            valorUnitario: 1.00,
            valorTotal: 10.00,
            valorLiquido: 10.00,
            situacaoDoItem: 'ATIVO',
            aplicacao: 'TESTE',
            iva: 'S',
            nmc: '0000.00.00',
            contrato: 'CONT001',
            sequencialDoItemDeContrato: null,
            requisicao: 'REQ001',
            loginRequisitante: '<EMAIL>',
            tipoRequisicaoERP: 'COMPRA',
            sequencialDoItemDeRequisicao: 1,
            valorDaUltimaCompra: 1.00,
            valorDaUltimaCompraParaPlanta: 1.00,
            valorLiquidoDaUltimaCompra: 1.00,
            valorLiquidoDaUltimaCompraParaPlanta: 1.00,
            valorDoDesconto: 0.00,
            cotacaoDeReferencia: 'COT001',
            sequencialDoItemDaCotacao: null,
            utilizacao: 'TESTE',
            observacoes: [],
            rateio: [],
            entrega: [
              {
                dataDeEntrega: '2024-01-10T00:00:00Z', // 6 dias antes da emissão
                diasParaEntrega: 6,
                quantidadeEntrega: 10,
                parcela: 1
              }
            ]
          }
        ]
      });

      const notaFiscal = createNotaFiscalFixture({
        dataEmissao: '2024-01-16T00:00:00Z', // 6 dias depois
        produtos: [
          {
            numeroItem: '1',
            codigo: 'PROD001',
            descricao: 'Produto Teste',
            ncm: '0000.00.00',
            cfop: '5102',
            unidade: 'PC',
            quantidade: '10.0000',
            valorUnitario: '1.0000000000',
            valorTotal: '10.00'
          }
        ],
        informacoesAdicionais: {
          infCpl: 'PEDIDO N. 40514 - ENTREGA ELCO CIC.'
        }
      });

      const errors = await service.compararPedidoComNotaFiscal(pedido, notaFiscal);
      
      const erroDataEntrega = errors.find(error => error.includes('Data de entrega'));
      expect(erroDataEntrega).toBeUndefined();
    });
  });

  describe('Regra 2: Janela de recusa do dia 26 ao último dia do mês', () => {
    it('deve definir podeRecusar como false quando está na janela de recusa', async () => {
      // Mock da data atual para estar na janela de recusa (dia 26)
      const mockDate = new Date('2024-01-26T00:00:00Z');
      jest.spyOn(global, 'Date').mockImplementation(() => mockDate);

      const pedido = createPedidoPortalFixture();
      const notaFiscal = createNotaFiscalFixture({
        informacoesAdicionais: {
          infCpl: 'PEDIDO N. 40514 - ENTREGA ELCO CIC.'
        }
      });

      // Forçar um erro para testar a janela de recusa
      const pedidoComErro = createPedidoPortalFixture({
        pedidoERP: '12345' // Número diferente para gerar erro
      });
      
      const resultado = await service.processarComparacaoPedidoNotaFiscal(
        pedidoComErro,
        notaFiscal,
        '9199',
        '40514'
      );

      // Verificar se o erro foi salvo com podeRecusar = false
      expect(resultado.success).toBe(false);
      
      // Restaurar a implementação original da Date
      jest.restoreAllMocks();
    });
  });

  describe('Regra 3: NF deve referenciar apenas um Pedido de Compra', () => {
    it('deve detectar erro quando não encontra número do pedido', async () => {
      const pedido = createPedidoPortalFixture();
      const notaFiscal = createNotaFiscalFixture({
        informacoesAdicionais: {
          infCpl: 'APENAS TEXTO SEM NUMEROS DE PEDIDO'
        }
      });

      const errors = await service.compararPedidoComNotaFiscal(pedido, notaFiscal);
      
      const erroNumeroPedido = errors.find(error => error.includes('Número do pedido não encontrado'));
      expect(erroNumeroPedido).toBeDefined();
    });

    it('deve aceitar quando encontra múltiplos números mas um padrão específico de pedido', async () => {
      const pedido = createPedidoPortalFixture();
      const notaFiscal = createNotaFiscalFixture({
        informacoesAdicionais: {
          infCpl: 'PEDIDO N. 40514 E TAMBEM PEDIDO 67890 - ENTREGA ELCO CIC. PEDIDO 99999'
        }
      });

      const errors = await service.compararPedidoComNotaFiscal(pedido, notaFiscal);
      
      // Com a nova lógica, deve aceitar quando há padrão específico de pedido
      const erroMultiplosPedidos = errors.find(error => error.includes('múltiplos pedidos'));
      expect(erroMultiplosPedidos).toBeUndefined();
    });

    it('deve aceitar quando encontra apenas um número de pedido', async () => {
      const pedido = createPedidoPortalFixture();
      const notaFiscal = createNotaFiscalFixture({
        informacoesAdicionais: {
          infCpl: 'PEDIDO N. 40514 - ENTREGA ELCO CIC.'
        }
      });

      const errors = await service.compararPedidoComNotaFiscal(pedido, notaFiscal);
      
      const erroMultiplosPedidos = errors.find(error => error.includes('múltiplos pedidos'));
      expect(erroMultiplosPedidos).toBeUndefined();
    });
  describe('Regra 4: Coerência valor item (valorUnitário × quantidade = valorTotal)', () => {
    it('deve detectar erro quando valor total é inconsistente', async () => {
      const pedido = createPedidoPortalFixture();
      const notaFiscal = createNotaFiscalFixture({
        produtos: [
          {
            numeroItem: '1',
            codigo: 'PROD001',
            descricao: 'Produto Teste',
            ncm: '0000.00.00',
            cfop: '5102',
            unidade: 'PC',
            valorUnitario: '6.97', // 6.97 × 50 = 348.50
            quantidade: '50.0000',
            valorTotal: '350.00' // Deveria ser 348.50
          }
        ],
        informacoesAdicionais: {
          infCpl: 'PEDIDO N. 40514 - ENTREGA ELCO CIC.'
        }
      });

      const errors = await service.compararPedidoComNotaFiscal(pedido, notaFiscal);
      
      const erroValorInconsistente = errors.find(error => error.includes('Valor total inconsistente no item 1'));
      expect(erroValorInconsistente).toBeDefined();
    });

    it('não deve detectar erro quando valor total está correto', async () => {
      const pedido = createPedidoPortalFixture();
      const notaFiscal = createNotaFiscalFixture({
        produtos: [
          {
            numeroItem: '1',
            codigo: 'PROD001',
            descricao: 'Produto Teste',
            ncm: '0000.00.00',
            cfop: '5102',
            unidade: 'PC',
            valorUnitario: '6.97', // 6.97 × 50 = 348.50
            quantidade: '50.0000',
            valorTotal: '348.50' // Correto
          }
        ],
        informacoesAdicionais: {
          infCpl: 'PEDIDO N. 40514 - ENTREGA ELCO CIC.'
        }
      });

      const errors = await service.compararPedidoComNotaFiscal(pedido, notaFiscal);
      
      const erroValorInconsistente = errors.find(error => error.includes('Valor total inconsistente'));
      expect(erroValorInconsistente).toBeUndefined();
    });
  });

  describe('Regra 5: Prazo limite de emissão (NF deve ser emitida até dia 25)', () => {
    it('deve detectar erro quando NF é emitida após dia 25', async () => {
      const pedido = createPedidoPortalFixture();
      const notaFiscal = createNotaFiscalFixture({
        dataEmissao: '2024-01-26T00:00:00Z', // Dia 26 de janeiro
        informacoesAdicionais: {
          infCpl: 'PEDIDO N. 40514 - ENTREGA ELCO CIC.'
        }
      });

      const errors = await service.compararPedidoComNotaFiscal(pedido, notaFiscal);
      
      const erroPrazoLimite = errors.find(error => error.includes('emitida após o prazo limite'));
      expect(erroPrazoLimite).toBeDefined();
      expect(erroPrazoLimite).toContain('dia 26');
      expect(erroPrazoLimite).toContain('janeiro de 2024');
    });

    it('não deve detectar erro quando NF é emitida até dia 25', async () => {
      const pedido = createPedidoPortalFixture();
      const notaFiscal = createNotaFiscalFixture({
        dataEmissao: '2024-01-25T00:00:00Z', // Dia 25 de janeiro (limite)
        informacoesAdicionais: {
          infCpl: 'PEDIDO N. 40514 - ENTREGA ELCO CIC.'
        }
      });

      const errors = await service.compararPedidoComNotaFiscal(pedido, notaFiscal);
      
      const erroPrazoLimite = errors.find(error => error.includes('emitida após o prazo limite'));
      expect(erroPrazoLimite).toBeUndefined();
    });

    it('não deve detectar erro quando NF é emitida antes do dia 25', async () => {
      const pedido = createPedidoPortalFixture();
      const notaFiscal = createNotaFiscalFixture({
        dataEmissao: '2024-01-15T00:00:00Z', // Dia 15 de janeiro
        informacoesAdicionais: {
          infCpl: 'PEDIDO N. 40514 - ENTREGA ELCO CIC.'
        }
      });

      const errors = await service.compararPedidoComNotaFiscal(pedido, notaFiscal);
      
      const erroPrazoLimite = errors.find(error => error.includes('emitida após o prazo limite'));
      expect(erroPrazoLimite).toBeUndefined();
    });
  });

  describe('envio de emails', () => {
    it('deve enviar email de divergência com sucesso', async () => {
      // Mock do erro
      const mockErro = {
        id: 1,
        numeroNota: '45236',
        clienteExternalCode: 'TEST123',
        tipoErro: 'NF Emitida Incoerente! Quantidade do item 1 excede o pedido. Pedido: 100, NF: 120',
        compradorEmail: '<EMAIL>',
        fornecedorEmail: '<EMAIL>',
        dataEmailEnviado: null,
        podeRecusar: true
      };

      jest.spyOn(service['vnfErrorRepository'], 'findOne').mockResolvedValue(mockErro as any);
      jest.spyOn(service['vnfErrorRepository'], 'save').mockResolvedValue(mockErro as any);

      const result = await service.enviarEmailDivergencia(1);

      expect(result.success).toBe(true);
      expect(result.message).toContain('Email enviado com <NAME_EMAIL>');
      expect(service['vnfErrorRepository'].save).toHaveBeenCalledWith(
        expect.objectContaining({
          ...mockErro,
          dataEmailEnviado: expect.any(Date)
        })
      );
    });

    it('deve falhar ao enviar email quando erro não existe', async () => {
      jest.spyOn(service['vnfErrorRepository'], 'findOne').mockResolvedValue(null);

      const result = await service.enviarEmailDivergencia(999);

      expect(result.success).toBe(false);
      expect(result.message).toBe('Erro não encontrado');
    });

    it('deve falhar ao enviar email quando fornecedor não tem email', async () => {
      const mockErro = {
        id: 1,
        numeroNota: '45236',
        fornecedorEmail: null,
        dataEmailEnviado: null
      };

      jest.spyOn(service['vnfErrorRepository'], 'findOne').mockResolvedValue(mockErro as any);

      const result = await service.enviarEmailDivergencia(1);

      expect(result.success).toBe(false);
      expect(result.message).toBe('Email do fornecedor não encontrado');
    });

    it('deve falhar ao enviar email quando já foi enviado anteriormente', async () => {
      const mockErro = {
        id: 1,
        numeroNota: '45236',
        fornecedorEmail: '<EMAIL>',
        dataEmailEnviado: new Date()
      };

      jest.spyOn(service['vnfErrorRepository'], 'findOne').mockResolvedValue(mockErro as any);

      const result = await service.enviarEmailDivergencia(1);

      expect(result.success).toBe(false);
      expect(result.message).toBe('Email já foi enviado anteriormente');
    });

    it('deve enviar emails em lote com sucesso', async () => {
      const mockErro1 = {
        id: 1,
        numeroNota: '45236',
        fornecedorEmail: '<EMAIL>',
        dataEmailEnviado: null,
        tipoErro: 'NF Emitida Incoerente! Quantidade do item 1 excede o pedido. Pedido: 100, NF: 120'
      };

      const mockErro2 = {
        id: 2,
        numeroNota: '45237',
        fornecedorEmail: '<EMAIL>',
        dataEmailEnviado: null,
        tipoErro: 'NF Emitida Incoerente! Valor unitário do item 1 divergente. Pedido: 10.50, NF: 12.00'
      };

      jest.spyOn(service['vnfErrorRepository'], 'findOne')
        .mockResolvedValueOnce(mockErro1 as any)
        .mockResolvedValueOnce(mockErro2 as any);
      
      jest.spyOn(service['vnfErrorRepository'], 'save')
        .mockResolvedValueOnce(mockErro1 as any)
        .mockResolvedValueOnce(mockErro2 as any);

      const result = await service.enviarEmailsEmLote([1, 2]);

      expect(result.success).toBe(true);
      expect(result.results).toHaveLength(2);
      expect(result.results[0].success).toBe(true);
      expect(result.results[1].success).toBe(true);
    });

    it('deve gerar conteúdo de email correto para erro de quantidade', () => {
      const mockErro = {
        numeroNota: '45236',
        tipoErro: 'NF Emitida Incoerente! Quantidade do item 1 excede o pedido. Pedido: 100, NF: 120'
      };

      const conteudo = service['gerarConteudoEmail'](mockErro as any);

      expect(conteudo).toContain('Nota Fiscal nº 45236');
      expect(conteudo).toContain('Quantidade informada: 120 peças');
      expect(conteudo).toContain('Quantidade correta: 100 peças');
      expect(conteudo).toContain('<EMAIL>');
      expect(conteudo).toContain('24 horas');
    });

    it('deve gerar conteúdo de email correto para erro de valor unitário', () => {
      const mockErro = {
        numeroNota: '45236',
        tipoErro: 'NF Emitida Incoerente! Valor unitário do item 1 divergente. Pedido: 10.50, NF: 12.00'
      };

      const conteudo = service['gerarConteudoEmail'](mockErro as any);

      expect(conteudo).toContain('Valor unitário informado: R$ 12.00');
      expect(conteudo).toContain('Valor unitário correto: R$ 10.50');
    });

    it('deve extrair detalhes do erro corretamente', () => {
      const erroQuantidade = 'NF Emitida Incoerente! Quantidade do item 1 excede o pedido. Pedido: 100, NF: 120';
      const detalhesQuantidade = service['extrairDetalhesDoErro'](erroQuantidade);
      
      expect(detalhesQuantidade).toContain('Quantidade informada: 120 peças');
      expect(detalhesQuantidade).toContain('Quantidade correta: 100 peças');

      const erroValor = 'NF Emitida Incoerente! Valor unitário do item 1 divergente. Pedido: 10.50, NF: 12.00';
      const detalhesValor = service['extrairDetalhesDoErro'](erroValor);
      
      expect(detalhesValor).toContain('Valor unitário informado: R$ 12.00');
      expect(detalhesValor).toContain('Valor unitário correto: R$ 10.50');
    });
  });
}); 