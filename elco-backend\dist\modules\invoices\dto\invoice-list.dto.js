"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InvoiceErrorListResponseDto = exports.InvoiceListResponseDto = exports.InvoiceDto = exports.InvoiceErrorDto = exports.InvoiceItemDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class InvoiceItemDto {
    id;
}
exports.InvoiceItemDto = InvoiceItemDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Number)
], InvoiceItemDto.prototype, "id", void 0);
class InvoiceErrorDto {
    id;
    errorMessage;
    data;
    xmlEnviado;
    soapEnvelope;
    idRomaneio;
    idOrdem;
}
exports.InvoiceErrorDto = InvoiceErrorDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Number)
], InvoiceErrorDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], InvoiceErrorDto.prototype, "errorMessage", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Date)
], InvoiceErrorDto.prototype, "data", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", String)
], InvoiceErrorDto.prototype, "xmlEnviado", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", String)
], InvoiceErrorDto.prototype, "soapEnvelope", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Number)
], InvoiceErrorDto.prototype, "idRomaneio", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Number)
], InvoiceErrorDto.prototype, "idOrdem", void 0);
class InvoiceDto {
    id;
    filInCodigo;
    notInCodigo;
    notInNumero;
    tpdInCodigo;
    notDtEmissao;
    notHrHoraemissao;
    notStChaveacesso;
    notStCgc;
    notStIncrestadual;
    notStMunicipio;
    notStUf;
    items;
    errors;
    idRomaneio;
    idOrdem;
}
exports.InvoiceDto = InvoiceDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Number)
], InvoiceDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Number)
], InvoiceDto.prototype, "filInCodigo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Number)
], InvoiceDto.prototype, "notInCodigo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], InvoiceDto.prototype, "notInNumero", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Number)
], InvoiceDto.prototype, "tpdInCodigo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Date)
], InvoiceDto.prototype, "notDtEmissao", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], InvoiceDto.prototype, "notHrHoraemissao", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], InvoiceDto.prototype, "notStChaveacesso", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], InvoiceDto.prototype, "notStCgc", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], InvoiceDto.prototype, "notStIncrestadual", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], InvoiceDto.prototype, "notStMunicipio", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], InvoiceDto.prototype, "notStUf", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [InvoiceItemDto] }),
    __metadata("design:type", Array)
], InvoiceDto.prototype, "items", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [InvoiceErrorDto] }),
    __metadata("design:type", Array)
], InvoiceDto.prototype, "errors", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Number)
], InvoiceDto.prototype, "idRomaneio", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Number)
], InvoiceDto.prototype, "idOrdem", void 0);
class InvoiceListResponseDto {
    invoices;
    total;
}
exports.InvoiceListResponseDto = InvoiceListResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: [InvoiceDto] }),
    __metadata("design:type", Array)
], InvoiceListResponseDto.prototype, "invoices", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Number)
], InvoiceListResponseDto.prototype, "total", void 0);
class InvoiceErrorListResponseDto {
    errors;
    total;
}
exports.InvoiceErrorListResponseDto = InvoiceErrorListResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: [InvoiceErrorDto] }),
    __metadata("design:type", Array)
], InvoiceErrorListResponseDto.prototype, "errors", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Number)
], InvoiceErrorListResponseDto.prototype, "total", void 0);
//# sourceMappingURL=invoice-list.dto.js.map