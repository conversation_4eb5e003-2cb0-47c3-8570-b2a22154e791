"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var SyncService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SyncService = void 0;
const common_1 = require("@nestjs/common");
const schedule_1 = require("@nestjs/schedule");
const axios_1 = require("axios");
const separation_orders_service_1 = require("../separation-orders/separation-orders.service");
let SyncService = SyncService_1 = class SyncService {
    orderService;
    logger = new common_1.Logger(SyncService_1.name);
    constructor(orderService) {
        this.orderService = orderService;
    }
    async fetchAndSaveOrders() {
        this.logger.log('🔄 Fetching separation orders...');
        try {
            const timeout = new Promise((_, reject) => setTimeout(() => reject(new Error('Timeout na sincronização')), 15000));
            const syncOperation = async () => {
                const allOrders = [];
                let page = 1;
                const pageSize = 100;
                while (true) {
                    const res = await axios_1.default.get('https://apigateway.smartgo.com.br/expedicao', {
                        params: {
                            Page: page,
                            PageSize: pageSize,
                            Status: 'PEDIDO_EM_ATENDIMENTO',
                            subStatus: 'SEPARADO',
                        },
                        headers: {
                            Accept: 'application/json',
                            api_key: process.env.WMS_API_KEY,
                        },
                        timeout: 10000,
                    });
                    const data = res.data?.model?.items ?? [];
                    allOrders.push(...data);
                    if (data.length < pageSize)
                        break;
                    page++;
                }
                const mapped = allOrders.map((o) => ({
                    internalCode: o.codigoInterno,
                    depositorName: o.nomeDepositante,
                    orderDate: new Date(o.dataDoPedido),
                    status: 'PENDING',
                    externalCode: o.codigoExterno,
                    observation: o.observacao ?? '',
                    environment: o.ambienteDeGeracao ?? '',
                    typeOfBond: '',
                }));
                const result = await this.orderService.saveNew(mapped);
                this.logger.log(`✅ ${result.length} new separation orders saved.`);
            };
            await Promise.race([syncOperation(), timeout]);
        }
        catch (error) {
            this.logger.error('❌ Erro na sincronização:', error.message);
        }
    }
};
exports.SyncService = SyncService;
__decorate([
    (0, schedule_1.Cron)('*/60 * * * * *'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], SyncService.prototype, "fetchAndSaveOrders", null);
exports.SyncService = SyncService = SyncService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [separation_orders_service_1.SeparationOrderService])
], SyncService);
//# sourceMappingURL=sync.service.js.map