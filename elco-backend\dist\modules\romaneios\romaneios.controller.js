"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RomaneiosController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const romaneios_service_1 = require("./romaneios.service");
const create_romaneio_dto_1 = require("./dto/create-romaneio.dto");
const update_romaneio_dto_1 = require("./dto/update-romaneio.dto");
let RomaneiosController = class RomaneiosController {
    romaneiosService;
    constructor(romaneiosService) {
        this.romaneiosService = romaneiosService;
    }
    async create(createRomaneioDto) {
        return await this.romaneiosService.create(createRomaneioDto);
    }
    findAll() {
        return this.romaneiosService.findAll();
    }
    findOne(id) {
        return this.romaneiosService.findOne(+id);
    }
    update(id, updateRomaneioDto) {
        return this.romaneiosService.update(+id, updateRomaneioDto);
    }
    remove(id) {
        return this.romaneiosService.deleteRomaneio(+id);
    }
    async saveDraft(draftData) {
        return await this.romaneiosService.saveDraft(draftData);
    }
    async getDraftByExternalCode(externalCode) {
        return await this.romaneiosService.getDraftByExternalCode(externalCode);
    }
    async calcularPesoRomaneio(id) {
        return await this.romaneiosService.calcularPesoRomaneio(+id);
    }
};
exports.RomaneiosController = RomaneiosController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Criar novo romaneio',
        description: 'Cria um novo romaneio de carga no sistema.',
    }),
    (0, swagger_1.ApiBody)({
        type: create_romaneio_dto_1.CreateRomaneioDto,
        description: 'Dados do novo romaneio',
        examples: {
            create: {
                summary: 'Exemplo de romaneio',
                value: {
                    externalCode: 'ROM001',
                    driverId: 'uuid-driver',
                    vehicleId: 1,
                    transporterId: 'uuid-transporter',
                    addressId: 1,
                    scheduledDate: '2024-01-15T10:00:00Z',
                    observations: 'Entrega prioritária',
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Romaneio criado com sucesso',
        schema: {
            type: 'object',
            properties: {
                id: { type: 'number', example: 1 },
                externalCode: { type: 'string', example: 'ROM001' },
                status: { type: 'string', example: 'PENDING' },
                scheduledDate: { type: 'string', format: 'date-time' },
                createdAt: { type: 'string', format: 'date-time' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dados inválidos' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_romaneio_dto_1.CreateRomaneioDto]),
    __metadata("design:returntype", Promise)
], RomaneiosController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Listar todos os romaneios',
        description: 'Retorna uma lista com todos os romaneios cadastrados.',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de romaneios',
        schema: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    id: { type: 'number', example: 1 },
                    externalCode: { type: 'string', example: 'ROM001' },
                    status: { type: 'string', example: 'PENDING' },
                    scheduledDate: { type: 'string', format: 'date-time' },
                    driver: {
                        type: 'object',
                        properties: {
                            id: { type: 'string', example: 'uuid-driver' },
                            name: { type: 'string', example: 'João Silva' },
                        },
                    },
                    vehicle: {
                        type: 'object',
                        properties: {
                            id: { type: 'number', example: 1 },
                            plate: { type: 'string', example: 'ABC-1234' },
                        },
                    },
                    address: {
                        type: 'object',
                        properties: {
                            id: { type: 'number', example: 1 },
                            name: { type: 'string', example: 'Endereço Principal' },
                        },
                    },
                },
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], RomaneiosController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Obter romaneio por ID',
        description: 'Retorna os detalhes completos de um romaneio específico.',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID do romaneio',
        type: 'string',
        example: '1',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Dados do romaneio',
        schema: {
            type: 'object',
            properties: {
                id: { type: 'number', example: 1 },
                externalCode: { type: 'string', example: 'ROM001' },
                status: { type: 'string', example: 'PENDING' },
                scheduledDate: { type: 'string', format: 'date-time' },
                observations: { type: 'string', example: 'Entrega prioritária' },
                driver: { type: 'object' },
                vehicle: { type: 'object' },
                transporter: { type: 'object' },
                address: { type: 'object' },
                orders: { type: 'array', items: { type: 'object' } },
                createdAt: { type: 'string', format: 'date-time' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Romaneio não encontrado' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], RomaneiosController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Atualizar romaneio',
        description: 'Atualiza os dados de um romaneio existente.',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID do romaneio',
        type: 'string',
        example: '1',
    }),
    (0, swagger_1.ApiBody)({ type: update_romaneio_dto_1.UpdateRomaneioDto }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Romaneio atualizado com sucesso',
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Romaneio não encontrado' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dados inválidos' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_romaneio_dto_1.UpdateRomaneioDto]),
    __metadata("design:returntype", void 0)
], RomaneiosController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Excluir romaneio completamente',
        description: 'Remove um romaneio do sistema, excluindo permanentemente todos os produtos relacionados, romaneio_orders e atualizando os separation_orders para status PENDING.',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID do romaneio',
        type: 'string',
        example: '1',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Romaneio excluído com sucesso',
        schema: {
            type: 'object',
            properties: {
                message: { type: 'string', example: 'Romaneio excluído com sucesso' },
                deletedProducts: {
                    type: 'boolean',
                    example: true,
                    description: 'Produtos excluídos',
                },
                deletedRomaneioOrders: {
                    type: 'number',
                    example: 3,
                    description: 'Quantidade de romaneio_orders excluídos',
                },
                updatedSeparationOrders: {
                    type: 'number',
                    example: 2,
                    description: 'Quantidade de separation_orders atualizados',
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Romaneio não encontrado' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Erro ao excluir romaneio' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], RomaneiosController.prototype, "remove", null);
__decorate([
    (0, common_1.Post)('rascunho'),
    (0, swagger_1.ApiOperation)({
        summary: 'Salvar rascunho de romaneio',
        description: 'Salva um rascunho de romaneio para posterior finalização.',
    }),
    (0, swagger_1.ApiBody)({
        description: 'Dados do rascunho',
        schema: {
            type: 'object',
            properties: {
                externalCode: { type: 'string', example: 'DRAFT001' },
                data: { type: 'object', description: 'Dados do rascunho' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Rascunho salvo com sucesso',
        schema: {
            type: 'object',
            properties: {
                id: { type: 'string', example: 'uuid-draft' },
                externalCode: { type: 'string', example: 'DRAFT001' },
                data: { type: 'object' },
                createdAt: { type: 'string', format: 'date-time' },
            },
        },
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], RomaneiosController.prototype, "saveDraft", null);
__decorate([
    (0, common_1.Get)('rascunho/:externalCode'),
    (0, swagger_1.ApiOperation)({
        summary: 'Obter rascunho por código externo',
        description: 'Retorna um rascunho de romaneio pelo código externo.',
    }),
    (0, swagger_1.ApiParam)({
        name: 'externalCode',
        description: 'Código externo do rascunho',
        type: 'string',
        example: 'DRAFT001',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Dados do rascunho',
        schema: {
            type: 'object',
            properties: {
                id: { type: 'number', example: 1 },
                externalCode: { type: 'string', example: 'DRAFT001' },
                data: { type: 'object', description: 'Dados do rascunho' },
                createdAt: { type: 'string', format: 'date-time' },
                updatedAt: { type: 'string', format: 'date-time' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Rascunho não encontrado' }),
    __param(0, (0, common_1.Param)('externalCode')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], RomaneiosController.prototype, "getDraftByExternalCode", null);
__decorate([
    (0, common_1.Get)(':id/peso'),
    (0, swagger_1.ApiOperation)({
        summary: 'Calcular peso do romaneio',
        description: 'Calcula o peso líquido e bruto do romaneio da mesma forma que é feito para a nota fiscal.',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID do romaneio',
        type: 'number',
        example: 1,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Peso calculado do romaneio',
        schema: {
            type: 'object',
            properties: {
                pesoLiquido: {
                    type: 'number',
                    example: 150.5,
                    description: 'Peso líquido em kg',
                },
                pesoBruto: {
                    type: 'number',
                    example: 180.2,
                    description: 'Peso bruto em kg',
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Romaneio não encontrado' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], RomaneiosController.prototype, "calcularPesoRomaneio", null);
exports.RomaneiosController = RomaneiosController = __decorate([
    (0, swagger_1.ApiTags)('Romaneios'),
    (0, common_1.Controller)('romaneios'),
    __metadata("design:paramtypes", [romaneios_service_1.RomaneiosService])
], RomaneiosController);
//# sourceMappingURL=romaneios.controller.js.map