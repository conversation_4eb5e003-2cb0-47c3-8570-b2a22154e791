import { Injectable, Inject, forwardRef, Logger } from '@nestjs/common';
import { parseStringPromise } from 'xml2js';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, IsNull } from 'typeorm';
import { <PERSON>ron } from '@nestjs/schedule';
import axios from 'axios';
import * as https from 'https';
import * as fs from 'fs';
import * as path from 'path';
import * as zlib from 'zlib';
import { promisify } from 'util';
import { Invoice } from '../invoices/entities/invoice.entity';
import { InvoiceItem } from '../invoices/entities/invoice-item.entity';
import { Product } from '../products/entities/product.entity';
import { VnfError } from './vnf-error.entity';
import { VnfConsultaProcessada } from './entities/vnf-consulta-processada.entity';
import { SeparationOrder } from '../separation-orders/entity/separation-order.entity';
import { <PERSON><PERSON>o } from '../romaneios/entities/romaneio.entity';
import { RomaneioOrder } from '../romaneio-orders/entities/romaneio-order.entity';
import { SupplierEmail } from '../supplier-email/entity/supplier-email.entity';
import { OracleService } from '../oracle/oracle.service';

import { EmailService } from './email.service';
import { PedidoPortal, NotaFiscal } from './interfaces';

@Injectable()
export class VnfService {
    private readonly logger = new Logger(VnfService.name);
    private isProcessingCron = false;
    private lastCronExecution: Date | null = null;
    
    private tokenPortalCache: string | null = null;
    private tokenPortalExpiry: Date | null = null;
    private lastTokenRequest: Date | null = null;

    constructor(
        @InjectRepository(Invoice)
        private readonly invoiceRepository: Repository<Invoice>,
        @InjectRepository(InvoiceItem)
        private readonly invoiceItemRepository: Repository<InvoiceItem>,
        @InjectRepository(Product)
        private readonly productRepository: Repository<Product>,
        @InjectRepository(VnfError)
        private readonly vnfErrorRepository: Repository<VnfError>,
        @InjectRepository(VnfConsultaProcessada)
        private readonly vnfConsultaProcessadaRepository: Repository<VnfConsultaProcessada>,
        @InjectRepository(SeparationOrder)
        private readonly separationOrderRepository: Repository<SeparationOrder>,
        @InjectRepository(Romaneio)
        private readonly romaneioRepository: Repository<Romaneio>,
        @InjectRepository(RomaneioOrder)
        private readonly romaneioOrderRepository: Repository<RomaneioOrder>,
        @InjectRepository(SupplierEmail)
        private readonly supplierEmailRepository: Repository<SupplierEmail>,
        
        private readonly emailService: EmailService,
        private readonly oracleService: OracleService,
    ) { }

    private async obterTokenPortalCompras(): Promise<string | null> {
        try {
            const agora = new Date();
            
            if (this.tokenPortalCache && this.tokenPortalExpiry && agora < this.tokenPortalExpiry) {
                console.log('[VnfService] 💾 Usando token do portal em cache');
                return this.tokenPortalCache;
            }

            if (this.lastTokenRequest) {
                const timeSinceLastRequest = agora.getTime() - this.lastTokenRequest.getTime();
                const minInterval = 3000;
                
                if (timeSinceLastRequest < minInterval) {
                    const waitTime = minInterval - timeSinceLastRequest;
                    console.log(`[VnfService] ⏳ Aguardando ${waitTime}ms antes de solicitar novo token`);
                    await this.delay(waitTime);
                }
            }

            console.log('[VnfService] 🔑 Solicitando novo token do portal de compras');
            this.lastTokenRequest = new Date();

            const response = await axios.post('https://wsprd.portaldecompras.co/API/v1/Token', {
                usuario: process.env.PORTAL_COMPRAS_USUARIO || 'usuario',
                Senha: process.env.PORTAL_COMPRAS_SENHA || 'senha'
            }, {
                headers: {
                    'Content-Type': 'application/json'
                },
                timeout: 15000
            });

            if (response.data && response.data.token) {
                this.tokenPortalCache = response.data.token;
                this.tokenPortalExpiry = new Date(Date.now() + 25 * 60 * 1000);
                
                console.log('[VnfService] ✅ Token obtido e armazenado em cache por 25 minutos');
                return response.data.token;
            }

            console.error('[VnfService] Token não encontrado na resposta:', response.data);
            return null;
        } catch (error) {
            console.error('[VnfService] Erro ao obter token do portal de compras:', error);
            
            this.tokenPortalCache = null;
            this.tokenPortalExpiry = null;
            
            return null;
        }
    }

    private async consultarPedidoPortalCompras(numeroPedido: string, token: string): Promise<{
        success: boolean;
        data?: PedidoPortal;
        error?: string;
    }> {
        try {
            await this.delay(1000);
            
            console.log(`[VnfService] 🔍 Consultando pedido ${numeroPedido} no portal de compras`);

            const response = await axios.get(`https://wsprd.portaldecompras.co/api/v1/Pedido/ConsultaPorCodigoERP/?codigoERP=${numeroPedido}`, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                timeout: 15000
            });

            return {
                success: true,
                data: response.data
            };
        } catch (error: any) {
            console.error('[VnfService] Erro ao consultar pedido no portal de compras:', error);
            
            if (error.response && error.response.status === 404) {
                const errorMessage = error.response.data?.mensagem || error.response.data?.message;
                if (errorMessage && errorMessage.includes('não localizado no portal de compras')) {
                    return {
                        success: false,
                        error: `Pedido ${numeroPedido} não localizado no portal de compras. Verifique se o número do pedido está correto ou se o pedido ainda está ativo.`
                    };
                }
            }

            if (error.response && error.response.status === 429) {
                console.warn(`[VnfService] ⚠️ Rate limit atingido no portal de compras para pedido ${numeroPedido}`);
                
                await this.delay(5000);
                
                return {
                    success: false,
                    error: 'Rate limit atingido no portal de compras. Tente novamente mais tarde.'
                };
            }

            if (error.response && error.response.status === 401) {
                console.warn(`[VnfService] 🔑 Token inválido, limpando cache`);
                
                this.tokenPortalCache = null;
                this.tokenPortalExpiry = null;
                
                return {
                    success: false,
                    error: 'Token do portal de compras expirado. Será renovado na próxima consulta.'
                };
            }

            return {
                success: false,
                error: `Erro ao consultar pedido: ${error.message}`
            };
        }
    }

    private extrairNumeroPedido(infCpl: string): { numero: string | null; erro?: string } {
        if (!infCpl) return { numero: null, erro: 'Número do pedido não encontrado nas observações da NF.' };

        const padraoPedido1 = /PEDIDO:\s*(\d+)\/(\d{5,})/i;
        const matchPedido1 = infCpl.match(padraoPedido1);
        if (matchPedido1 && matchPedido1[2]) {
            console.log(`[VnfService] Número do pedido encontrado via padrão 1: ${matchPedido1[2]} (de ${matchPedido1[1]}/${matchPedido1[2]})`);
            return { numero: matchPedido1[2] };
        }

        const padraoPedido2 = /(?:PEDIDO\s*(?:N\.?\s*)?(\d{5,}))/i;
        const matchPedido2 = infCpl.match(padraoPedido2);
        if (matchPedido2 && matchPedido2[1]) {
            console.log(`[VnfService] Número do pedido encontrado via padrão 2: ${matchPedido2[1]}`);
            return { numero: matchPedido2[1] };
        }

        const padraoPedido3 = /PEDIDO:\s*(\d{5,})/i;
        const matchPedido3 = infCpl.match(padraoPedido3);
        if (matchPedido3 && matchPedido3[1]) {
            console.log(`[VnfService] Número do pedido encontrado via padrão 3: ${matchPedido3[1]}`);
            return { numero: matchPedido3[1] };
        }

        const padraoPedido4 = /PEDIDO\s+(\d+)\/(\d{5,})/i;
        const matchPedido4 = infCpl.match(padraoPedido4);
        if (matchPedido4 && matchPedido4[2]) {
            console.log(`[VnfService] Número do pedido encontrado via padrão 4: ${matchPedido4[2]} (de ${matchPedido4[1]}/${matchPedido4[2]})`);
            return { numero: matchPedido4[2] };
        }

        const padraoPedido5 = /PEDIDO:\s*(\d{1,4})\/(\d{5,})/i;
        const matchPedido5 = infCpl.match(padraoPedido5);
        if (matchPedido5 && matchPedido5[2]) {
            console.log(`[VnfService] Número do pedido encontrado via padrão 5: ${matchPedido5[2]} (de ${matchPedido5[1]}/${matchPedido5[2]})`);
            return { numero: matchPedido5[2] };
        }

        const linhas = infCpl.split('\n');
        for (const linha of linhas) {
            const numerosLinha = linha.match(/(\d{5,})/g);
            if (numerosLinha) {
                for (const numero of numerosLinha) {
                    if (numero.length === 8 && /^\d{8}$/.test(numero)) {
                        continue;
                    }
                    
                    if (numero.length >= 10 && numero.length <= 11 && /^\d+$/.test(numero)) {
                        continue;
                    }

                    if (numero.length === 14 && /^\d{14}$/.test(numero)) {
                        continue;
                    }

                    if (numero.length === 11 && /^\d{11}$/.test(numero)) {
                        continue;
                    }

                    console.log(`[VnfService] Número do pedido encontrado via análise de linha: ${numero}`);
                    return { numero: numero };
                }
            }
        }


        const regexNumeros = /(\d{5,})/g;
        const numeros = infCpl.match(regexNumeros);
        
        if (!numeros || numeros.length === 0) {
            return { numero: null, erro: 'Número do pedido não encontrado nas observações da NF.' };
        }

        const numerosUnicos = [...new Set(numeros)].filter(numero => {
            if (numero.length === 8 && /^\d{8}$/.test(numero)) {
                return false;
            }
            if (numero.length >= 10 && numero.length <= 11 && /^\d+$/.test(numero)) {
                return false;
            }
            if (numero.length === 14 && /^\d{14}$/.test(numero)) {
                return false;
            }
            if (numero.length === 11 && /^\d{11}$/.test(numero)) {
                return false;
            }
            return true;
        });

        if (numerosUnicos.length === 0) {
            return { numero: null, erro: 'Número do pedido não encontrado nas observações da NF.' };
        }

        if (numerosUnicos.length === 1) {
            return { numero: numerosUnicos[0] };
        }

        console.log(`[VnfService] Múltiplos números encontrados após filtro, usando o primeiro: ${numerosUnicos[0]}`);
        return { numero: numerosUnicos[0] };
    }

    private async buscarEmailFornecedor(nomeFornecedor: string): Promise<string | null> {
        if (!nomeFornecedor) return null;

        try {
            const nomeNormalizado = nomeFornecedor.trim().toUpperCase();
            
            const fornecedor = await this.supplierEmailRepository
                .createQueryBuilder('supplier')
                .where('UPPER(supplier.supplierName) LIKE :nome', { nome: `%${nomeNormalizado}%` })
                .getOne();

            if (fornecedor) {
                console.log(`[VnfService] Email do fornecedor encontrado: ${fornecedor.email} para ${nomeFornecedor}`);
                return fornecedor.email;
            }

            console.log(`[VnfService] Email do fornecedor não encontrado para: ${nomeFornecedor}`);
            return null;
        } catch (error) {
            console.error('[VnfService] Erro ao buscar email do fornecedor:', error);
            return null;
        }
    }

    private async consultarXmlSefaz(chaveAcesso: string): Promise<{
        success: boolean;
        status?: string;
        message?: string;
        xml?: string | null;
        pdfBase64?: string | null;
    }> {
        const pfxPath = path.resolve(
            __dirname,
            '../../../certs/ELCO_ENGENHARIA_LTDA_77521375000121.pfx',
        );

        const gunzip = promisify(zlib.gunzip);

        const httpsAgent = new https.Agent({
            pfx: fs.readFileSync(pfxPath),
            passphrase: process.env.PFX_PASSPHRASE,
        });

        const envelope = `<?xml version="1.0" encoding="UTF-8"?>
<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Header/>
    <soap:Body>
        <nfeDistDFeInteresse xmlns="http://www.portalfiscal.inf.br/nfe/wsdl/NFeDistribuicaoDFe">
            <nfeDadosMsg>
                <distDFeInt versao="1.01" xmlns="http://www.portalfiscal.inf.br/nfe">
                    <tpAmb>1</tpAmb>
                    <CNPJ>77521375000121</CNPJ>
                    <consChNFe>
                        <chNFe>${chaveAcesso}</chNFe>
                    </consChNFe>
                </distDFeInt>
            </nfeDadosMsg>
        </nfeDistDFeInteresse>
    </soap:Body>
</soap:Envelope>`;

        const url = 'https://www1.nfe.fazenda.gov.br/NFeDistribuicaoDFe/NFeDistribuicaoDFe.asmx';

        try {
            const { data } = await axios.post(url, envelope, {
                httpsAgent,
                headers: {
                    'Content-Type': 'text/xml;charset=utf-8',
                    SOAPAction:
                        'http://www.portalfiscal.inf.br/nfe/wsdl/NFeDistribuicaoDFe/nfeDistDFeInteresse',
                },
                timeout: 30000,
            });

            const parsed = await parseStringPromise(data, { explicitArray: false });
            const retDist =
                parsed['soap:Envelope']['soap:Body']['nfeDistDFeInteresseResponse']['nfeDistDFeInteresseResult']['retDistDFeInt'];

            const cStat = retDist.cStat;
            const xMotivo = retDist.xMotivo;

            if (cStat !== '138') {
                return {
                    success: false,
                    status: cStat,
                    message: xMotivo,
                    xml: null,
                    pdfBase64: null,
                };
            }

            const rawDocZip = retDist?.loteDistDFeInt?.docZip;
            const docZipArr = Array.isArray(rawDocZip) ? rawDocZip : rawDocZip ? [rawDocZip] : [];

            let procNfeXml: string | null = null;

            for (const item of docZipArr) {
                try {
                    const base64 = typeof item === 'string' ? item : item._;
                    const xml = (await gunzip(Buffer.from(base64, 'base64'))).toString('utf-8');
                    if (xml.includes('<nfeProc')) {
                        procNfeXml = xml;
                        break;
                    }
                } catch {
                    continue;
                }
            }

            if (!procNfeXml) {
                return {
                    success: true,
                    status: cStat,
                    message: 'NF-e não encontrada no lote.',
                    xml: null,
                    pdfBase64: null,
                };
            }

            return {
                success: true,
                status: cStat,
                message: xMotivo,
                xml: procNfeXml,
                pdfBase64: null,
            };

        } catch (error) {
            console.error('[VnfService] Erro ao consultar XML na SEFAZ:', error);
            return {
                success: false,
                message: `Erro ao consultar XML na SEFAZ: ${error.message}`,
            };
        }
    }

    async consultarXmlPorChave(chaveAcesso: string): Promise<any> {
        try {
            if (!chaveAcesso || chaveAcesso.length !== 44) {
                return {
                    success: false,
                    message: 'Chave de acesso inválida. Deve conter 44 dígitos.',
                };
            }

            const resultado = await this.consultarXmlSefaz(chaveAcesso);

            if (!resultado.success) {
                return {
                    success: false,
                    message: resultado.message,
                    status: resultado.status,
                };
            }

            if (resultado.xml) {
                try {
                    const xmlJson = await parseStringPromise(resultado.xml, { explicitArray: false });
                    const notaFiscal = this.mapearCampos(xmlJson);

                    let dadosPedido: { success: boolean; data?: PedidoPortal; error?: string } | null = null;
                    const resultadoExtracao = this.extrairNumeroPedido(notaFiscal.informacoesAdicionais?.infCpl);
                    
                    if (resultadoExtracao.numero) {
                        console.log(`[VnfService] Número do pedido extraído: ${resultadoExtracao.numero}`);
                        
                        const token = await this.obterTokenPortalCompras();
                        if (token) {
                            dadosPedido = await this.consultarPedidoPortalCompras(resultadoExtracao.numero, token);
                            
                            if (dadosPedido && dadosPedido.success && dadosPedido.data) {
                                console.log('[VnfService] Iniciando comparação entre pedido e nota fiscal');
                                const resultadoComparacao = await this.processarComparacaoPedidoNotaFiscal(
                                    dadosPedido.data,
                                    notaFiscal,
                                    notaFiscal.numeroNota,
                                    resultadoExtracao.numero
                                );
                                
                                if (!resultadoComparacao.success) {
                                    console.log(`[VnfService] Encontradas ${resultadoComparacao.errors.length} divergências na comparação`);
                                    dadosPedido.error = `Encontradas ${resultadoComparacao.errors.length} divergências na comparação`;
                                } else {
                                    console.log('[VnfService] Nota fiscal aprovada na comparação com o pedido');
                                }
                            }
                        } else {
                            console.error('[VnfService] Não foi possível obter token do portal de compras');
                        }
                    } else {
                        console.log('[VnfService] Número do pedido não encontrado no XML');
                    }

                    return {
                        success: true,
                        message: 'XML consultado com sucesso',
                        xml: resultado.xml,
                        pdfBase64: resultado.pdfBase64,
                        notaFiscal: notaFiscal,
                        chaveAcesso: chaveAcesso,
                        numeroPedido: resultadoExtracao.numero,
                        dadosPedido: dadosPedido
                    };
                } catch (parseError) {
                    console.error('[VnfService] Erro ao processar XML:', parseError);
                    return {
                        success: true,
                        message: 'XML consultado, mas houve erro no processamento',
                        xml: resultado.xml,
                        pdfBase64: resultado.pdfBase64,
                        chaveAcesso: chaveAcesso,
                    };
                }
            }

            return {
                success: false,
                message: 'XML não encontrado para a chave de acesso informada',
            };

        } catch (error) {
            console.error('[VnfService] Erro ao consultar XML por chave:', error);
            return {
                success: false,
                message: `Erro ao consultar XML: ${error.message}`,
            };
        }
    }

    async processarXmlNfe(params: {
        xml: string;
        numeroNota: string;
        idNota: number;
        idOrdem: number;
        idRomaneio: number;
        chaveAcesso: string;
        status: string;
    }): Promise<any> {
        const { xml, ...rest } = params;
        const xmlJson = await parseStringPromise(xml, { explicitArray: false });
        const friendlyJson = this.mapearCampos(xmlJson);
        const resultadoVerificacao = await this.verificarNotaFiscal({
            ...rest,
            notaFiscal: friendlyJson,
        });
        return {
            ...rest,
            notaFiscal: friendlyJson,
            resultadoVerificacao,
        };
    }

    private mapearCampos(xmlJson: any): any {
        const proc = xmlJson.nfeProc || xmlJson['nfeProc'];
        if (!proc) return xmlJson;
        const nfe = proc.NFe || proc['NFe'];
        const protNFe = proc.protNFe || proc['protNFe'];
        const infNFe = nfe?.infNFe || nfe?.['infNFe'];
        const ide = infNFe?.ide;
        const emit = infNFe?.emit;
        const dest = infNFe?.dest;
        const produtos = Array.isArray(infNFe?.det) ? infNFe.det : [infNFe?.det];
        return {
            versao: proc.$?.versao,
            numeroNota: ide?.nNF,
            dataEmissao: ide?.dhEmi,
            naturezaOperacao: ide?.natOp,
            modelo: ide?.mod,
            serie: ide?.serie,
            emitente: {
                cnpj: emit?.CNPJ,
                nome: emit?.xNome,
                fantasia: emit?.xFant,
                endereco: emit?.enderEmit,
                inscricaoEstadual: emit?.IE,
                regimeTributario: emit?.CRT,
                email: emit?.email,
            },
            destinatario: {
                cnpj: dest?.CNPJ,
                nome: dest?.xNome,
                endereco: dest?.enderDest,
                inscricaoEstadual: dest?.IE,
                indicadorIE: dest?.indIEDest,
                email: dest?.email,
            },
            produtos: produtos?.map((item: any) => ({
                numeroItem: item.$?.nItem,
                codigo: item.prod?.cProd,
                descricao: item.prod?.xProd,
                ncm: item.prod?.NCM,
                cfop: item.prod?.CFOP,
                unidade: item.prod?.uCom,
                quantidade: item.prod?.qCom,
                valorUnitario: item.prod?.vUnCom,
                valorTotal: item.prod?.vProd,
                ean: item.prod?.cEAN,
                volume: item.prod?.volume,
            })),
            total: infNFe?.total?.ICMSTot,
            transporte: infNFe?.transp,
            pagamento: infNFe?.pag,
            informacoesAdicionais: infNFe?.infAdic,
            protocolo: protNFe?.infProt,
        };
    }

    private async verificarNotaFiscal(params: {
        numeroNota: string;
        idNota: number;
        idOrdem: number;
        idRomaneio: number;
        chaveAcesso: string;
        status: string;
        notaFiscal: any;
    }): Promise<any> {
        const { idNota, idOrdem, idRomaneio, notaFiscal } = params;

        const invoice = await this.invoiceRepository.findOne({ where: { id: idNota } });
        const separationOrder = await this.separationOrderRepository.findOne({ where: { id: idOrdem } });
        const romaneio = await this.romaneioRepository.findOne({ where: { id: idRomaneio } });
        const romaneioOrder = await this.romaneioOrderRepository.findOne({
            where: { separationOrderId: idOrdem, romaneioId: idRomaneio },
        });
        const invoiceItems = await this.invoiceItemRepository.find({ where: { invoiceId: idNota } });

        const erros: string[] = [];

        const validarCampo = (campo: string, valorSistema: any, valorXml: any) => {
            const v1 = valorSistema ?? '';
            const v2 = valorXml ?? '';

            if (!v1 && v2) {
                erros.push(`Campo ${campo} está preenchido no XML mas ausente no sistema`);
            } else if (v1 && !v2) {
                erros.push(`Campo ${campo} está ausente no XML mas preenchido no sistema`);
            } else if (v1?.toString().trim() !== v2?.toString().trim()) {
                erros.push(`Divergência no campo ${campo}: sistema='${v1}' vs XML='${v2}'`);
            }
        };

        if (invoice) {
            validarCampo("numeroNota", invoice.notInNumero, notaFiscal.numeroNota);
            validarCampo("dataEmissao", invoice.notDtEmissao?.toISOString(), notaFiscal.dataEmissao);
            validarCampo("horaEmissao", invoice.notHrHoraemissao, notaFiscal.dataEmissao?.split("T")?.[1]?.substring(0, 8));
            validarCampo("cnpjEmitente", invoice.notStCgc, notaFiscal.emitente?.cnpj);
            validarCampo("ufEmitente", invoice.notStUf, notaFiscal.emitente?.endereco?.UF);
            validarCampo("municipioEmitente", invoice.notStMunicipio, notaFiscal.emitente?.endereco?.xMun);
            validarCampo("inscricaoEstadual", invoice.notStIncrestadual, notaFiscal.emitente?.inscricaoEstadual);
            validarCampo("cfop", invoice.cfopStDescricao, notaFiscal.produtos?.[0]?.cfop);
            validarCampo("chaveAcesso", invoice.notStChaveacesso, notaFiscal.protocolo?.chNFe);
            validarCampo("numeroDanfe", invoice.numeroDanfe, notaFiscal.numeroNota);
            validarCampo("status", invoice.status, notaFiscal.protocolo?.cStat === "100" ? "APROVADA" : "REJEITADA");
            validarCampo("xml", invoice.xml ? "ok" : "", notaFiscal ? "ok" : "");
        }

        if (romaneio && notaFiscal?.transporte?.vol) {
            const vol = notaFiscal.transporte.vol;

            validarCampo("pesoLiquido", romaneio.totalWeight, parseInt(vol.pesoL));
            validarCampo("pesoBruto", romaneio.totalWeight, parseInt(vol.pesoB));
            validarCampo("volumeQtd", romaneio.totalVolume, parseInt(vol.qVol));
            validarCampo("dataRomaneio", romaneio.dateIssue?.toISOString()?.split("T")?.[0], notaFiscal.dataEmissao?.split("T")?.[0]);

            const enderecoXml = notaFiscal.informacoesAdicionais?.infCpl || '';
            if (enderecoXml && romaneio.address) {
                const enderecoSistema = romaneio.address.trim().toLowerCase();
                const enderecoXmlTxt = enderecoXml.trim().toLowerCase();
                if (!enderecoXmlTxt.includes(enderecoSistema)) {
                    erros.push(`Endereço do romaneio não encontrado no XML (romaneio='${romaneio.address}')`);
                }
            }
        }

        const produtosXml = notaFiscal.produtos ?? [];

        for (const item of invoiceItems) {
            const xml = produtosXml.find(p => p.codigo === item.notInCodigo?.toString());
            if (!xml) {
                erros.push(`Produto com código ${item.notInCodigo} não encontrado no XML`);
                continue;
            }

            validarCampo(`Descrição do produto ${item.notInCodigo}`, item.itnStDescricao, xml.descricao);
            validarCampo(`Quantidade ${item.notInCodigo}`, item.itnReQuantidade, xml.quantidade);
            validarCampo(`Valor unitário ${item.notInCodigo}`, item.itnReValorunitario, xml.valorUnitario);
            validarCampo(`Valor total ${item.notInCodigo}`, item.itnReValortotal, xml.valorTotal);
        }

        for (const erro of erros) {
            await this.salvarErroVnf(
                notaFiscal.numeroNota,
                separationOrder?.externalCode || null,
                erro,
                notaFiscal.destinatario?.email || null,
                notaFiscal.emitente?.email || null,
                notaFiscal.emitente?.nome || null
            );
        }

        return {
            invoice,
            separationOrder,
            invoiceItems,
            romaneio,
            romaneioOrder,
            produtos: produtosXml,
            erros,
        };
    }

    private async salvarErroVnf(
        numeroNota: string,
        clienteExternalCode: string | null,
        tipoErro: string,
        compradorEmail: string | null,
        fornecedorEmail: string | null,
        nomeFornecedor: string | null,
        podeRecusar: boolean = true,
    ) {
        const erro = this.vnfErrorRepository.create({
            numeroNota: numeroNota || '',
            clienteExternalCode: clienteExternalCode || null,
            nomeFornecedor: nomeFornecedor || null,
            tipoErro: tipoErro || '',
            compradorEmail: compradorEmail || null,
            fornecedorEmail: fornecedorEmail || null,
            dataEmailEnviado: undefined,
            podeRecusar: podeRecusar,
        } as Partial<VnfError>);
        await this.vnfErrorRepository.save(erro);
    }

    private validarCNPJ(cnpj: string): boolean {
        cnpj = cnpj.replace(/[\D]/g, '');
        if (cnpj.length !== 14) return false;
        let tamanho = cnpj.length - 2;
        let numeros = cnpj.substring(0, tamanho);
        let digitos = cnpj.substring(tamanho);
        let soma = 0;
        let pos = tamanho - 7;
        for (let i = tamanho; i >= 1; i--) {
            soma += +numeros.charAt(tamanho - i) * pos--;
            if (pos < 2) pos = 9;
        }
        let resultado = soma % 11 < 2 ? 0 : 11 - (soma % 11);
        if (resultado !== +digitos.charAt(0)) return false;
        tamanho = tamanho + 1;
        numeros = cnpj.substring(0, tamanho);
        soma = 0;
        pos = tamanho - 7;
        for (let i = tamanho; i >= 1; i--) {
            soma += +numeros.charAt(tamanho - i) * pos--;
            if (pos < 2) pos = 9;
        }
        resultado = soma % 11 < 2 ? 0 : 11 - (soma % 11);
        if (resultado !== +digitos.charAt(1)) return false;
        return true;
    }

    async listarErrosVnf(): Promise<VnfError[]> {
        return this.vnfErrorRepository.find({ 
            where: { deletedAt: IsNull() },
            order: { createdAt: 'DESC' } 
        });
    }

    /**
     * Compara um pedido do portal com uma nota fiscal e retorna os erros encontrados
     */
    async compararPedidoComNotaFiscal(pedido: PedidoPortal, notaFiscal: NotaFiscal): Promise<string[]> {
        const errors: string[] = [];

        // Validação de cabeçalhos
        this.validarCabecalhos(pedido, notaFiscal, errors);

        // Validação de produtos
        this.validarProdutos(pedido, notaFiscal, errors);

        // Validação de data de entrega (Regra 1)
        this.validarDataEntrega(pedido, notaFiscal, errors);

        // Validação de prazo limite de emissão (Nova Regra)
        this.validarPrazoLimiteEmissao(notaFiscal, errors);

        // Validação de coerência de valores (Regra 4)
        this.validarCoerenciaValores(notaFiscal, errors);

        // Validações finais
        this.validarRegrasFinais(notaFiscal, pedido, errors);

        return errors;
    }

    /**
     * Valida os campos de cabeçalho entre pedido e nota fiscal
     */
    private validarCabecalhos(pedido: PedidoPortal, notaFiscal: NotaFiscal, errors: string[]): void {
        // 1. Validar número do pedido nas observações da NF
        const resultadoExtracao = this.extrairNumeroPedido(notaFiscal.informacoesAdicionais?.infCpl);
        
        if (resultadoExtracao.erro) {
            errors.push(`NF Emitida Incoerente! ${resultadoExtracao.erro}`);
            return; // Não continua com outras validações se não conseguiu extrair o número do pedido OU se há múltiplos
        }
        
        const pedidoEsperado = pedido.pedidoERP ? pedido.pedidoERP.toString().trim() : pedido.pedidoERP.toString().trim();
        const numeroPedidoExtraido = resultadoExtracao.numero ? resultadoExtracao.numero.toString().trim() : null;
        
        if (!numeroPedidoExtraido || numeroPedidoExtraido !== pedidoEsperado) {
            errors.push(`NF Emitida Incoerente! Número do pedido não encontrado ou divergente. Esperado: ${pedidoEsperado}, Encontrado: ${numeroPedidoExtraido || 'não encontrado'}`);
        }

        // 2. Validar CNPJs
        if (pedido.cnpjFornecedor !== notaFiscal.emitente.cnpj) {
            errors.push(`NF Emitida Incoerente! CNPJ do fornecedor divergente. Pedido: ${pedido.cnpjFornecedor}, NF: ${notaFiscal.emitente.cnpj}`);
        }

        if (pedido.cnpjPlanta !== notaFiscal.destinatario.cnpj) {
            errors.push(`NF Emitida Incoerente! CNPJ da planta divergente. Pedido: ${pedido.cnpjPlanta}, NF: ${notaFiscal.destinatario.cnpj}`);
        }

        // 3. Validar nome do fornecedor (ignorando maiúsculas, minúsculas e acentos)
        const nomeFornecedorNormalizado = this.normalizarTexto(pedido.nomeFornecedor);
        const nomeEmitenteNormalizado = this.normalizarTexto(notaFiscal.emitente.nome);
        
        if (nomeFornecedorNormalizado !== nomeEmitenteNormalizado) {
            errors.push(`NF Emitida Incoerente! Nome do fornecedor divergente. Pedido: ${pedido.nomeFornecedor}, NF: ${notaFiscal.emitente.nome}`);
        }

        // 4. Validar código de condição de pagamento
        const condicaoPagamentoEncontrada = this.extrairCondicaoPagamento(notaFiscal.informacoesAdicionais);
        // Normalizar ambos os valores removendo letras e zeros à esquerda
        const condicaoPedidoNormalizada = pedido.condicaoPagamento.replace(/\D/g, '').replace(/^0+/, '') || '0';
        const condicaoNFNormalizada = condicaoPagamentoEncontrada ? condicaoPagamentoEncontrada.replace(/^0+/, '') || '0' : '';
        
        if (condicaoNFNormalizada && condicaoPedidoNormalizada !== condicaoNFNormalizada) {
            errors.push(`NF Emitida Incoerente! Condição de pagamento divergente. Pedido: ${pedido.condicaoPagamento}, NF: ${condicaoPagamentoEncontrada || 'não encontrada'}`);
        }
    }

    /**
     * Valida os produtos entre pedido e nota fiscal
     */
    private validarProdutos(pedido: PedidoPortal, notaFiscal: NotaFiscal, errors: string[]): void {
        const produtosPedido = pedido.itens;
        const produtosNF = notaFiscal.produtos;

        // Verificar se a NF não tem mais de 30 itens
        if (produtosNF.length > 30) {
            errors.push(`NF Emitida Incoerente! Nota fiscal possui mais de 30 itens (${produtosNF.length})`);
            return;
        }

        // Comparar cada item do pedido com o correspondente na NF
        for (const itemPedido of produtosPedido) {
            const produtoNF = produtosNF.find(p => parseInt(p.numeroItem) === itemPedido.sequencial);
            
            if (!produtoNF) {
                errors.push(`NF Emitida Incoerente! Item ${itemPedido.sequencial} do pedido não encontrado na nota fiscal`);
                continue;
            }

            // Validar sequencial
            if (parseInt(produtoNF.numeroItem) !== itemPedido.sequencial) {
                errors.push(`NF Emitida Incoerente! Sequencial do item ${itemPedido.sequencial} divergente. Pedido: ${itemPedido.sequencial}, NF: ${produtoNF.numeroItem}`);
            }

            // Validar unidade de medida
            if (produtoNF.unidade !== itemPedido.codigoUnidadeMedida) {
                errors.push(`NF Emitida Incoerente! Unidade de medida do item ${itemPedido.sequencial} divergente. Pedido: ${itemPedido.codigoUnidadeMedida}, NF: ${produtoNF.unidade}`);
            }

            // Validar NCM
            if (produtoNF.ncm !== itemPedido.nmc) {
                errors.push(`NF Emitida Incoerente! NCM do item ${itemPedido.sequencial} divergente. Pedido: ${itemPedido.nmc}, NF: ${produtoNF.ncm}`);
            }

            // Validar valor unitário (admite diferença de centavos)
            const valorUnitarioPedido = itemPedido.valorUnitario;
            const valorUnitarioNF = parseFloat(produtoNF.valorUnitario);
            const diferenca = Math.abs(valorUnitarioPedido - valorUnitarioNF);
            
            if (diferenca > 0.01) {
                errors.push(`NF Emitida Incoerente! Valor unitário do item ${itemPedido.sequencial} divergente. Pedido: ${valorUnitarioPedido}, NF: ${valorUnitarioNF}`);
            }

            // Validar quantidade (NF não pode ultrapassar pedido)
            const quantidadePedido = itemPedido.quantidade;
            const quantidadeNF = parseFloat(produtoNF.quantidade);
            
            if (quantidadeNF > quantidadePedido) {
                errors.push(`NF Emitida Incoerente! Quantidade do item ${itemPedido.sequencial} excede o pedido. Pedido: ${quantidadePedido}, NF: ${quantidadeNF}`);
            }
        }
    }

    /**
     * Valida as regras finais da nota fiscal
     */
    private validarRegrasFinais(notaFiscal: NotaFiscal, pedido: PedidoPortal, errors: string[]): void {
        // Validar se o desconto está zerado
        const valorDesconto = parseFloat(notaFiscal.total.vDesc);
        if (valorDesconto > 0) {
            errors.push(`NF Emitida Incoerente! Nota fiscal possui desconto (${valorDesconto})`);
        }

        // Validar valor total da NF vs valor total do pedido com frete
        const valorTotalNF = parseFloat(notaFiscal.total.vNF);
        const valorTotalPedido = pedido.valorTotalComFrete;
        const diferenca = Math.abs(valorTotalNF - valorTotalPedido);
        
        if (diferenca > 0.01) {
            errors.push(`NF Emitida Incoerente! Valor total da nota fiscal divergente. Pedido: ${valorTotalPedido}, NF: ${valorTotalNF}`);
        }
    }



    /**
     * Extrai a condição de pagamento das observações da NF
     */
    private extrairCondicaoPagamento(informacoesAdicionais: any): string | null {
        if (!informacoesAdicionais?.obsCont) return null;

        const obsCont = Array.isArray(informacoesAdicionais.obsCont) 
            ? informacoesAdicionais.obsCont 
            : [informacoesAdicionais.obsCont];

        for (const obs of obsCont) {
            if (obs.$?.xCampo === 'Condicao Pgto:' && obs.xTexto) {
                // Extrair apenas os números da condição e normalizar
                const match = obs.xTexto.match(/^(\d+)/);
                if (match) {
                    // Remover zeros à esquerda e retornar apenas os números
                    const numero = match[1].replace(/^0+/, '');
                    return numero || '0'; // Se todos os dígitos forem zero, retorna '0'
                }
                // Fallback: extrair todos os números e normalizar
                const numeros = obs.xTexto.replace(/\D/g, '');
                return numeros.replace(/^0+/, '') || '0';
            }
        }

        return null;
    }

    /**
     * Normaliza texto removendo acentos e convertendo para minúsculas
     */
    private normalizarTexto(texto: string): string {
        return texto
            .normalize('NFD')
            .replace(/[\u0300-\u036f]/g, '')
            .toLowerCase()
            .trim();
    }

    /**
     * Valida a data de entrega dos itens (Regra 1)
     * Deve ser de até 10 dias de antecedência
     */
    private validarDataEntrega(pedido: PedidoPortal, notaFiscal: NotaFiscal, errors: string[]): void {
        if (!pedido.itens || !notaFiscal.dataEmissao) return;

        const dataEmissaoNF = new Date(notaFiscal.dataEmissao);
        
        for (const item of pedido.itens) {
            if (!item.entrega || !Array.isArray(item.entrega)) continue;
            
            for (const entrega of item.entrega) {
                if (!entrega.dataDeEntrega) continue;
                
                const dataEntrega = new Date(entrega.dataDeEntrega);
                const diferencaDias = Math.ceil((dataEmissaoNF.getTime() - dataEntrega.getTime()) / (1000 * 60 * 60 * 24));
                
                if (diferencaDias > 10) {
                    errors.push(`NF Emitida Incoerente! Data de entrega do item ${item.sequencial} antecipada em ${diferencaDias} dias (limite = 10).`);
                }
            }
        }
    }

    /**
     * Valida o prazo limite de emissão da nota fiscal (Nova Regra)
     * A nota deve ser emitida até o dia 25 do mês
     */
    private validarPrazoLimiteEmissao(notaFiscal: NotaFiscal, errors: string[]): void {
        if (!notaFiscal.dataEmissao) return;

        const dataEmissaoNF = new Date(notaFiscal.dataEmissao);
        const diaEmissao = dataEmissaoNF.getDate();
        
        // Verificar se a emissão foi após o dia 25
        if (diaEmissao > 25) {
            const mesEmissao = dataEmissaoNF.toLocaleDateString('pt-BR', { month: 'long', year: 'numeric' });
            errors.push(`NF Emitida Incoerente! Nota fiscal emitida após o prazo limite. Emitida no dia ${diaEmissao} de ${mesEmissao} (limite: dia 25).`);
        }
    }

    /**
     * Valida a coerência de valores dos itens (Regra 4)
     * valorUnitário × quantidade = valorTotal
     */
    private validarCoerenciaValores(notaFiscal: NotaFiscal, errors: string[]): void {
        if (!notaFiscal.produtos) return;

        for (const produto of notaFiscal.produtos) {
            const valorUnitario = parseFloat(produto.valorUnitario);
            const quantidade = parseFloat(produto.quantidade);
            const valorTotalInformado = parseFloat(produto.valorTotal);
            
            const valorTotalCalculado = valorUnitario * quantidade;
            const diferenca = Math.abs(valorTotalCalculado - valorTotalInformado);
            
            if (diferenca > 0.01) {
                errors.push(`NF Emitida Incoerente! Valor total inconsistente no item ${produto.numeroItem} (esperado ${valorTotalCalculado.toFixed(2)}, informado ${valorTotalInformado.toFixed(2)}).`);
            }
        }
    }

    /**
     * Processa a comparação entre pedido e nota fiscal, salvando erros no banco
     */
    async processarComparacaoPedidoNotaFiscal(
        pedido: PedidoPortal, 
        notaFiscal: NotaFiscal,
        numeroNota: string,
        clienteExternalCode?: string
    ): Promise<{ success: boolean; errors: string[] }> {
        try {
            const errors = await this.compararPedidoComNotaFiscal(pedido, notaFiscal);

            // Buscar email do fornecedor
            const fornecedorEmail = await this.buscarEmailFornecedor(pedido.nomeFornecedor);

            // Verificar se estamos na janela de recusa (Regra 2)
            const hoje = new Date();
            const diaDoMes = hoje.getDate();
            const ultimoDiaDoMes = new Date(hoje.getFullYear(), hoje.getMonth() + 1, 0).getDate();
            const estaNaJanelaDeRecusa = diaDoMes >= 26 && diaDoMes <= ultimoDiaDoMes;

            // Salvar cada erro no banco de dados
            for (const error of errors) {
                await this.salvarErroVnf(
                    numeroNota,
                    clienteExternalCode || pedido.pedidoERP,
                    error,
                    pedido.loginComprador,
                    fornecedorEmail, // Agora usando o email do fornecedor encontrado
                    pedido.nomeFornecedor, // Nome do fornecedor do pedido
                    !estaNaJanelaDeRecusa // podeRecusar = true se NÃO estiver na janela de recusa
                );
            }

            return {
                success: errors.length === 0,
                errors
            };
        } catch (error) {
            console.error('[VnfService] Erro ao processar comparação:', error);
            return {
                success: false,
                errors: [`Erro interno ao processar comparação: ${error.message}`]
            };
        }
    }

    /**
     * Envia email de divergência para o fornecedor
     */
    async enviarEmailDivergencia(erroId: number): Promise<{ success: boolean; message: string }> {
        try {
            const erro = await this.vnfErrorRepository.findOne({ where: { id: erroId } });
            
            if (!erro) {
                return { success: false, message: 'Erro não encontrado' };
            }

            if (!erro.fornecedorEmail) {
                return { success: false, message: 'Email do fornecedor não encontrado' };
            }

            // Gerar o corpo do email baseado no tipo de erro
            const emailContent = this.gerarConteudoEmail(erro);

            // Enviar e-mail usando o EmailService
            const resultadoEmail = await this.emailService.enviarEmailDivergencia(
                erro.fornecedorEmail,
                erro.numeroNota,
                emailContent,
                erro.nomeFornecedor
            );

            if (!resultadoEmail.success) {
                return {
                    success: false,
                    message: resultadoEmail.message
                };
            }

            // Atualizar a data de envio
            erro.dataEmailEnviado = new Date();
            await this.vnfErrorRepository.save(erro);

            return { 
                success: true, 
                message: `Email enviado com sucesso para ${erro.fornecedorEmail}` 
            };

        } catch (error) {
            console.error('[VnfService] Erro ao enviar email:', error);
            return { 
                success: false, 
                message: `Erro ao enviar email: ${error.message}` 
            };
        }
    }

    /**
     * Envia emails em lote para múltiplos erros, agrupando por fornecedor e nota
     */
    async enviarEmailsEmLote(erroIds: number[], emailsAdicionais?: string[]): Promise<{ success: boolean; results: Array<{ id: number; success: boolean; message: string }> }> {
        const results: Array<{ id: number; success: boolean; message: string }> = [];

        try {
            // Buscar todos os erros
            const erros = await this.vnfErrorRepository.find({ 
                where: erroIds.map(id => ({ id })),
                order: { createdAt: 'ASC' }
            });

            if (erros.length === 0) {
                return {
                    success: false,
                    results: erroIds.map(id => ({ id, success: false, message: 'Erro não encontrado' }))
                };
            }

            // Agrupar erros por fornecedor e nota
            const grupos = new Map<string, VnfError[]>();
            
            for (const erro of erros) {
                const chave = `${erro.fornecedorEmail || 'sem-email'}_${erro.numeroNota}`;
                
                if (!grupos.has(chave)) {
                    grupos.set(chave, []);
                }
                grupos.get(chave)!.push(erro);
            }

            // Enviar um email por grupo
            for (const [chave, errosGrupo] of grupos) {
                const primeiroErro = errosGrupo[0];
                
                if (!primeiroErro.fornecedorEmail) {
                    // Marcar todos os erros do grupo como falha
                    for (const erro of errosGrupo) {
                        results.push({
                            id: erro.id,
                            success: false,
                            message: 'Email do fornecedor não encontrado'
                        });
                    }
                    continue;
                }



                // Gerar conteúdo do email com todos os erros do grupo
                const emailContent = this.gerarConteudoEmailAgrupado(errosGrupo);

                // Lista de emails para enviar (fornecedor + emails adicionais)
                const emailsParaEnviar = [primeiroErro.fornecedorEmail];
                if (emailsAdicionais && emailsAdicionais.length > 0) {
                    emailsParaEnviar.push(...emailsAdicionais);
                }

                console.log(`[VnfService] Emails adicionais recebidos:`, emailsAdicionais);
                console.log(`[VnfService] Email do fornecedor: ${primeiroErro.fornecedorEmail}`);
                console.log(`[VnfService] Lista final de emails para enviar:`, emailsParaEnviar);

                // Enviar email para cada destinatário
                let todosEnviadosComSucesso = true;
                const mensagensEnvio: string[] = [];

                console.log(`[VnfService] Enviando emails para: ${emailsParaEnviar.join(', ')}`);
                console.log(`[VnfService] Conteúdo do email: ${emailContent.substring(0, 200)}...`);

                for (const emailDestinatario of emailsParaEnviar) {
                    try {
                        console.log(`[VnfService] Tentando enviar email para: ${emailDestinatario}`);
                        
                        const resultadoEmail = await this.emailService.enviarEmailDivergencia(
                            emailDestinatario,
                            primeiroErro.numeroNota,
                            emailContent,
                            primeiroErro.nomeFornecedor
                        );

                        console.log(`[VnfService] Resultado do envio para ${emailDestinatario}:`, resultadoEmail);

                        if (resultadoEmail.success) {
                            mensagensEnvio.push(`Email enviado com sucesso para ${emailDestinatario}`);
                        } else {
                            todosEnviadosComSucesso = false;
                            mensagensEnvio.push(`Falha ao enviar para ${emailDestinatario}: ${resultadoEmail.message}`);
                        }
                    } catch (error) {
                        console.error(`[VnfService] Erro ao enviar para ${emailDestinatario}:`, error);
                        todosEnviadosComSucesso = false;
                        mensagensEnvio.push(`Erro ao enviar para ${emailDestinatario}: ${error.message}`);
                    }
                }

                if (todosEnviadosComSucesso) {
                    // Atualizar todos os erros do grupo com a data de envio
                    for (const erro of errosGrupo) {
                        erro.dataEmailEnviado = new Date();
                        await this.vnfErrorRepository.save(erro);
                        
                        results.push({
                            id: erro.id,
                            success: true,
                            message: mensagensEnvio.join('; ')
                        });
                    }
                } else {
                    // Marcar todos os erros do grupo como falha
                    for (const erro of errosGrupo) {
                        results.push({
                            id: erro.id,
                            success: false,
                            message: mensagensEnvio.join('; ')
                        });
                    }
                }
            }

            const successCount = results.filter(r => r.success).length;
            const totalCount = results.length;

            return {
                success: successCount > 0,
                results
            };

        } catch (error) {
            console.error('[VnfService] Erro ao enviar emails em lote:', error);
            return {
                success: false,
                results: erroIds.map(id => ({ 
                    id, 
                    success: false, 
                    message: `Erro interno: ${error.message}` 
                }))
            };
        }
    }

    /**
     * Gera o conteúdo do email baseado no tipo de erro
     */
    private gerarConteudoEmail(erro: VnfError): string {
        const numeroNota = erro.numeroNota;
        const fornecedorNome = this.extrairNomeFornecedorDoErro(erro.tipoErro);
        
        // Extrair detalhes específicos do erro para personalizar o email
        const detalhesErro = this.extrairDetalhesDoErro(erro.tipoErro);

        return `Prezado Fornecedor,

Durante o processo automático de conferência da Nota Fiscal nº ${numeroNota}, foram identificadas divergências no arquivo XML em relação ao nosso Pedido de Compra, conforme detalhado abaixo:

${detalhesErro}

Solicitamos a revisão e o reenvio imediato do arquivo XML corrigido para o e-mail: <EMAIL>

Informamos que, caso não haja retorno com a devida correção no prazo máximo de 24 horas, a NF-e será recusada automaticamente e o recebimento, rejeitado.

Importante: Notas Fiscais emitidas sem autorização formal do Departamento de Compras, por meio do Pedido de Compra, serão recusadas automaticamente, sem possibilidade de revisão.

Atenciosamente,

Departamento de Compras
Elco Engenharia`;
    }

    /**
     * Gera o conteúdo do email para múltiplos erros agrupados
     */
    private gerarConteudoEmailAgrupado(erros: VnfError[]): string {
        const primeiroErro = erros[0];
        const numeroNota = primeiroErro.numeroNota;
        const fornecedorNome = this.extrairNomeFornecedorDoErro(primeiroErro.tipoErro);
        
        // Gerar lista de todos os erros
        const detalhesErros = erros.map(erro => this.extrairDetalhesDoErro(erro.tipoErro)).join('\n\n');

        return `Prezado Fornecedor,

Durante o processo automático de conferência da Nota Fiscal nº ${numeroNota}, foram identificadas ${erros.length} divergência(s) no arquivo XML em relação ao nosso Pedido de Compra, conforme detalhado abaixo:

${detalhesErros}

Solicitamos a revisão e o reenvio imediato do arquivo XML corrigido para o e-mail: <EMAIL>

Informamos que, caso não haja retorno com a devida correção no prazo máximo de 24 horas, a NF-e será recusada automaticamente e o recebimento, rejeitado.

Importante: Notas Fiscais emitidas sem autorização formal do Departamento de Compras, por meio do Pedido de Compra, serão recusadas automaticamente, sem possibilidade de revisão.

Atenciosamente,

Departamento de Compras
Elco Engenharia`;
    }

    /**
     * Extrai o nome do fornecedor do tipo de erro
     */
    private extrairNomeFornecedorDoErro(tipoErro: string): string {
        // Tentar extrair o nome do fornecedor do erro
        const match = tipoErro.match(/fornecedor divergente\. Pedido: ([^,]+)/);
        if (match) {
            return match[1].trim();
        }
        
        // Se não encontrar, retornar um nome genérico
        return 'Fornecedor';
    }

    /**
     * Extrai detalhes específicos do erro para personalizar o email
     */
    private extrairDetalhesDoErro(tipoErro: string): string {
        // Mapear tipos de erro para detalhes específicos
        if (tipoErro.includes('Quantidade')) {
            const match = tipoErro.match(/Quantidade do item (\d+) excede o pedido\. Pedido: ([\d.]+), NF: ([\d.]+)/);
            if (match) {
                const [, item, pedido, nf] = match;
                return `• Quantidade informada: ${nf} peças
• Quantidade correta: ${pedido} peças, conforme descrito no Pedido de Compra.`;
            }
        }

        if (tipoErro.includes('Valor unitário')) {
            const match = tipoErro.match(/Valor unitário do item (\d+) divergente\. Pedido: ([\d.]+), NF: ([\d.]+)/);
            if (match) {
                const [, item, pedido, nf] = match;
                return `• Valor unitário informado: R$ ${nf}
• Valor unitário correto: R$ ${pedido}, conforme descrito no Pedido de Compra.`;
            }
        }

        if (tipoErro.includes('CNPJ do fornecedor')) {
            const match = tipoErro.match(/CNPJ do fornecedor divergente\. Pedido: ([\d.]+), NF: ([\d.]+)/);
            if (match) {
                const [, pedido, nf] = match;
                return `• CNPJ informado: ${nf}
• CNPJ correto: ${pedido}, conforme descrito no Pedido de Compra.`;
            }
        }

        // Para outros tipos de erro, usar o erro completo
        return `• ${tipoErro}`;
    }

    /**
     * Aceita um erro (envia email automaticamente)
     */
    async aceitarErro(erroId: number): Promise<{ success: boolean; message: string }> {
        try {
            const erro = await this.vnfErrorRepository.findOne({ where: { id: erroId } });
            
            if (!erro) {
                return { success: false, message: 'Erro não encontrado' };
            }

            if (!erro.fornecedorEmail) {
                return { success: false, message: 'Email do fornecedor não encontrado' };
            }

            // Gerar o corpo do email baseado no tipo de erro
            const emailContent = this.gerarConteudoEmail(erro);

            // Enviar e-mail usando o EmailService
            const resultadoEmail = await this.emailService.enviarEmailDivergencia(
                erro.fornecedorEmail,
                erro.numeroNota,
                emailContent,
                erro.nomeFornecedor
            );

            if (!resultadoEmail.success) {
                return {
                    success: false,
                    message: resultadoEmail.message
                };
            }

            // Atualizar a data de envio
            erro.dataEmailEnviado = new Date();
            await this.vnfErrorRepository.save(erro);

            return { 
                success: true, 
                message: `Email enviado com sucesso para ${erro.fornecedorEmail}` 
            };

        } catch (error) {
            console.error('[VnfService] Erro ao aceitar erro:', error);
            return { 
                success: false, 
                message: `Erro ao aceitar erro: ${error.message}` 
            };
        }
    }

    /**
     * Recusa um erro (soft delete)
     */
    async recusarErro(erroId: number): Promise<{ success: boolean; message: string }> {
        try {
            const erro = await this.vnfErrorRepository.findOne({ where: { id: erroId } });
            
            if (!erro) {
                return { success: false, message: 'Erro não encontrado' };
            }

            // Soft delete
            await this.vnfErrorRepository.softDelete(erroId);

            return { 
                success: true, 
                message: 'Erro recusado com sucesso' 
            };

        } catch (error) {
            console.error('[VnfService] Erro ao recusar erro:', error);
            return { 
                success: false, 
                message: `Erro ao recusar erro: ${error.message}` 
            };
        }
    }

    /**
     * Testa a conexão SMTP
     */
    async testarConexaoEmail(): Promise<{ success: boolean; message: string }> {
        return this.emailService.testarConexao();
    }

    /**
     * Testa o envio de email
     */
    async testarEnvioEmail(email: string): Promise<{ success: boolean; message: string }> {
        try {
            console.log(`[VnfService] Testando envio de email para: ${email}`);
            
            const resultado = await this.emailService.enviarEmailDivergencia(
                email,
                'TESTE-001',
                `Este é um email de teste do sistema VNF.`,
                'Fornecedor Teste'
            );

            return resultado;
        } catch (error) {
            console.error('[VnfService] Erro ao testar envio de email:', error);
            return {
                success: false,
                message: `Erro ao testar envio: ${error.message}`
            };
        }
    }

    /**
     * Salva ou atualiza o email de um fornecedor na tabela supplier_email
     */
    async salvarEmailFornecedor(nomeFornecedor: string, email: string): Promise<{ 
        success: boolean; 
        message: string; 
        fornecedor?: any 
    }> {
        try {
            if (!nomeFornecedor || !email) {
                return {
                    success: false,
                    message: 'Nome do fornecedor e email são obrigatórios'
                };
            }

            // Normalizar o nome do fornecedor
            const nomeNormalizado = nomeFornecedor.trim().toUpperCase();
            
            // Verificar se já existe um fornecedor com este nome
            const fornecedorExistente = await this.supplierEmailRepository
                .createQueryBuilder('supplier')
                .where('UPPER(supplier.supplierName) = :nome', { nome: nomeNormalizado })
                .getOne();

            let fornecedor: any;

            if (fornecedorExistente) {
                // Atualizar email existente
                fornecedorExistente.email = email;
                fornecedor = await this.supplierEmailRepository.save(fornecedorExistente);
                console.log(`[VnfService] Email do fornecedor atualizado: ${email} para ${nomeNormalizado}`);
            } else {
                // Criar novo fornecedor
                fornecedor = await this.supplierEmailRepository.save({
                    supplierName: nomeNormalizado,
                    email: email
                });
                console.log(`[VnfService] Novo fornecedor criado: ${nomeNormalizado} com email ${email}`);
            }

            // Atualizar todos os erros VNF que têm este nome de fornecedor
            const errosParaAtualizar = await this.vnfErrorRepository
                .createQueryBuilder('erro')
                .where('UPPER(erro.nomeFornecedor) = :nome', { nome: nomeNormalizado })
                .getMany();

            if (errosParaAtualizar.length > 0) {
                for (const erro of errosParaAtualizar) {
                    erro.fornecedorEmail = email;
                }
                await this.vnfErrorRepository.save(errosParaAtualizar);
                console.log(`[VnfService] ${errosParaAtualizar.length} erro(s) VNF atualizado(s) com o novo email`);
            }

            return {
                success: true,
                message: fornecedorExistente 
                    ? `Email do fornecedor atualizado com sucesso. ${errosParaAtualizar.length} erro(s) VNF atualizado(s).` 
                    : `Email do fornecedor salvo com sucesso. ${errosParaAtualizar.length} erro(s) VNF atualizado(s).`,
                fornecedor: {
                    id: fornecedor.id,
                    supplierName: fornecedor.supplierName,
                    email: fornecedor.email
                }
            };

        } catch (error) {
            console.error('[VnfService] Erro ao salvar email do fornecedor:', error);
            return {
                success: false,
                message: `Erro ao salvar email do fornecedor: ${error.message}`
            };
        }
    }

    /**
     * Busca chaves de acesso XML no Oracle para uma data específica
     */
    private async buscarChavesAcessoOracle(data: string): Promise<{
        chavesNovas: string[];
        totalChaves: number;
        chavesJaProcessadas: number;
    }> {
        try {
            const query = `
                SELECT RCNF_ST_CHAVEXML
                FROM ELCO.est_recebimentonfe@ELCO
                WHERE TRUNC(RCB_DT_DOCUMENTO) = TO_DATE(:data, 'DD/MM/YYYY')
                AND RCNF_ST_CHAVEXML IS NOT NULL
            `;

            const resultado = await this.oracleService.executeQuery(query, [data]);
            
            // Filtrar apenas chaves válidas (44 dígitos)
            const chavesValidas = resultado
                .map((row: any) => row[0])
                .filter((chave: string) => chave && chave.length === 44 && /^\d{44}$/.test(chave));

            console.log(`[VnfService] Encontradas ${chavesValidas.length} chaves de acesso válidas no Oracle para a data ${data}`);

            // Filtrar chaves que ainda não foram processadas
            const chavesNaoProcessadas: string[] = [];
            
            for (const chave of chavesValidas) {
                const jaProcessada = await this.verificarChaveJaProcessada(chave);
                if (!jaProcessada) {
                    chavesNaoProcessadas.push(chave);
                }
            }

            const chavesProcessadas = chavesValidas.length - chavesNaoProcessadas.length;
            console.log(`[VnfService] ${chavesProcessadas} chaves já foram processadas anteriormente`);
            console.log(`[VnfService] ${chavesNaoProcessadas.length} chaves novas para processar`);

            return {
                chavesNovas: chavesNaoProcessadas,
                totalChaves: chavesValidas.length,
                chavesJaProcessadas: chavesProcessadas
            };

        } catch (error) {
            console.error('[VnfService] Erro ao buscar chaves de acesso no Oracle:', error);
            throw new Error(`Erro ao consultar Oracle: ${error.message}`);
        }
    }

    /**
     * Processa lote de XMLs automaticamente para uma data específica
     */
    async processarLoteXmlsPorData(data: string): Promise<{
        success: boolean;
        message: string;
        totalProcessados: number;
        chavesNovas?: number;
        chavesJaProcessadas?: number;
        limiteSefazAtingido?: boolean;
        sucessos: number;
        erros: number;
        detalhes: Array<{
            chaveAcesso: string;
            numeroNota?: string;
            status: 'sucesso' | 'erro';
            message: string;
            temErros?: boolean;
            quantidadeErros?: number;
        }>;
    }> {
        try {
            console.log(`[VnfService] Iniciando processamento em lote para a data: ${data}`);

            // Buscar chaves de acesso no Oracle
            const chavesAcesso = await this.buscarChavesAcessoOracle(data);

            if (chavesAcesso.totalChaves === 0) {
                return {
                    success: true,
                    message: `Nenhuma chave de acesso encontrada para a data ${data}`,
                    totalProcessados: 0,
                    sucessos: 0,
                    erros: 0,
                    detalhes: []
                };
            }

            console.log(`[VnfService] Processando ${chavesAcesso.totalChaves} chaves de acesso`);

            const detalhes: Array<{
                chaveAcesso: string;
                numeroNota?: string;
                status: 'sucesso' | 'erro';
                message: string;
                temErros?: boolean;
                quantidadeErros?: number;
            }> = [];

            let sucessos = 0;
            let erros = 0;

            // Processar cada chave de acesso sequencialmente para evitar sobrecarga
            for (let i = 0; i < chavesAcesso.chavesNovas.length; i++) {
                const chaveAcesso = chavesAcesso.chavesNovas[i];
                
                try {
                    console.log(`[VnfService] Processando ${i + 1}/${chavesAcesso.totalChaves}: ${chaveAcesso}`);

                    // Delay progressivo entre as requisições para não sobrecarregar os serviços
                    if (i > 0) {
                        // Delay base de 5 segundos + delay progressivo a cada 10 consultas
                        const baseDelay = 5000; // 5 segundos
                        const progressiveDelay = Math.floor(i / 10) * 2000; // +2s a cada 10 consultas
                        const totalDelay = baseDelay + progressiveDelay;
                        
                        console.log(`[VnfService] ⏳ Aguardando ${totalDelay/1000}s antes da próxima consulta`);
                        await this.delay(totalDelay);
                    }

                    const resultado = await this.consultarXmlPorChave(chaveAcesso);

                    if (resultado.success) {
                        sucessos++;
                        
                        // Verificar se houve erros na comparação ou problemas com pedido
                        let temErros = false;
                        let quantidadeErros = 0;
                        let statusConsulta: 'sucesso' | 'erro' | 'limite_atingido' | 'erro_sefaz' = 'sucesso';
                        let observacoes = '';
                        
                        // Caso 1: Número do pedido não encontrado no XML
                        if (!resultado.numeroPedido) {
                            await this.criarErroVnf(
                                resultado.notaFiscal?.numeroNota || 'N/A',
                                chaveAcesso,
                                'Número do pedido não encontrado no XML',
                                resultado.notaFiscal
                            );
                            temErros = true;
                            quantidadeErros++;
                            observacoes = 'Número do pedido não encontrado no XML';
                        }
                        // Caso 2: Pedido não encontrado no portal de compras (404)
                        else if (resultado.dadosPedido && !resultado.dadosPedido.success && 
                                resultado.dadosPedido.error && 
                                resultado.dadosPedido.error.includes('não localizado no portal de compras')) {
                            await this.criarErroVnf(
                                resultado.notaFiscal?.numeroNota || 'N/A',
                                chaveAcesso,
                                'Número do pedido não encontrado no XML',
                                resultado.notaFiscal
                            );
                            temErros = true;
                            quantidadeErros++;
                            observacoes = `Pedido ${resultado.numeroPedido} não localizado no portal de compras`;
                        }
                        // Caso 3: Outras divergências na comparação
                        else if (resultado.dadosPedido && resultado.dadosPedido.error) {
                            temErros = true;
                            quantidadeErros = 1; // Por enquanto contamos como 1 erro por nota
                            observacoes = 'Divergências encontradas na comparação pedido x nota fiscal';
                        } else {
                            observacoes = 'Processado com sucesso sem divergências';
                        }

                        // Salvar no cache
                        await this.salvarConsultaProcessada(
                            chaveAcesso,
                            resultado,
                            statusConsulta,
                            observacoes,
                            new Date(data.split('/').reverse().join('-'))
                        );

                        detalhes.push({
                            chaveAcesso,
                            numeroNota: resultado.notaFiscal?.numeroNota,
                            status: 'sucesso',
                            message: temErros ? 
                                (quantidadeErros > 0 ? 'Processado com erros registrados na tabela' : 'Processado com divergências encontradas') 
                                : 'Processado com sucesso',
                            temErros,
                            quantidadeErros
                        });

                        console.log(`[VnfService] ✅ Sucesso: ${chaveAcesso} - Nota: ${resultado.notaFiscal?.numeroNota}${temErros ? ' (com erros)' : ''}`);
                    } else {
                        erros++;
                        
                        // Determinar tipo de erro
                        let statusConsulta: 'sucesso' | 'erro' | 'limite_atingido' | 'erro_sefaz' = 'erro';
                        let observacoes = resultado.message || 'Erro desconhecido';
                        
                        // Verificar se é erro de limite do SEFAZ
                        if (resultado.message && (
                            resultado.message.toLowerCase().includes('consumo indevido') ||
                            resultado.message.toLowerCase().includes('limite') ||
                            resultado.message.toLowerCase().includes('ultrapassou')
                        )) {
                            statusConsulta = 'limite_atingido';
                            observacoes = 'Limite de consultas SEFAZ atingido';
                        }

                        // Salvar no cache mesmo quando há erro
                        await this.salvarConsultaProcessada(
                            chaveAcesso,
                            null,
                            statusConsulta,
                            observacoes,
                            new Date(data.split('/').reverse().join('-'))
                        );

                        detalhes.push({
                            chaveAcesso,
                            status: 'erro',
                            message: resultado.message || 'Erro desconhecido'
                        });

                        console.log(`[VnfService] ❌ Erro: ${chaveAcesso} - ${resultado.message}`);
                        
                        // Se for erro de limite, interromper processamento
                        if (statusConsulta === 'limite_atingido') {
                            console.log(`[VnfService] 🚫 LIMITE DO SEFAZ ATINGIDO! Interrompendo processamento.`);
                            
                            // Marcar chaves restantes como não processadas
                            for (let j = i + 1; j < chavesAcesso.chavesNovas.length; j++) {
                                await this.salvarConsultaProcessada(
                                    chavesAcesso.chavesNovas[j],
                                    null,
                                    'limite_atingido',
                                    'Não processado devido ao limite do SEFAZ',
                                    new Date(data.split('/').reverse().join('-'))
                                );
                            }
                            
                            break; // Interromper o loop
                        }
                    }

                } catch (error) {
                    erros++;
                    
                    // Verificar se é erro relacionado ao limite do SEFAZ
                    const isLimiteError = this.verificarErroLimiteSefaz(error);
                    const statusConsulta = isLimiteError ? 'limite_atingido' : 'erro_sefaz';
                    const observacoes = isLimiteError ? 'Limite de consultas SEFAZ atingido' : `Erro interno: ${error.message}`;

                    // Salvar no cache
                    await this.salvarConsultaProcessada(
                        chaveAcesso,
                        null,
                        statusConsulta,
                        observacoes,
                        new Date(data.split('/').reverse().join('-'))
                    );

                    detalhes.push({
                        chaveAcesso,
                        status: 'erro',
                        message: isLimiteError ? 'Limite de consultas SEFAZ atingido' : `Erro interno: ${error.message}`
                    });

                    console.error(`[VnfService] ❌ Erro interno ao processar ${chaveAcesso}:`, error);
                    
                    // Se for erro de limite, interromper processamento
                    if (isLimiteError) {
                        console.log(`[VnfService] 🚫 LIMITE DO SEFAZ ATINGIDO! Interrompendo processamento.`);
                        
                        // Marcar chaves restantes como não processadas
                        for (let j = i + 1; j < chavesAcesso.chavesNovas.length; j++) {
                            await this.salvarConsultaProcessada(
                                chavesAcesso.chavesNovas[j],
                                null,
                                'limite_atingido',
                                'Não processado devido ao limite do SEFAZ',
                                new Date(data.split('/').reverse().join('-'))
                            );
                        }
                        
                        break; // Interromper o loop
                    }
                }
            }

            let message = '';
            let limiteSefazAtingido = false;
            
            // Verificar se houve limite do SEFAZ
            const errosLimite = detalhes.filter(d => d.message && (
                d.message.includes('Limite de consultas SEFAZ') ||
                d.message.includes('Consumo Indevido')
            ));
            
            if (errosLimite.length > 0) {
                limiteSefazAtingido = true;
                message = `⚠️ LIMITE DO SEFAZ ATINGIDO! Processamento interrompido. ${sucessos} sucessos, ${erros} erros de ${chavesAcesso.chavesNovas.length} chaves novas processadas. ${chavesAcesso.chavesJaProcessadas} chaves já foram processadas anteriormente.`;
            } else if (chavesAcesso.chavesJaProcessadas > 0) {
                message = `Processamento concluído: ${sucessos} sucessos, ${erros} erros de ${chavesAcesso.chavesNovas.length} chaves novas processadas. ${chavesAcesso.chavesJaProcessadas} chaves já foram processadas anteriormente (total: ${chavesAcesso.totalChaves}).`;
            } else {
                message = `Processamento concluído: ${sucessos} sucessos, ${erros} erros de ${chavesAcesso.totalChaves} chaves processadas.`;
            }
            
            console.log(`[VnfService] ${message}`);

            return {
                success: true,
                message,
                totalProcessados: chavesAcesso.totalChaves,
                chavesNovas: chavesAcesso.chavesNovas.length,
                chavesJaProcessadas: chavesAcesso.chavesJaProcessadas,
                limiteSefazAtingido,
                sucessos,
                erros,
                detalhes
            };

        } catch (error) {
            console.error('[VnfService] Erro no processamento em lote:', error);
            return {
                success: false,
                message: `Erro no processamento em lote: ${error.message}`,
                totalProcessados: 0,
                sucessos: 0,
                erros: 0,
                detalhes: []
            };
        }
    }

    /**
     * Função auxiliar para delay entre requisições
     */
    private delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Criar erro VNF para problemas no processamento automático
     */
    private async criarErroVnf(
        numeroNota: string,
        chaveAcesso: string,
        tipoErro: string,
        notaFiscal?: any
    ): Promise<void> {
        try {
            // Extrair dados do fornecedor se disponível
            let nomeFornecedor: string | null = null;
            let clienteExternalCode: string | null = null;
            let fornecedorEmail: string | null = null;

            if (notaFiscal?.emitente?.nome) {
                nomeFornecedor = notaFiscal.emitente.nome;
                if (nomeFornecedor) {
                    fornecedorEmail = await this.buscarEmailFornecedor(nomeFornecedor);
                }
            }

            // Criar e salvar o erro
            const erro = this.vnfErrorRepository.create({
                numeroNota: numeroNota || chaveAcesso.substring(25, 34), // Fallback: extrair número da chave
                clienteExternalCode: clienteExternalCode || undefined,
                nomeFornecedor: nomeFornecedor || undefined,
                tipoErro: tipoErro,
                compradorEmail: undefined, // Não temos essa informação no processamento automático
                fornecedorEmail: fornecedorEmail || undefined,
                dataEmailEnviado: undefined,
                podeRecusar: true,
            });

            await this.vnfErrorRepository.save(erro);

            console.log(`[VnfService] 📝 Erro VNF criado: ${tipoErro} - Nota: ${numeroNota}`);

        } catch (error) {
            console.error(`[VnfService] Erro ao criar erro VNF:`, error);
        }
    }

    /**
     * Verificar se uma chave de acesso já foi processada
     */
    private async verificarChaveJaProcessada(chaveAcesso: string): Promise<VnfConsultaProcessada | null> {
        try {
            return await this.vnfConsultaProcessadaRepository.findOne({
                where: { chaveAcesso }
            });
        } catch (error) {
            console.error(`[VnfService] Erro ao verificar chave processada:`, error);
            return null;
        }
    }

    /**
     * Salvar resultado da consulta processada
     */
    private async salvarConsultaProcessada(
        chaveAcesso: string,
        resultado: any,
        statusConsulta: 'sucesso' | 'erro' | 'limite_atingido' | 'erro_sefaz',
        observacoes?: string,
        dataDocumento?: Date
    ): Promise<void> {
        try {
            // Verificar se já existe
            const existente = await this.verificarChaveJaProcessada(chaveAcesso);
            
            if (existente) {
                // Atualizar existente
                existente.statusConsulta = statusConsulta;
                existente.observacoes = observacoes || existente.observacoes;
                if (resultado?.notaFiscal) {
                    existente.numeroNota = resultado.notaFiscal.numeroNota || existente.numeroNota;
                    existente.nomeFornecedor = resultado.notaFiscal.emitente?.nome || existente.nomeFornecedor;
                }
                if (resultado?.numeroPedido) {
                    existente.numeroPedido = resultado.numeroPedido;
                }
                if (resultado?.dadosPedido?.error) {
                    existente.temDivergencias = true;
                }
                
                await this.vnfConsultaProcessadaRepository.save(existente);
            } else {
                // Criar novo
                const consulta = this.vnfConsultaProcessadaRepository.create({
                    chaveAcesso,
                    numeroNota: resultado?.notaFiscal?.numeroNota,
                    nomeFornecedor: resultado?.notaFiscal?.emitente?.nome,
                    numeroPedido: resultado?.numeroPedido,
                    statusConsulta,
                    temDivergencias: resultado?.dadosPedido?.error ? true : false,
                    observacoes,
                    dataDocumento: dataDocumento || new Date()
                });

                await this.vnfConsultaProcessadaRepository.save(consulta);
            }

            console.log(`[VnfService] 💾 Consulta salva: ${chaveAcesso} - Status: ${statusConsulta}`);

        } catch (error) {
            console.error(`[VnfService] Erro ao salvar consulta processada:`, error);
        }
    }

    /**
     * Verificar se o erro é relacionado ao limite do SEFAZ
     */
    private verificarErroLimiteSefaz(error: any): boolean {
        const errorMessage = error?.message?.toLowerCase() || '';
        const responseData = error?.response?.data?.toString?.()?.toLowerCase() || '';
        
        return errorMessage.includes('consumo indevido') ||
               errorMessage.includes('limite') ||
               errorMessage.includes('ultrapassou') ||
               responseData.includes('consumo indevido') ||
               responseData.includes('limite') ||
               responseData.includes('ultrapassou');
    }

    /**
     * Listar consultas processadas
     */
    async listarConsultasProcessadas(data?: string): Promise<VnfConsultaProcessada[]> {
        try {
            let whereCondition: any = {};

            if (data) {
                // Converter data DD/MM/YYYY para YYYY-MM-DD
                const dateParts = data.split('/');
                if (dateParts.length === 3) {
                    const dataFormatada = `${dateParts[2]}-${dateParts[1]}-${dateParts[0]}`;
                    whereCondition.dataDocumento = dataFormatada;
                }
            }

            const consultas = await this.vnfConsultaProcessadaRepository.find({
                where: whereCondition,
                order: {
                    dataProcessamento: 'DESC'
                }
            });

            return consultas;

        } catch (error) {
            console.error('[VnfService] Erro ao listar consultas processadas:', error);
            throw new Error(`Erro ao listar consultas processadas: ${error.message}`);
        }
    }

    /**
     * Cron job para processamento automático de XMLs a cada 15 minutos
     * Executa às: 00:00, 00:15, 00:30, 00:45 de cada hora
     */
    @Cron('0 */15 * * * *') // A cada 15 minutos
    async processarXmlsAutomaticamente() {
        if (this.isProcessingCron) {
            this.logger.warn('⏳ Processamento anterior ainda em execução. Pulando esta execução.');
            return;
        }

        this.isProcessingCron = true;
        this.lastCronExecution = new Date();
        
        this.logger.log('🤖 Iniciando processamento automático de XMLs...');

        try {
            // Timeout de 14 minutos para evitar conflito com próxima execução
            const timeout = new Promise((_, reject) => 
                setTimeout(() => reject(new Error('Timeout no processamento automático')), 14 * 60 * 1000)
            );
            
            const processamento = async () => {
                // Obter data atual no formato DD/MM/YYYY
                const agora = new Date();
                const dia = agora.getDate().toString().padStart(2, '0');
                const mes = (agora.getMonth() + 1).toString().padStart(2, '0');
                const ano = agora.getFullYear();
                const dataHoje = `${dia}/${mes}/${ano}`;

                this.logger.log(`📅 Processando XMLs para a data: ${dataHoje}`);

                // Executar o processamento em lote
                const resultado = await this.processarLoteXmlsPorData(dataHoje);

                // Log dos resultados
                if (resultado.success) {
                    if (resultado.limiteSefazAtingido) {
                        this.logger.warn(`⚠️ ${resultado.message}`);
                    } else if (resultado.chavesJaProcessadas && resultado.chavesJaProcessadas > 0) {
                        this.logger.log(`📊 ${resultado.message}`);
                    } else if (resultado.totalProcessados === 0) {
                        this.logger.log(`📭 Nenhuma chave nova encontrada para ${dataHoje}`);
                    } else {
                        this.logger.log(`✅ ${resultado.message}`);
                    }

                    // Log de estatísticas detalhadas
                    if (resultado.totalProcessados > 0) {
                        this.logger.log(`📈 Estatísticas: ${resultado.sucessos} sucessos, ${resultado.erros} erros, ${resultado.chavesJaProcessadas || 0} já processadas`);
                        
                        // Contar erros por tipo
                        const errosLimite = resultado.detalhes.filter(d => 
                            d.message && d.message.includes('Limite de consultas SEFAZ')
                        ).length;
                        
                        if (errosLimite > 0) {
                            this.logger.warn(`🚫 ${errosLimite} chave(s) não processada(s) devido ao limite do SEFAZ`);
                        }
                    }
                } else {
                    this.logger.error(`❌ Erro no processamento automático: ${resultado.message}`);
                }

                return resultado;
            };

            // Executar com timeout
            await Promise.race([processamento(), timeout]);

        } catch (error) {
            if (error.message === 'Timeout no processamento automático') {
                this.logger.error('⏰ Timeout no processamento automático após 14 minutos');
            } else {
                this.logger.error(`❌ Erro no processamento automático: ${error.message}`);
            }
            // Não interromper o processo por erro no cron
        } finally {
            this.isProcessingCron = false;
            const duracao = Date.now() - this.lastCronExecution.getTime();
            this.logger.log(`🏁 Processamento automático finalizado em ${Math.round(duracao / 1000)}s`);
        }
    }

    /**
     * Obter status do processamento automático
     */
    getStatusProcessamentoAutomatico(): {
        isProcessing: boolean;
        lastExecution: Date | null;
        nextExecution: Date | null;
    } {
        // Calcular próxima execução (próximo múltiplo de 15 minutos)
        const agora = new Date();
        const minutos = agora.getMinutes();
        const proximoMultiplo = Math.ceil(minutos / 15) * 15;
        const proximaExecucao = new Date(agora);
        
        if (proximoMultiplo >= 60) {
            proximaExecucao.setHours(proximaExecucao.getHours() + 1);
            proximaExecucao.setMinutes(0);
        } else {
            proximaExecucao.setMinutes(proximoMultiplo);
        }
        proximaExecucao.setSeconds(0);
        proximaExecucao.setMilliseconds(0);

        return {
            isProcessing: this.isProcessingCron,
            lastExecution: this.lastCronExecution,
            nextExecution: proximaExecucao
        };
    }
}