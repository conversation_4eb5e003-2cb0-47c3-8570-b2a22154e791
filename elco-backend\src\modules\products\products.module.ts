import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ProductsService } from './products.service';
import { ProductsController } from './products.controller';
import { Product } from './entities/product.entity';
import { RomaneiosModule } from '../romaneios/romaneios.module';
import { ProductWeights } from './entities/product_weights.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Product,
      ProductWeights
    ]),
    forwardRef(() => RomaneiosModule)
  ],
  controllers: [ProductsController],
  providers: [ProductsService],
  exports: [ProductsService]
})
export class ProductsModule {}
