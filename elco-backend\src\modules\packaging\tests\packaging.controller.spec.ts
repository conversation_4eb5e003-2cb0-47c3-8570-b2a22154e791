import { Test, TestingModule } from '@nestjs/testing';
import { PackagingController } from '../packaging.controller';
import { PackagingService } from '../packaging.service';
import { NotFoundException } from '@nestjs/common';

describe('PackagingController', () => {
  let controller: PackagingController;
  let service: PackagingService;

  const mockPackaging = {
    id: '1',
    name: 'Caixa Teste',
    weight: 1.5,
    height: 20,
    length: 30,
    width: 25,
    volume: 0.015,
    type: '<PERSON>aixa',
    createdAt: new Date(),
    deleted_at: null,
  };

  const mockService = {
    findAll: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    findOne: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [PackagingController],
      providers: [
        {
          provide: PackagingService,
          useValue: mockService,
        },
      ],
    }).compile();

    controller = module.get<PackagingController>(PackagingController);
    service = module.get<PackagingService>(PackagingService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('findAll', () => {
    it('should return all packagings', async () => {
      const mockPackagings = [mockPackaging];
      mockService.findAll.mockResolvedValue(mockPackagings);

      const result = await controller.findAll();

      expect(result).toEqual(mockPackagings);
      expect(service.findAll).toHaveBeenCalled();
    });
  });

  describe('create', () => {
    it('should create a new packaging', async () => {
      const createDto = {
        name: 'Nova Caixa',
        weight: 2.0,
        height: 25,
        length: 35,
        width: 30,
        volume: 0.02625,
        type: 'Caixa',
      };

      mockService.create.mockResolvedValue(mockPackaging);

      const result = await controller.create(createDto);

      expect(result).toEqual(mockPackaging);
      expect(service.create).toHaveBeenCalledWith(createDto);
    });
  });

  describe('update', () => {
    it('should update an existing packaging', async () => {
      const updateDto = {
        name: 'Caixa Atualizada',
        weight: 2.5,
      };

      const updatedPackaging = { ...mockPackaging, ...updateDto };
      mockService.update.mockResolvedValue(updatedPackaging);

      const result = await controller.update('1', updateDto);

      expect(result).toEqual(updatedPackaging);
      expect(service.update).toHaveBeenCalledWith('1', updateDto);
    });

    it('should throw NotFoundException when packaging not found', async () => {
      mockService.update.mockRejectedValue(new NotFoundException('Embalagem não encontrada'));

      await expect(controller.update('999', { name: 'Test' })).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('delete', () => {
    it('should delete a packaging and return success message', async () => {
      mockService.delete.mockResolvedValue(true);

      const result = await controller.delete('1');

      expect(result).toEqual({ message: 'Embalagem excluída com sucesso' });
      expect(service.delete).toHaveBeenCalledWith('1');
    });

    it('should throw NotFoundException when packaging not found', async () => {
      mockService.delete.mockRejectedValue(new NotFoundException('Embalagem não encontrada'));

      await expect(controller.delete('999')).rejects.toThrow(NotFoundException);
    });
  });

  describe('findOne', () => {
    it('should return a packaging by id', async () => {
      mockService.findOne.mockResolvedValue(mockPackaging);

      const result = await controller.findOne('1');

      expect(result).toEqual(mockPackaging);
      expect(service.findOne).toHaveBeenCalledWith('1');
    });

    it('should throw NotFoundException when packaging not found', async () => {
      mockService.findOne.mockResolvedValue(null);

      await expect(controller.findOne('999')).rejects.toThrow(NotFoundException);
    });
  });
});
