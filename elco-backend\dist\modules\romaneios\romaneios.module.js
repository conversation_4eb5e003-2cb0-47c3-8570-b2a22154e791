"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RomaneiosModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const romaneios_service_1 = require("./romaneios.service");
const romaneios_controller_1 = require("./romaneios.controller");
const romaneio_entity_1 = require("./entities/romaneio.entity");
const romaneio_draft_entity_1 = require("./entities/romaneio-draft.entity");
const mega_module_1 = require("../mega/mega.module");
const invoices_module_1 = require("../invoices/invoices.module");
const oracle_module_1 = require("../oracle/oracle.module");
let RomaneiosModule = class RomaneiosModule {
};
exports.RomaneiosModule = RomaneiosModule;
exports.RomaneiosModule = RomaneiosModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([romaneio_entity_1.Romaneio, romaneio_draft_entity_1.RomaneioDraft]),
            mega_module_1.MegaModule,
            invoices_module_1.InvoicesModule,
            oracle_module_1.OracleModule,
        ],
        controllers: [romaneios_controller_1.RomaneiosController],
        providers: [romaneios_service_1.RomaneiosService],
        exports: [romaneios_service_1.RomaneiosService, typeorm_1.TypeOrmModule],
    })
], RomaneiosModule);
//# sourceMappingURL=romaneios.module.js.map