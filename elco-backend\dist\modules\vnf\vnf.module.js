"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VnfModule = void 0;
const common_1 = require("@nestjs/common");
const vnf_service_1 = require("./vnf.service");
const vnf_controller_1 = require("./vnf.controller");
const email_service_1 = require("./email.service");
const typeorm_1 = require("@nestjs/typeorm");
const vnf_error_entity_1 = require("./vnf-error.entity");
const vnf_consulta_processada_entity_1 = require("./entities/vnf-consulta-processada.entity");
const invoice_entity_1 = require("../invoices/entities/invoice.entity");
const invoice_item_entity_1 = require("../invoices/entities/invoice-item.entity");
const product_entity_1 = require("../products/entities/product.entity");
const invoices_module_1 = require("../invoices/invoices.module");
const products_module_1 = require("../products/products.module");
const separation_order_entity_1 = require("../separation-orders/entity/separation-order.entity");
const romaneio_entity_1 = require("../romaneios/entities/romaneio.entity");
const romaneio_order_entity_1 = require("../romaneio-orders/entities/romaneio-order.entity");
const supplier_email_entity_1 = require("../supplier-email/entity/supplier-email.entity");
const oracle_module_1 = require("../oracle/oracle.module");
let VnfModule = class VnfModule {
};
exports.VnfModule = VnfModule;
exports.VnfModule = VnfModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                invoice_entity_1.Invoice,
                invoice_item_entity_1.InvoiceItem,
                product_entity_1.Product,
                vnf_error_entity_1.VnfError,
                vnf_consulta_processada_entity_1.VnfConsultaProcessada,
                separation_order_entity_1.SeparationOrder,
                romaneio_entity_1.Romaneio,
                romaneio_order_entity_1.RomaneioOrder,
                supplier_email_entity_1.SupplierEmail,
            ]),
            (0, common_1.forwardRef)(() => invoices_module_1.InvoicesModule),
            (0, common_1.forwardRef)(() => products_module_1.ProductsModule),
            oracle_module_1.OracleModule,
        ],
        providers: [vnf_service_1.VnfService, email_service_1.EmailService],
        controllers: [vnf_controller_1.VnfController],
        exports: [vnf_service_1.VnfService],
    })
], VnfModule);
//# sourceMappingURL=vnf.module.js.map