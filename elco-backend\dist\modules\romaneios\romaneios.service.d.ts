import { CreateProductPackagingDto, CreateRomaneioDto } from './dto/create-romaneio.dto';
import { UpdateRomaneioDto } from './dto/update-romaneio.dto';
import { DataSource, QueryRunner, Repository } from 'typeorm';
import { Romaneio } from './entities/romaneio.entity';
import { MegaService } from '../mega/mega.service';
import { RomaneioDraft } from './entities/romaneio-draft.entity';
export declare class RomaneiosService {
    private readonly repository;
    private readonly draftRepository;
    private readonly dataSource;
    private readonly megaService;
    private readonly oracleConnectionFactory;
    constructor(repository: Repository<Romaneio>, draftRepository: Repository<RomaneioDraft>, dataSource: DataSource, megaService: MegaService, oracleConnectionFactory: () => Promise<any>);
    private findProductByCode;
    create(createRomaneioDto: CreateRomaneioDto): Promise<{
        response: CreateRomaneioDto;
    }>;
    findAll(): Promise<any>;
    saveProductsBySeparationOrderId(queryRunner: QueryRunner, separationOrderId: number, apiKey: string, romaneioId: number, productPackagings: CreateProductPackagingDto[]): Promise<void>;
    private prepareOracleData;
    private createRomaneioOrders;
    private removeDrafts;
    private handlePostCreationTasks;
    private saveAllProducts;
    private saveProductsBySeparationOrderIdExternal;
    findOne(id: number): Promise<Romaneio | null>;
    update(id: number, updateRomaneioDto: UpdateRomaneioDto): Promise<Romaneio | null>;
    deleteRomaneio(id: number): Promise<{
        message: string;
        deletedProducts: boolean;
        deletedRomaneioOrders: number;
        updatedSeparationOrders: number;
    }>;
    saveDraft(dto: CreateRomaneioDto): Promise<RomaneioDraft>;
    getDraftByExternalCode(externalCode: string): Promise<{
        data: any;
        id: number;
        externalCode: string;
        createdAt: Date;
        updatedAt: Date;
    } | null>;
    calcularPesoRomaneio(romaneioId: number): Promise<{
        pesoLiquido: number;
        pesoBruto: number;
    }>;
    private autoVerifyOldRomaneios;
}
