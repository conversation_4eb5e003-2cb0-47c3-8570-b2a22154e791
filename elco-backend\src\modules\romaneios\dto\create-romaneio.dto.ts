import { Transform, Type } from 'class-transformer';
import {
  IsDateString,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { CreateRomaneioOrderDto } from 'src/modules/romaneio-orders/dto/create-romaneio-order.dto';

export class CreateProductPackagingDto {
  @IsString()
  readonly orderCode: string;

  @IsString()
  readonly productCode: string;

  @IsString()
  @IsOptional()
  readonly productName: string;

  @IsNumber()
  @IsOptional()
  readonly productQuantity: number;

  @IsString()
  @IsOptional()
  readonly productType: string;

  @IsNumber()
  readonly packagingId: number;

  @IsNumber()
  @IsOptional()
  readonly secondaryVolume: number;

  @IsNumber()
  @IsOptional()
  readonly totalSecondaryVolumes: number;

  @IsNumber()
  @IsOptional()
  readonly productWeight: number;
}

export class CreateRomaneioDto {
  @IsOptional()
  @IsDateString()
  @Transform(({ value }) => {
    const [day, month, year] = value.split('/');
    return `${year}-${month}-${day}`;
  })
  readonly dateIssue: Date;

  @IsString()
  @IsOptional()
  readonly address: string;

  @IsNumber()
  @IsOptional()
  readonly carrierId: number;

  @IsNumber()
  @IsOptional()
  readonly driverId: number;

  @IsNumber()
  @IsOptional()
  readonly vehicleId: number;

  @IsNumber()
  @IsOptional()
  readonly totalLength: number;

  @IsNumber()
  @IsOptional()
  readonly totalWidth: number;

  @IsNumber()
  @IsOptional()
  readonly totalHeight: number;

  @IsNumber()
  @IsOptional()
  readonly totalWeight: number;

  @IsNumber()
  @IsOptional()
  readonly totalVolume: number;

  @IsNumber()
  @IsOptional()
  readonly mainPackagingId: number;

  @IsNumber()
  @IsOptional()
  readonly secondaryPackagingId?: number;

  @IsNumber()
  @IsOptional()
  readonly userId: number;

  @IsString()
  @IsOptional()
  readonly linking: string;

  @IsString()
  @IsOptional()
  readonly observations: string;

  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateRomaneioOrderDto)
  readonly romaneioOrders: CreateRomaneioOrderDto[];

  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateProductPackagingDto)
  readonly productPackagings: CreateProductPackagingDto[];
}
