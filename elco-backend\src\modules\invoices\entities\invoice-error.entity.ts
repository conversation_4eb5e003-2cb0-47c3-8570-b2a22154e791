import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
} from 'typeorm';

@Entity('invoice_errors')
export class InvoiceError {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'id_romaneio' })
  idRomaneio: number;

  @Column({ name: 'id_ordem' })
  idOrdem: number;

  @Column({ name: 'error_message', type: 'text' })
  errorMessage: string;

  @Column({ name: 'xml_enviado', type: 'text', nullable: true })
  xmlEnviado: string;

  @Column({ name: 'soap_envelope', type: 'text', nullable: true })
  soapEnvelope: string;

  @Column({
    name: 'data',
    type: 'datetime',
    nullable: true,
    default: () => 'CURRENT_TIMESTAMP',
  })
  data: Date;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @DeleteDateColumn({ name: 'deleted_at' })
  deletedAt: Date;
}
