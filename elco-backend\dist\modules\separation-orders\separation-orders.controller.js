"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SeparationOrderController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const separation_orders_service_1 = require("./separation-orders.service");
let SeparationOrderController = class SeparationOrderController {
    service;
    constructor(service) {
        this.service = service;
    }
    getAll(page = 1, limit = 10, search, status, depositorName, clientFilter) {
        const filters = { search, status, depositorName, clientFilter };
        return this.service.findAll(page, limit, filters);
    }
    getStats() {
        return this.service.getStats();
    }
    getFilterOptions() {
        return this.service.getFilterOptions();
    }
    async fetchExternalProducts(internalCode) {
        return this.service.getProductDetails(internalCode);
    }
    async linkOrder(internalCode, body) {
        return this.service.linkOrder(internalCode, body.tipo);
    }
    async syncOrderVolume(body) {
        return this.service.linkOrderVolume(body.orderId, body.volume);
    }
};
exports.SeparationOrderController = SeparationOrderController;
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Listar todos os pedidos de separação',
        description: 'Retorna uma lista paginada com todos os pedidos de separação cadastrados no sistema.',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        required: false,
        type: Number,
        description: 'Número da página (padrão: 1)',
        example: 1,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Quantidade de itens por página (padrão: 10)',
        example: 10,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'search',
        required: false,
        type: String,
        description: 'Termo de busca (código interno, externo ou depositante)',
        example: 'SEP001',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'status',
        required: false,
        type: String,
        description: 'Filtrar por status',
        example: 'PENDING',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'depositorName',
        required: false,
        type: String,
        description: 'Filtrar por depositante',
        example: 'Depositante Exemplo',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'clientFilter',
        required: false,
        type: String,
        description: 'Filtrar por cliente (código externo)',
        example: 'CLI001',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista paginada de pedidos de separação',
        schema: {
            type: 'object',
            properties: {
                orders: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            id: { type: 'number', example: 1 },
                            internalCode: { type: 'string', example: 'SEP001' },
                            externalCode: { type: 'string', example: 'EXT001' },
                            status: {
                                type: 'string',
                                enum: ['PENDING', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED'],
                                example: 'PENDING',
                            },
                            priority: { type: 'string', example: 'HIGH' },
                            type: {
                                type: 'string',
                                enum: ['MATRIZ', 'FILIAL'],
                                example: 'MATRIZ',
                            },
                            createdAt: { type: 'string', format: 'date-time' },
                            products: {
                                type: 'array',
                                items: {
                                    type: 'object',
                                    properties: {
                                        productCode: { type: 'string', example: 'PROD001' },
                                        quantity: { type: 'number', example: 10 },
                                        separated: { type: 'number', example: 8 },
                                    },
                                },
                            },
                        },
                    },
                },
                total: {
                    type: 'number',
                    example: 100,
                    description: 'Total de registros',
                },
                page: { type: 'number', example: 1, description: 'Página atual' },
                limit: { type: 'number', example: 10, description: 'Itens por página' },
                totalPages: {
                    type: 'number',
                    example: 10,
                    description: 'Total de páginas',
                },
            },
        },
    }),
    __param(0, (0, common_1.Query)('page')),
    __param(1, (0, common_1.Query)('limit')),
    __param(2, (0, common_1.Query)('search')),
    __param(3, (0, common_1.Query)('status')),
    __param(4, (0, common_1.Query)('depositorName')),
    __param(5, (0, common_1.Query)('clientFilter')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, String, String, String, String]),
    __metadata("design:returntype", void 0)
], SeparationOrderController.prototype, "getAll", null);
__decorate([
    (0, common_1.Get)('stats'),
    (0, swagger_1.ApiOperation)({
        summary: 'Obter estatísticas gerais dos pedidos',
        description: 'Retorna estatísticas gerais de todos os pedidos de separação.',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Estatísticas gerais dos pedidos',
        schema: {
            type: 'object',
            properties: {
                totalOrders: { type: 'number', example: 150 },
                completedCount: { type: 'number', example: 45 },
                linkedCount: { type: 'number', example: 60 },
                pendingCount: { type: 'number', example: 40 },
                errorCount: { type: 'number', example: 5 },
                completedPercentage: { type: 'number', example: 30 },
                linkedPercentage: { type: 'number', example: 40 },
                pendingPercentage: { type: 'number', example: 27 },
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], SeparationOrderController.prototype, "getStats", null);
__decorate([
    (0, common_1.Get)('filter-options'),
    (0, swagger_1.ApiOperation)({
        summary: 'Obter opções para filtros',
        description: 'Retorna listas de valores únicos para uso em filtros.',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Opções disponíveis para filtros',
        schema: {
            type: 'object',
            properties: {
                statuses: {
                    type: 'array',
                    items: { type: 'string' },
                    example: ['PENDING', 'LINKED', 'ROMANIO'],
                },
                depositors: {
                    type: 'array',
                    items: { type: 'string' },
                    example: ['Depositante A', 'Depositante B'],
                },
                clients: {
                    type: 'array',
                    items: { type: 'string' },
                    example: ['CLI001 - Cliente A', 'CLI002 - Cliente B'],
                },
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], SeparationOrderController.prototype, "getFilterOptions", null);
__decorate([
    (0, common_1.Get)('external-products/:internalCode'),
    (0, swagger_1.ApiOperation)({
        summary: 'Buscar produtos externos por código interno',
        description: 'Busca informações de produtos em sistemas externos através do código interno do pedido.',
    }),
    (0, swagger_1.ApiParam)({
        name: 'internalCode',
        description: 'Código interno do pedido de separação',
        type: 'string',
        example: 'SEP001',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Detalhes dos produtos encontrados',
        schema: {
            type: 'object',
            properties: {
                internalCode: { type: 'string', example: 'SEP001' },
                found: { type: 'boolean', example: true },
                products: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            code: { type: 'string', example: 'PROD001' },
                            name: { type: 'string', example: 'Produto Exemplo' },
                            description: { type: 'string', example: 'Descrição do produto' },
                            stock: { type: 'number', example: 100 },
                            location: { type: 'string', example: 'A-01-15' },
                            weight: { type: 'number', example: 1.5 },
                        },
                    },
                },
                lastUpdate: { type: 'string', format: 'date-time' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Pedido não encontrado ou sem produtos',
    }),
    __param(0, (0, common_1.Param)('internalCode')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SeparationOrderController.prototype, "fetchExternalProducts", null);
__decorate([
    (0, common_1.Patch)('vincular/:internalCode'),
    (0, swagger_1.ApiOperation)({
        summary: 'Vincular pedido de separação',
        description: 'Vincula um pedido de separação a um tipo específico (MATRIZ ou FILIAL).',
    }),
    (0, swagger_1.ApiParam)({
        name: 'internalCode',
        description: 'Código interno do pedido de separação',
        type: 'string',
        example: 'SEP001',
    }),
    (0, swagger_1.ApiBody)({
        description: 'Tipo de vinculação',
        schema: {
            type: 'object',
            properties: {
                tipo: {
                    type: 'string',
                    enum: ['MATRIZ', 'FILIAL'],
                    example: 'MATRIZ',
                },
            },
            required: ['tipo'],
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Pedido vinculado com sucesso',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                internalCode: { type: 'string', example: 'SEP001' },
                linkedTo: { type: 'string', example: 'MATRIZ' },
                message: { type: 'string', example: 'Pedido vinculado com sucesso' },
                updatedAt: { type: 'string', format: 'date-time' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Pedido não encontrado' }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Tipo inválido ou pedido já vinculado',
    }),
    __param(0, (0, common_1.Param)('internalCode')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], SeparationOrderController.prototype, "linkOrder", null);
__decorate([
    (0, common_1.Patch)('/order-volume'),
    (0, swagger_1.ApiOperation)({
        summary: 'Sincronizar volume do pedido',
        description: 'Atualiza o volume/quantidade de um pedido específico.',
    }),
    (0, swagger_1.ApiBody)({
        description: 'Dados para sincronização de volume',
        schema: {
            type: 'object',
            properties: {
                orderId: { type: 'number', example: 123 },
                volume: { type: 'number', example: 15.5 },
            },
            required: ['orderId', 'volume'],
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Volume sincronizado com sucesso',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                orderId: { type: 'number', example: 123 },
                previousVolume: { type: 'number', example: 10.0 },
                newVolume: { type: 'number', example: 15.5 },
                message: { type: 'string', example: 'Volume atualizado com sucesso' },
                updatedAt: { type: 'string', format: 'date-time' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Pedido não encontrado' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Volume inválido' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], SeparationOrderController.prototype, "syncOrderVolume", null);
exports.SeparationOrderController = SeparationOrderController = __decorate([
    (0, swagger_1.ApiTags)('Pedidos de Separação'),
    (0, common_1.Controller)('separation-orders'),
    __metadata("design:paramtypes", [separation_orders_service_1.SeparationOrderService])
], SeparationOrderController);
//# sourceMappingURL=separation-orders.controller.js.map