# Configuração SMTP para Envio de Emails

## Gmail

### 1. Ativar Autenticação de Dois Fatores
1. Acesse sua conta Google
2. Vá em **Segurança** → **Verificação em duas etapas**
3. Ative a verificação em duas etapas

### 2. Gerar Senha de App
1. Em **Segurança**, procure por **Senhas de app**
2. Clique em **Senhas de app**
3. Selecione **Email** como aplicativo
4. Clique em **Gerar**
5. **Copie a senha de 16 caracteres** (exemplo: `abcd efgh ijkl mnop`)

### 3. Configurar Variáveis de Ambiente
Adicione ao seu arquivo `.env`:

```env
# Configurações SMTP para Gmail
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=sua-senha-de-app-16-caracteres
SMTP_FROM=<EMAIL>
SMTP_FROM_NAME=Elco Engenharia
```

### 4. Testar Conexão
Use o endpoint `/vnf/testar-email` para testar a conexão SMTP.

## Outlook/Hotmail

### 1. Gerar Senha de App
1. Acesse https://account.live.com/proofs/AppPassword
2. Faça login com sua conta Microsoft
3. Clique em **Criar uma nova senha de aplicativo**
4. Copie a senha gerada

### 2. Configurar Variáveis
```env
SMTP_HOST=smtp-mail.outlook.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=sua-senha-de-app
SMTP_FROM=<EMAIL>
SMTP_FROM_NAME=Elco Engenharia
```

## Yahoo

### 1. Gerar Senha de App
1. Acesse https://login.yahoo.com/account/security
2. Vá em **Senhas de app**
3. Clique em **Gerar senha de app**
4. Copie a senha gerada

### 2. Configurar Variáveis
```env
SMTP_HOST=smtp.mail.yahoo.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=sua-senha-de-app
SMTP_FROM=<EMAIL>
SMTP_FROM_NAME=Elco Engenharia
```

## Dicas Importantes

1. **Nunca use sua senha normal** - sempre use senha de app
2. **Mantenha a senha segura** - não compartilhe ou commite no git
3. **Teste sempre** - use o endpoint de teste antes de usar em produção
4. **Limite de envios** - Gmail tem limite de 500 emails/dia para contas gratuitas
5. **Logs** - verifique os logs do servidor para debug

## Troubleshooting

### Erro: "Invalid login"
- Verifique se a senha de app está correta
- Confirme se a autenticação de dois fatores está ativa

### Erro: "Connection timeout"
- Verifique se o firewall não está bloqueando a porta 587
- Teste a conectividade: `telnet smtp.gmail.com 587`

### Erro: "Authentication failed"
- Regenerar a senha de app
- Verificar se o email está correto 