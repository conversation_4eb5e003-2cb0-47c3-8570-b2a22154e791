"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MegaService = void 0;
const common_1 = require("@nestjs/common");
const xml2js_1 = require("xml2js");
const axios_1 = require("axios");
const xml2js_2 = require("xml2js");
const https = require("https");
const fs = require("fs");
const path = require("path");
const zlib = require("zlib");
const node_pdf_nfe_1 = require("@alexssmusica/node-pdf-nfe");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const invoice_entity_1 = require("../invoices/entities/invoice.entity");
const invoice_item_entity_1 = require("../invoices/entities/invoice-item.entity");
const invoice_error_entity_1 = require("../invoices/entities/invoice-error.entity");
const util_1 = require("util");
const product_entity_1 = require("../products/entities/product.entity");
const romaneio_entity_1 = require("../romaneios/entities/romaneio.entity");
let MegaService = class MegaService {
    soapClient;
    oracleConnectionFactory;
    invoiceRepository;
    invoiceItemRepository;
    invoiceErrorRepository;
    productRepository;
    romaneioRepository;
    constructor(soapClient, oracleConnectionFactory, invoiceRepository, invoiceItemRepository, invoiceErrorRepository, productRepository, romaneioRepository) {
        this.soapClient = soapClient;
        this.oracleConnectionFactory = oracleConnectionFactory;
        this.invoiceRepository = invoiceRepository;
        this.invoiceItemRepository = invoiceItemRepository;
        this.invoiceErrorRepository = invoiceErrorRepository;
        this.productRepository = productRepository;
        this.romaneioRepository = romaneioRepository;
    }
    async xmlParaDanfeBase64(xml) {
        const doc = await (0, node_pdf_nfe_1.gerarPDF)(xml, { cancelada: false });
        const stream = new (await Promise.resolve().then(() => require('stream'))).PassThrough();
        const buffers = [];
        return await new Promise((resolve, reject) => {
            doc.pipe(stream);
            stream.on('data', (chunk) => buffers.push(chunk));
            stream.on('finish', () => resolve(Buffer.concat(buffers).toString('base64')));
            doc.on('error', reject);
        });
    }
    async gerarComMeuDanfe(xml) {
        try {
            const { data } = await axios_1.default.post('https://ws.meudanfe.com/api/v1/get/nfe/xmltodanfepdf/API', xml, {
                headers: { 'Content-Type': 'text/plain' },
                timeout: 30000,
            });
            return typeof data === 'string' ? data.replace(/"/g, '') : null;
        }
        catch (err) {
            console.error('[MegaService] MeuDanfe falhou:', err);
            return null;
        }
    }
    async extrairPKMega(respostaXml) {
        const resultado = await (0, xml2js_2.parseStringPromise)(respostaXml);
        const pkmega = resultado['SOAP-ENV:Envelope']['SOAP-ENV:Body'][0]['v1:MegaIntegradorService___IntegraXMLResponse'][0]['v1:Result'][0]['v1:PKMega'][0];
        const numeros = pkmega.split(';');
        return numeros[numeros.length - 1];
    }
    async consultarNotaOracle(numeroNota) {
        const queryNotaFiscal = `
      SELECT * FROM ELCO.ven_notafiscal@ELCO
      WHERE NOT_IN_CODIGO = :numeroNota
      ORDER BY NOT_DT_EMISSAO DESC
    `;
        const queryItemNotaFiscal = `
      SELECT * FROM ELCO.ven_itemnotafiscal@ELCO
      WHERE NOT_IN_CODIGO = :numeroNota
    `;
        const connection = await this.oracleConnectionFactory();
        try {
            const notaFiscalResult = await connection.execute(queryNotaFiscal, [numeroNota]);
            const itemNotaFiscalResult = await connection.execute(queryItemNotaFiscal, [numeroNota]);
            const colsNota = Array.isArray(notaFiscalResult.metaData)
                ? notaFiscalResult.metaData.map((m) => m.name)
                : [];
            const colsItem = Array.isArray(itemNotaFiscalResult.metaData)
                ? itemNotaFiscalResult.metaData.map((m) => m.name)
                : [];
            const notaFiscal = Array.isArray(notaFiscalResult.rows)
                ? notaFiscalResult.rows.map((row) => {
                    const obj = {};
                    colsNota.forEach((col, idx) => {
                        const val = row[idx];
                        if (val !== null && !(typeof val === 'number' && val === 0)) {
                            obj[col] = val;
                        }
                    });
                    return obj;
                })
                : [];
            const itemNotaFiscal = Array.isArray(itemNotaFiscalResult.rows)
                ? itemNotaFiscalResult.rows.map((row) => {
                    const obj = {};
                    colsItem.forEach((col, idx) => {
                        const val = row[idx];
                        if (val !== null && !(typeof val === 'number' && val === 0)) {
                            obj[col] = val;
                        }
                    });
                    return obj;
                })
                : [];
            let chaveAcessoXml = null;
            if (notaFiscal.length > 0) {
                const chave = notaFiscal[0].NOT_ST_CHAVEACESSO;
                if (typeof chave === 'string' && chave) {
                    chaveAcessoXml = chave;
                }
            }
            return { notaFiscal, itemNotaFiscal, chaveAcessoXml };
        }
        finally {
            await connection.close();
        }
    }
    async consultarXml(chaveAcesso) {
        const pfxPath = path.resolve(__dirname, '../../../certs/ELCO_ENGENHARIA_LTDA_77521375000121.pfx');
        const gunzip = (0, util_1.promisify)(zlib.gunzip);
        const httpsAgent = new https.Agent({
            pfx: fs.readFileSync(pfxPath),
            passphrase: process.env.PFX_PASSPHRASE,
        });
        const envelope = `<?xml version="1.0" encoding="UTF-8"?>
  <soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Header/>
    <soap:Body>
      <nfeDistDFeInteresse xmlns="http://www.portalfiscal.inf.br/nfe/wsdl/NFeDistribuicaoDFe">
        <nfeDadosMsg>
          <distDFeInt versao="1.01" xmlns="http://www.portalfiscal.inf.br/nfe">
            <tpAmb>1</tpAmb>
            <CNPJ>77521375000121</CNPJ>
            <consChNFe>
              <chNFe>${chaveAcesso}</chNFe>
            </consChNFe>
          </distDFeInt>
        </nfeDadosMsg>
      </nfeDistDFeInteresse>
    </soap:Body>
  </soap:Envelope>`;
        const url = 'https://www1.nfe.fazenda.gov.br/NFeDistribuicaoDFe/NFeDistribuicaoDFe.asmx';
        try {
            const { data } = await axios_1.default.post(url, envelope, {
                httpsAgent,
                headers: {
                    'Content-Type': 'text/xml;charset=utf-8',
                    SOAPAction: 'http://www.portalfiscal.inf.br/nfe/wsdl/NFeDistribuicaoDFe/nfeDistDFeInteresse',
                },
                timeout: 30000,
            });
            const parsed = await (0, xml2js_2.parseStringPromise)(data, { explicitArray: false });
            const retDist = parsed['soap:Envelope']['soap:Body']['nfeDistDFeInteresseResponse']['nfeDistDFeInteresseResult']['retDistDFeInt'];
            const cStat = retDist.cStat;
            const xMotivo = retDist.xMotivo;
            if (cStat !== '138') {
                return {
                    success: false,
                    status: cStat,
                    message: xMotivo,
                    xml: null,
                    pdfBase64: null,
                };
            }
            const rawDocZip = retDist?.loteDistDFeInt?.docZip;
            const docZipArr = Array.isArray(rawDocZip) ? rawDocZip : rawDocZip ? [rawDocZip] : [];
            let procNfeXml = null;
            for (const item of docZipArr) {
                try {
                    const base64 = typeof item === 'string' ? item : item._;
                    const xml = (await gunzip(Buffer.from(base64, 'base64'))).toString('utf-8');
                    if (xml.includes('<nfeProc')) {
                        procNfeXml = xml;
                        break;
                    }
                }
                catch {
                    continue;
                }
            }
            if (!procNfeXml) {
                return {
                    success: true,
                    status: cStat,
                    message: 'NF-e não encontrada no lote.',
                    xml: null,
                    pdfBase64: null,
                };
            }
            let pdfBase64 = null;
            try {
                pdfBase64 = await this.xmlParaDanfeBase64(procNfeXml);
            }
            catch (err) {
                console.error('[MegaService] Gerador local falhou, tentando MeuDanfe…', err);
                pdfBase64 = await this.gerarComMeuDanfe(procNfeXml);
            }
            return {
                success: true,
                status: cStat,
                message: xMotivo,
                xml: procNfeXml,
                pdfBase64,
            };
        }
        catch (error) {
            if (axios_1.default.isAxiosError(error)) {
                const err = error;
                console.error('Erro HTTP SEFAZ:', err.response?.status, err.response?.data);
            }
            else {
                console.error('Erro ao consultar XML na SEFAZ:', error);
            }
            throw new Error('Falha na consulta à SEFAZ');
        }
    }
    async enviarNotaFiscal(dados, idOrdem, idRomaneio) {
        const { pPRO_IN_ID, pUSU_IN_CODIGO, pTransacao, pSistema, notaFiscal } = dados;
        const notaFiscalObj = {
            NotaFiscal: {
                $: { OPERACAO: 'I' },
                ...notaFiscal
            }
        };
        if (Array.isArray(notaFiscal.ItemNotaFiscal)) {
            notaFiscalObj.NotaFiscal.ItemNotaFiscal = notaFiscal.ItemNotaFiscal.map(item => ({
                $: { OPERACAO: 'I' },
                ...item
            }));
        }
        else if (notaFiscal.ItemNotaFiscal) {
            notaFiscalObj.NotaFiscal.ItemNotaFiscal = {
                $: { OPERACAO: 'I' },
                ...notaFiscal.ItemNotaFiscal
            };
        }
        if (Array.isArray(notaFiscal.VolumeNF)) {
            notaFiscalObj.NotaFiscal.VolumeNF = notaFiscal.VolumeNF.map(volume => ({
                $: { OPERACAO: 'I' },
                ...volume
            }));
        }
        else if (notaFiscal.VolumeNF) {
            notaFiscalObj.NotaFiscal.VolumeNF = {
                $: { OPERACAO: 'I' },
                ...notaFiscal.VolumeNF
            };
        }
        if (notaFiscal.ObservacaoNF) {
            notaFiscalObj.NotaFiscal.ObservacaoNF = {
                $: { OPERACAO: 'I' },
                ...notaFiscal.ObservacaoNF
            };
        }
        const builder = new xml2js_1.Builder({
            headless: true,
            xmldec: { version: '1.0', encoding: 'utf-8' }
        });
        const notaFiscalXml = builder.buildObject(notaFiscalObj);
        const soapEnvelope = `<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/"
               xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:wsa="http://schemas.xmlsoap.org/ws/2004/08/addressing">
  <soap:Body>
    <MegaIntegradorService___IntegraXML xmlns="http://tempuri.org/">
      <pPRO_IN_ID>${pPRO_IN_ID}</pPRO_IN_ID>
      <pUSU_IN_CODIGO>${pUSU_IN_CODIGO}</pUSU_IN_CODIGO>
      <pXML><![CDATA[${notaFiscalXml}]]></pXML>
      <pTransacao>${pTransacao}</pTransacao>
      <pSistema>${pSistema}</pSistema>
    </MegaIntegradorService___IntegraXML>
  </soap:Body>
</soap:Envelope>`;
        try {
            const response = await axios_1.default.post('http://wservice.megaerp.online:17176/Integrador', soapEnvelope, {
                headers: {
                    'Content-Type': 'text/xml; charset=utf-8'
                }
            });
            console.log("🚀 ~ MegaService ~ enviarNotaFiscal ~ response:", response.data);
            const resultado = await (0, xml2js_2.parseStringPromise)(response.data);
            const erro = resultado['SOAP-ENV:Envelope']['SOAP-ENV:Body'][0]['v1:MegaIntegradorService___IntegraXMLResponse'][0]['v1:Result'][0]['v1:Erro'][0];
            const mensagem = resultado['SOAP-ENV:Envelope']['SOAP-ENV:Body'][0]['v1:MegaIntegradorService___IntegraXMLResponse'][0]['v1:Result'][0]['v1:Mensagem'][0];
            if (erro === 'true') {
                const invoiceError = this.invoiceErrorRepository.create({
                    idRomaneio,
                    idOrdem,
                    errorMessage: mensagem,
                    xmlEnviado: notaFiscalXml,
                    soapEnvelope
                });
                await this.invoiceErrorRepository.save(invoiceError);
                console.log(`[MegaService] Erro salvo na tabela invoice_errors para romaneio ${idRomaneio} e ordem ${idOrdem}: ${mensagem}`);
                await this.updateRomaneioStatusWithRetry(idRomaneio, 3);
                return {
                    success: false,
                    erro: mensagem,
                    xmlEnviado: notaFiscalXml,
                    soapEnvelope
                };
            }
            const numeroNota = await this.extrairPKMega(response.data);
            const notaOracle = await this.consultarNotaOracle(numeroNota);
            const nota = notaOracle.notaFiscal[0];
            if (notaOracle.notaFiscal.length > 0) {
                const numeroDanfe = nota.NOT_IN_NUMERO.toString().padStart(9, '0').replace(/(\\d{3})(\\d{3})(\\d{3})/, '$1 $2 $3');
                const invoice = this.invoiceRepository.create({
                    idOrdem,
                    idRomaneio,
                    filInCodigo: nota.FIL_IN_CODIGO,
                    notInCodigo: nota.NOT_IN_CODIGO,
                    notInNumero: nota.NOT_IN_NUMERO,
                    numeroDanfe,
                    tpdInCodigo: nota.TPD_IN_CODIGO,
                    notDtEmissao: nota.NOT_DT_EMISSAO,
                    notHrHoraemissao: nota.NOT_HR_HORAEMISSAO,
                    notDtSaida: nota.NOT_DT_SAIDA,
                    notHrHorasaida: nota.NOT_HR_HORASAIDA,
                    notStUf: nota.NOT_ST_UF,
                    notStMunicipio: nota.NOT_ST_MUNICIPIO,
                    notStCgc: nota.NOT_ST_CGC,
                    notStIncrestadual: nota.NOT_ST_INCRESTADUAL,
                    cfopStDescricao: nota.CFOP_ST_DESCRICAO,
                    notStChaveacesso: nota.NOT_ST_CHAVEACESSO,
                    ccfInReduzido: nota.CCF_IN_REDUZIDO,
                    projeto: nota.PROJ_IN_REDUZIDO.toString(),
                    status: 'PENDING',
                });
                const savedInvoice = await this.invoiceRepository.save(invoice);
                if (notaOracle.itemNotaFiscal.length > 0) {
                    const invoiceItems = notaOracle.itemNotaFiscal.map(item => this.invoiceItemRepository.create({
                        invoiceId: savedInvoice.id,
                        notInCodigo: item.NOT_IN_CODIGO,
                        notInNumero: nota.NOT_IN_NUMERO,
                        itnStDescricao: item.ITN_ST_DESCRICAO,
                        itnReValorunitario: item.ITN_RE_VALORUNITARIO,
                        itnStNcmExtenso: item.ITN_ST_NCM_EXTENSO,
                        itnReValortotal: item.ITN_RE_VALORTOTAL,
                        itnReQuantidade: item.ITN_RE_QUANTIDADE,
                        stpStCstpis: item.STP_ST_CSTPIS,
                        stcStCstcofins: item.STC_ST_CSTCOFINS,
                        idRomaneio,
                        idOrdem,
                    }));
                    await this.invoiceItemRepository.save(invoiceItems);
                }
                console.log(`[MegaService] Atualizando tax_note_number para produtos do romaneio ${idRomaneio} e ordem ${idOrdem} com número da nota: ${nota.NOT_IN_NUMERO}`);
                const updateResult = await this.productRepository.update({ idRomanio: idRomaneio, idSeparationOrder: idOrdem }, { taxNoteNumber: nota.NOT_IN_NUMERO });
                console.log(`[MegaService] Resultado da atualização: ${updateResult.affected} produtos atualizados`);
                if (updateResult.affected === 0) {
                    console.warn(`[MegaService] Nenhum produto encontrado para atualizar com idRomanio: ${idRomaneio} e idSeparationOrder: ${idOrdem}`);
                    const updateByRomaneioResult = await this.productRepository.update({ idRomanio: idRomaneio }, { taxNoteNumber: nota.NOT_IN_NUMERO });
                    console.log(`[MegaService] Atualização por romaneio: ${updateByRomaneioResult.affected} produtos atualizados`);
                }
                await this.updateRomaneioStatusWithRetry(idRomaneio, 2);
            }
            return {
                success: true,
                xmlEnviado: notaFiscalXml,
                soapEnvelope,
                resposta: response.data,
                numeroNota,
                notaOracle
            };
        }
        catch (error) {
            const invoiceError = this.invoiceErrorRepository.create({
                idRomaneio,
                idOrdem,
                errorMessage: error.message,
                xmlEnviado: notaFiscalXml,
                soapEnvelope
            });
            await this.invoiceErrorRepository.save(invoiceError);
            console.log(`[MegaService] Erro salvo na tabela invoice_errors para romaneio ${idRomaneio} e ordem ${idOrdem}: ${error.message}`);
            await this.updateRomaneioStatusWithRetry(idRomaneio, 3);
            return {
                success: false,
                erro: error.message,
                xmlEnviado: notaFiscalXml,
                soapEnvelope
            };
        }
    }
    async reenviarXML(errorId, xmlEnviado) {
        const error = await this.invoiceErrorRepository.findOne({ where: { id: errorId } });
        if (!error) {
            throw new Error('Erro não encontrado');
        }
        const soapEnvelope = `<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/"
               xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:wsa="http://schemas.xmlsoap.org/ws/2004/08/addressing">
  <soap:Body>
    <MegaIntegradorService___IntegraXML xmlns="http://tempuri.org/">
      <pPRO_IN_ID>501</pPRO_IN_ID>
      <pUSU_IN_CODIGO>1</pUSU_IN_CODIGO>
      <pXML><![CDATA[${xmlEnviado}]]></pXML>
      <pTransacao>0</pTransacao>
      <pSistema>1</pSistema>
    </MegaIntegradorService___IntegraXML>
  </soap:Body>
</soap:Envelope>`;
        try {
            const response = await axios_1.default.post('http://wservice.megaerp.online:17176/Integrador', soapEnvelope, {
                headers: {
                    'Content-Type': 'text/xml; charset=utf-8'
                }
            });
            const resultado = await (0, xml2js_2.parseStringPromise)(response.data);
            const erro = resultado['SOAP-ENV:Envelope']['SOAP-ENV:Body'][0]['v1:MegaIntegradorService___IntegraXMLResponse'][0]['v1:Result'][0]['v1:Erro'][0];
            const mensagem = resultado['SOAP-ENV:Envelope']['SOAP-ENV:Body'][0]['v1:MegaIntegradorService___IntegraXMLResponse'][0]['v1:Result'][0]['v1:Mensagem'][0];
            if (erro === 'true') {
                error.errorMessage = mensagem;
                error.xmlEnviado = xmlEnviado;
                error.soapEnvelope = soapEnvelope;
                await this.invoiceErrorRepository.save(error);
                await this.updateRomaneioStatusWithRetry(error.idRomaneio, 3);
                return {
                    success: false,
                    erro: mensagem,
                    xmlEnviado,
                    soapEnvelope
                };
            }
            const numeroNota = await this.extrairPKMega(response.data);
            const notaOracle = await this.consultarNotaOracle(numeroNota);
            const nota = notaOracle.notaFiscal[0];
            if (notaOracle.notaFiscal.length > 0) {
                const numeroDanfe = nota.NOT_IN_NUMERO.toString().padStart(9, '0').replace(/(\\d{3})(\\d{3})(\\d{3})/, '$1 $2 $3');
                const invoice = this.invoiceRepository.create({
                    idOrdem: error.idOrdem,
                    idRomaneio: error.idRomaneio,
                    filInCodigo: nota.FIL_IN_CODIGO,
                    notInCodigo: nota.NOT_IN_CODIGO,
                    notInNumero: nota.NOT_IN_NUMERO,
                    numeroDanfe,
                    tpdInCodigo: nota.TPD_IN_CODIGO,
                    notDtEmissao: nota.NOT_DT_EMISSAO,
                    notHrHoraemissao: nota.NOT_HR_HORAEMISSAO,
                    notDtSaida: nota.NOT_DT_SAIDA,
                    notHrHorasaida: nota.NOT_HR_HORASAIDA,
                    notStUf: nota.NOT_ST_UF,
                    notStMunicipio: nota.NOT_ST_MUNICIPIO,
                    notStCgc: nota.NOT_ST_CGC,
                    notStIncrestadual: nota.NOT_ST_INCRESTADUAL,
                    cfopStDescricao: nota.CFOP_ST_DESCRICAO,
                    notStChaveacesso: nota.NOT_ST_CHAVEACESSO,
                    ccfInReduzido: nota.CCF_IN_REDUZIDO,
                    projeto: nota.PROJ_IN_REDUZIDO.toString(),
                    status: 'PENDING',
                });
                const savedInvoice = await this.invoiceRepository.save(invoice);
                if (notaOracle.itemNotaFiscal.length > 0) {
                    const invoiceItems = notaOracle.itemNotaFiscal.map(item => this.invoiceItemRepository.create({
                        invoiceId: savedInvoice.id,
                        notInCodigo: item.NOT_IN_CODIGO,
                        notInNumero: nota.NOT_IN_NUMERO,
                        itnStDescricao: item.ITN_ST_DESCRICAO,
                        itnReValorunitario: item.ITN_RE_VALORUNITARIO,
                        itnStNcmExtenso: item.ITN_ST_NCM_EXTENSO,
                        itnReValortotal: item.ITN_RE_VALORTOTAL,
                        itnReQuantidade: item.ITN_RE_QUANTIDADE,
                        stpStCstpis: item.STP_ST_CSTPIS,
                        stcStCstcofins: item.STC_ST_CSTCOFINS,
                        idRomaneio: error.idRomaneio,
                        idOrdem: error.idOrdem,
                    }));
                    await this.invoiceItemRepository.save(invoiceItems);
                }
                console.log(`[MegaService] Atualizando tax_note_number para produtos do romaneio ${error.idRomaneio} e ordem ${error.idOrdem} com número da nota: ${nota.NOT_IN_NUMERO}`);
                const updateResult = await this.productRepository.update({ idRomanio: error.idRomaneio, idSeparationOrder: error.idOrdem }, { taxNoteNumber: nota.NOT_IN_NUMERO });
                console.log(`[MegaService] Resultado da atualização: ${updateResult.affected} produtos atualizados`);
                if (updateResult.affected === 0) {
                    console.warn(`[MegaService] Nenhum produto encontrado para atualizar com idRomanio: ${error.idRomaneio} e idSeparationOrder: ${error.idOrdem}`);
                    const updateByRomaneioResult = await this.productRepository.update({ idRomanio: error.idRomaneio }, { taxNoteNumber: nota.NOT_IN_NUMERO });
                    console.log(`[MegaService] Atualização por romaneio: ${updateByRomaneioResult.affected} produtos atualizados`);
                }
                await this.updateRomaneioStatusWithRetry(error.idRomaneio, 2);
            }
            await this.invoiceErrorRepository.remove(error);
            return {
                success: true,
                mensagem: 'XML processado com sucesso',
                xmlEnviado,
                soapEnvelope,
                resposta: response.data
            };
        }
        catch (error) {
            const errorEntity = await this.invoiceErrorRepository.findOne({ where: { id: errorId } });
            if (errorEntity) {
                errorEntity.errorMessage = error.message;
                errorEntity.xmlEnviado = xmlEnviado;
                errorEntity.soapEnvelope = soapEnvelope;
                await this.invoiceErrorRepository.save(errorEntity);
                await this.updateRomaneioStatusWithRetry(errorEntity.idRomaneio, 3);
            }
            return {
                success: false,
                erro: error.message,
                xmlEnviado,
                soapEnvelope
            };
        }
    }
    async consultarXmls(chavesAcesso) {
        const resultados = [];
        for (const chaveAcesso of chavesAcesso) {
            const invoice = await this.invoiceRepository.findOne({ where: { notStChaveacesso: chaveAcesso } });
            if (!invoice) {
                resultados.push({ chaveAcesso, success: false, error: 'Nota fiscal não encontrada' });
                continue;
            }
            try {
                const xmlResult = await this.consultarXml(chaveAcesso);
                if (xmlResult.success === false) {
                    resultados.push({ chaveAcesso, success: false, error: xmlResult.message });
                }
                else {
                    invoice.xml = xmlResult.xml || null;
                    invoice.status = 'ISSUED';
                    invoice.pdfBase64 = xmlResult.pdfBase64 || null;
                    await this.invoiceRepository.save(invoice);
                    resultados.push({ chaveAcesso, success: true });
                }
            }
            catch (error) {
                resultados.push({ chaveAcesso, success: false, error: error.message });
            }
        }
        return { success: true, resultados };
    }
    async salvarPdfBase64PorChaveAcesso(chaveAcesso, pdfBase64) {
        const invoice = await this.invoiceRepository.findOne({ where: { notStChaveacesso: chaveAcesso } });
        if (!invoice) {
            return { success: false, message: 'Nota fiscal não encontrada para a chave de acesso informada.' };
        }
        invoice.pdfBase64 = pdfBase64;
        await this.invoiceRepository.save(invoice);
        return { success: true, message: 'PDF Base64 salvo com sucesso.' };
    }
    async updateRomaneioStatusWithRetry(romaneioId, statusId, maxRetries = 3) {
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                await this.romaneioRepository.update(romaneioId, { statusId });
                console.log(`[MegaService] Status do romaneio ${romaneioId} atualizado para ${statusId} na tentativa ${attempt}`);
                return;
            }
            catch (error) {
                if (error.code === 'ER_LOCK_WAIT_TIMEOUT' && attempt < maxRetries) {
                    const delay = attempt * 1000;
                    console.log(`[MegaService] Lock timeout na tentativa ${attempt}, tentando novamente em ${delay}ms...`);
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
                else {
                    console.error(`[MegaService] Erro ao atualizar status do romaneio ${romaneioId} após ${attempt} tentativas:`, error);
                    throw error;
                }
            }
        }
    }
    async consultarProdutosRomaneio(romaneioId) {
        try {
            const produtos = await this.productRepository.find({
                where: { idRomanio: romaneioId },
                select: ['id', 'idRomanio', 'idSeparationOrder', 'name', 'taxNoteNumber']
            });
            return {
                success: true,
                produtos
            };
        }
        catch (error) {
            console.error('[MegaService] Erro ao consultar produtos do romaneio:', error);
            return {
                success: false,
                produtos: []
            };
        }
    }
};
exports.MegaService = MegaService;
exports.MegaService = MegaService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)('MegaSoapClient')),
    __param(1, (0, common_1.Inject)('OracleConnectionFactory')),
    __param(2, (0, typeorm_1.InjectRepository)(invoice_entity_1.Invoice)),
    __param(3, (0, typeorm_1.InjectRepository)(invoice_item_entity_1.InvoiceItem)),
    __param(4, (0, typeorm_1.InjectRepository)(invoice_error_entity_1.InvoiceError)),
    __param(5, (0, typeorm_1.InjectRepository)(product_entity_1.Product)),
    __param(6, (0, typeorm_1.InjectRepository)(romaneio_entity_1.Romaneio)),
    __metadata("design:paramtypes", [Object, Function, typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], MegaService);
//# sourceMappingURL=mega.service.js.map