export interface Endereco {
  xLgr: string;
  nro: string;
  xBairro: string;
  cMun: string;
  xMun: string;
  UF: string;
  CEP: string;
  cPais: string;
  xPais: string;
  fone?: string;
}

export interface Emitente {
  cnpj: string;
  nome: string;
  fantasia?: string;
  endereco: Endereco;
  inscricaoEstadual: string;
  regimeTributario: string;
  email?: string | null;
}

export interface Destinatario {
  cnpj: string;
  nome: string;
  endereco: Endereco;
  inscricaoEstadual: string;
  indicadorIE: string;
  email?: string | null;
}

export interface Produto {
  numeroItem: string;
  codigo: string;
  descricao: string;
  ncm: string;
  cfop: string;
  unidade: string;
  quantidade: string;
  valorUnitario: string;
  valorTotal: string;
  ean?: string;
  volume?: string | null;
}

export interface Total {
  vBC: string;
  vICMS: string;
  vICMSDeson: string;
  vFCPUFDest: string;
  vICMSUFDest: string;
  vICMSUFRemet: string;
  vFCP: string;
  vBCST: string;
  vST: string;
  vFCPST: string;
  vFCPSTRet: string;
  qBCMono: string;
  vICMSMono: string;
  qBCMonoReten: string;
  vICMSMonoReten: string;
  qBCMonoRet: string;
  vICMSMonoRet: string;
  vProd: string;
  vFrete: string;
  vSeg: string;
  vDesc: string;
  vII: string;
  vIPI: string;
  vIPIDevol: string;
  vPIS: string;
  vCOFINS: string;
  vOutro: string;
  vNF: string;
  vTotTrib: string;
}

export interface Transporte {
  modFrete: string;
  vol?: {
    qVol: string;
    pesoL: string;
    pesoB: string;
  };
}

export interface Pagamento {
  detPag: {
    indPag: string;
    tPag: string;
    vPag: string;
  };
}

export interface ObsCont {
  $: { xCampo: string };
  xTexto: string;
}

export interface InformacoesAdicionais {
  infCpl: string;
  obsCont?: ObsCont[];
}

export interface Protocolo {
  $: { Id: string };
  tpAmb: string;
  verAplic: string;
  chNFe: string;
  dhRecbto: string;
  nProt: string;
  digVal: string;
  cStat: string;
  xMotivo: string;
}

export interface NotaFiscal {
  versao: string;
  numeroNota: string;
  dataEmissao: string;
  naturezaOperacao: string;
  modelo: string;
  serie: string;
  emitente: Emitente;
  destinatario: Destinatario;
  produtos: Produto[];
  total: Total;
  transporte: Transporte;
  pagamento: Pagamento;
  informacoesAdicionais: InformacoesAdicionais;
  protocolo: Protocolo;
} 