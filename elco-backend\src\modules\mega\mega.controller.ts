import { Controller, Post, Body, Param, Get } from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBody,
  ApiParam,
} from '@nestjs/swagger';
import { MegaService } from './mega.service';

@ApiTags('Mega/XML')
@Controller('mega')
export class MegaController {
  constructor(private readonly megaService: MegaService) {}

  @Get('consultar-xml/:chaveAcesso')
  @ApiOperation({
    summary: 'Consultar XML por chave de acesso',
    description:
      'Consulta dados de uma nota fiscal no sistema Mega através da chave de acesso.',
  })
  @ApiParam({
    name: 'chaveAcesso',
    description: 'Chave de acesso da nota fiscal (44 dígitos)',
    type: 'string',
    example: '35200714200166000187550010000000007907734918',
  })
  @ApiResponse({
    status: 200,
    description: 'Dados da nota fiscal consultada',
    schema: {
      type: 'object',
      properties: {
        chaveAcesso: {
          type: 'string',
          example: '35200714200166000187550010000000007907734918',
        },
        status: { type: 'string', example: 'AUTORIZADA' },
        numero: { type: 'string', example: '000000007' },
        serie: { type: 'string', example: '001' },
        dataEmissao: { type: 'string', format: 'date-time' },
        valorTotal: { type: 'number', example: 1250.5 },
        fornecedor: {
          type: 'object',
          properties: {
            cnpj: { type: 'string', example: '14.200.166/0001-87' },
            razaoSocial: {
              type: 'string',
              example: 'Empresa Fornecedora Ltda',
            },
          },
        },
      },
    },
  })
  @ApiResponse({ status: 404, description: 'Nota fiscal não encontrada' })
  @ApiResponse({ status: 400, description: 'Chave de acesso inválida' })
  async consultarXml(@Param('chaveAcesso') chaveAcesso: string): Promise<any> {
    const teste = this.megaService.consultarXml(chaveAcesso);
    return teste;
  }

  @Post('nota-fiscal/:idOrdem/:idRomaneio')
  @ApiOperation({
    summary: 'Enviar nota fiscal',
    description:
      'Envia uma nota fiscal para o sistema Mega vinculada a uma ordem e romaneio.',
  })
  @ApiParam({
    name: 'idOrdem',
    description: 'ID da ordem',
    type: 'number',
    example: 123,
  })
  @ApiParam({
    name: 'idRomaneio',
    description: 'ID do romaneio',
    type: 'number',
    example: 456,
  })
  @ApiBody({
    description: 'Dados da nota fiscal para envio',
    schema: {
      type: 'object',
      properties: {
        chaveAcesso: {
          type: 'string',
          example: '35200714200166000187550010000000007907734918',
        },
        xmlContent: {
          type: 'string',
          description: 'Conteúdo XML da nota fiscal',
        },
        observacoes: {
          type: 'string',
          example: 'Nota fiscal de produtos especiais',
        },
      },
      required: ['chaveAcesso', 'xmlContent'],
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Nota fiscal enviada com sucesso',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        protocolo: { type: 'string', example: 'PROT123456789' },
        message: {
          type: 'string',
          example: 'Nota fiscal processada com sucesso',
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Dados inválidos ou erro no processamento',
  })
  async enviarNotaFiscal(
    @Body() dados: any,
    @Param('idOrdem') idOrdem: number,
    @Param('idRomaneio') idRomaneio: number,
  ): Promise<any> {
    return this.megaService.enviarNotaFiscal(dados, idOrdem, idRomaneio);
  }

  @Post('errors/send-xml')
  @ApiOperation({
    summary: 'Reenviar XML com erro',
    description: 'Reenvia um XML que apresentou erro durante o processamento.',
  })
  @ApiBody({
    description: 'Dados para reenvio do XML',
    schema: {
      type: 'object',
      properties: {
        errorId: { type: 'number', example: 789 },
        xmlEnviado: {
          type: 'string',
          description: 'Conteúdo XML corrigido para reenvio',
        },
      },
      required: ['errorId', 'xmlEnviado'],
    },
  })
  @ApiResponse({
    status: 200,
    description: 'XML reenviado com sucesso',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        errorId: { type: 'number', example: 789 },
        message: {
          type: 'string',
          example: 'XML reenviado e processado com sucesso',
        },
      },
    },
  })
  @ApiResponse({ status: 404, description: 'Erro não encontrado' })
  @ApiResponse({ status: 400, description: 'XML inválido' })
  async reenviarXML(@Body() body: { errorId: number; xmlEnviado: string }) {
    return this.megaService.reenviarXML(body.errorId, body.xmlEnviado);
  }

  @Post('consultar-xmls')
  @ApiOperation({
    summary: 'Consultar múltiplos XMLs',
    description:
      'Consulta informações de múltiplas notas fiscais através de suas chaves de acesso.',
  })
  @ApiBody({
    description: 'Lista de chaves de acesso para consulta',
    schema: {
      type: 'object',
      properties: {
        chavesAcesso: {
          type: 'array',
          items: { type: 'string' },
          example: [
            '35200714200166000187550010000000007907734918',
            '35200714200166000187550010000000008907734919',
          ],
          description: 'Array com chaves de acesso das notas fiscais',
        },
      },
      required: ['chavesAcesso'],
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Consulta realizada com sucesso',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        total: { type: 'number', example: 2 },
        results: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              chaveAcesso: {
                type: 'string',
                example: '35200714200166000187550010000000007907734918',
              },
              status: { type: 'string', example: 'ENCONTRADA' },
              dados: { type: 'object', description: 'Dados da nota fiscal' },
            },
          },
        },
        errors: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              chaveAcesso: {
                type: 'string',
                example: '35200714200166000187550010000000008907734919',
              },
              error: { type: 'string', example: 'Nota fiscal não encontrada' },
            },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Lista de chaves inválida ou vazia',
  })
  async consultarXmls(@Body() body: { chavesAcesso: string[] }) {
    return this.megaService.consultarXmls(body.chavesAcesso);
  }

  @Get('produtos-romaneio/:romaneioId')
  @ApiOperation({
    summary: 'Consultar produtos de um romaneio',
    description:
      'Consulta todos os produtos de um romaneio específico para debug.',
  })
  @ApiParam({
    name: 'romaneioId',
    description: 'ID do romaneio',
    type: 'number',
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'Produtos do romaneio',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        produtos: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'number' },
              idRomanio: { type: 'number' },
              idSeparationOrder: { type: 'number' },
              name: { type: 'string' },
              taxNoteNumber: { type: 'string' },
            },
          },
        },
      },
    },
  })
  async consultarProdutosRomaneio(@Param('romaneioId') romaneioId: number) {
    return this.megaService.consultarProdutosRomaneio(romaneioId);
  }
}
