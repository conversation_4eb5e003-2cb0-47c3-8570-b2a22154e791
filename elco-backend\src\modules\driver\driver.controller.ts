import {
  Controller,
  Post,
  Get,
  Put,
  Delete,
  Body,
  Param,
  UploadedFile,
  UseInterceptors,
  NotFoundException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBody,
  ApiParam,
  ApiConsumes,
} from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';
import { DriverService } from './driver.service';
import { CreateDriverDto } from './dto/create-driver.dto';
import { UpdateDriverDto } from './dto/update-driver.dto';

@ApiTags('Motoristas')
@Controller('drivers')
export class DriverController {
  constructor(private readonly driverService: DriverService) {}

  @Get('/list')
  @ApiOperation({
    summary: 'Listar todos os motoristas',
    description:
      'Retorna uma lista com todos os motoristas cadastrados no sistema.',
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de motoristas',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'string', example: 'uuid-string' },
          name: { type: 'string', example: '<PERSON>' },
          document: { type: 'string', example: '123.456.789-00' },
          license: { type: 'string', example: '12345678901' },
          phone: { type: 'string', example: '(11) 99999-9999' },
          isActive: { type: 'boolean', example: true },
        },
      },
    },
  })
  findAll() {
    return this.driverService.findAll();
  }

  @Get('/view/:id')
  @ApiOperation({
    summary: 'Obter motorista por ID',
    description: 'Retorna os detalhes completos de um motorista específico.',
  })
  @ApiParam({ name: 'id', description: 'ID do motorista', type: 'string' })
  @ApiResponse({
    status: 200,
    description: 'Dados do motorista',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string', example: 'uuid-string' },
        name: { type: 'string', example: 'João Silva' },
        document: { type: 'string', example: '123.456.789-00' },
        license: { type: 'string', example: '12345678901' },
        phone: { type: 'string', example: '(11) 99999-9999' },
        licenseDocument: { type: 'string', example: 'path/to/document.pdf' },
        isActive: { type: 'boolean', example: true },
        createdAt: { type: 'string', format: 'date-time' },
      },
    },
  })
  @ApiResponse({ status: 404, description: 'Motorista não encontrado' })
  async findOne(@Param('id') id: string) {
    return this.driverService.findOne(id);
  }

  @Post('/create')
  @ApiOperation({
    summary: 'Criar novo motorista',
    description:
      'Cria um novo motorista no sistema. Pode incluir upload de documento da CNH.',
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Dados do motorista e documento (opcional)',
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string', example: 'João Silva' },
        document: { type: 'string', example: '123.456.789-00' },
        license: { type: 'string', example: '12345678901' },
        phone: { type: 'string', example: '(11) 99999-9999' },
        licenseDocument: {
          type: 'string',
          format: 'binary',
          description: 'Arquivo da CNH (PDF, JPG, PNG)',
        },
      },
      required: ['name', 'document', 'license'],
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Motorista criado com sucesso',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string', example: 'uuid-string' },
        name: { type: 'string', example: 'João Silva' },
        document: { type: 'string', example: '123.456.789-00' },
        license: { type: 'string', example: '12345678901' },
        phone: { type: 'string', example: '(11) 99999-9999' },
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @UseInterceptors(FileInterceptor('licenseDocument'))
  async create(
    @Body() dto: CreateDriverDto,
    @UploadedFile() file: Express.Multer.File,
  ) {
    return this.driverService.create(dto, file);
  }

  @Put('/update/:id')
  @ApiOperation({
    summary: 'Atualizar motorista',
    description: 'Atualiza os dados de um motorista existente.',
  })
  @ApiParam({ name: 'id', description: 'ID do motorista', type: 'string' })
  @ApiBody({ type: UpdateDriverDto })
  @ApiResponse({
    status: 200,
    description: 'Motorista atualizado com sucesso',
  })
  @ApiResponse({ status: 404, description: 'Motorista não encontrado' })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  async update(@Param('id') id: string, @Body() dto: UpdateDriverDto) {
    return this.driverService.update(id, dto);
  }

  @Delete('/delete/:id')
  @ApiOperation({
    summary: 'Deletar motorista',
    description: 'Remove um motorista do sistema.',
  })
  @ApiParam({ name: 'id', description: 'ID do motorista', type: 'string' })
  @ApiResponse({
    status: 200,
    description: 'Motorista excluído com sucesso',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'Motorista excluído com sucesso' },
      },
    },
  })
  @ApiResponse({ status: 404, description: 'Motorista não encontrado' })
  async delete(@Param('id') id: string) {
    const deleted = await this.driverService.delete(id);
    if (!deleted) throw new NotFoundException('Motorista não encontrado');
    return { message: 'Motorista excluído com sucesso' };
  }
}
