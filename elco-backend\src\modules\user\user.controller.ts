import { Controller, Get, Post, Put, Delete, Param, Body, UseGuards, Req, ParseIntPipe } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBody, ApiBearerAuth, ApiParam } from '@nestjs/swagger';
import { UserService } from './user.service';
import { User } from './entity/user.entity';
import { JwtAuthGuard } from 'src/modules/auth/jwt-auth.guard';
import { UpdateUserDto } from './dto/update-user.dto';
import { CreateUserDto } from './dto/create-user.dto';

interface AuthRequest extends Request {
  user: any;
}

@ApiTags('Usuários')
@ApiBearerAuth('JWT-auth')
@Controller('users')
@UseGuards(JwtAuthGuard)
export class UserController {
  constructor(private readonly userService: UserService) { }

  @Get('me')
  @ApiOperation({ 
    summary: 'Obter perfil do usuário logado',
    description: 'Retorna as informações do perfil do usuário atual autenticado.'
  })
  @ApiResponse({
    status: 200,
    description: 'Perfil do usuário',
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string', example: 'João Silva' },
        email: { type: 'string', example: '<EMAIL>' },
        phone: { type: 'string', example: '(11) 99999-9999' },
        role: { type: 'string', example: 'admin' }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Token inválido ou ausente' })
  async getProfile(@Req() req: AuthRequest) {
    return {
      name: req.user.name,
      email: req.user.email,
      phone: req.user.phone,
      role: req.user.role?.name || 'N/A',
    };
  }

  @UseGuards(JwtAuthGuard)
  @Put('me')
  @ApiOperation({ 
    summary: 'Atualizar perfil do usuário logado',
    description: 'Permite ao usuário autenticado atualizar suas próprias informações.'
  })
  @ApiBody({
    type: UpdateUserDto,
    description: 'Dados para atualização do perfil',
    examples: {
      update: {
        summary: 'Exemplo de atualização',
        value: {
          name: 'João Silva Santos',
          phone: '(11) 98888-8888'
        }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: 'Perfil atualizado com sucesso',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'number', example: 1 },
        name: { type: 'string', example: 'João Silva Santos' },
        email: { type: 'string', example: '<EMAIL>' },
        phone: { type: 'string', example: '(11) 98888-8888' }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Token inválido ou ausente' })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  async updateProfile(@Req() req: any, @Body() body: UpdateUserDto) {
    const userId = req.user.id;
    return this.userService.updateProfile(userId, body);
  }

  @Put('edit/:id')
  @ApiOperation({ 
    summary: 'Atualizar usuário por ID',
    description: 'Permite atualizar qualquer usuário por ID (requer permissões administrativas).'
  })
  @ApiParam({ name: 'id', description: 'ID do usuário', type: 'string', example: '1' })
  @ApiBody({ type: UpdateUserDto })
  @ApiResponse({
    status: 200,
    description: 'Usuário atualizado com sucesso'
  })
  @ApiResponse({ status: 404, description: 'Usuário não encontrado' })
  async updateUser(@Param('id') id: string, @Body() body: UpdateUserDto) {
    return this.userService.update(Number(id), body);
  }

  @Get('view/:id')
  @ApiOperation({ 
    summary: 'Obter usuário por ID',
    description: 'Retorna os detalhes completos de um usuário específico.'
  })
  @ApiParam({ name: 'id', description: 'ID do usuário', type: 'string', example: '1' })
  @ApiResponse({
    status: 200,
    description: 'Dados do usuário',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'number', example: 1 },
        name: { type: 'string', example: 'João Silva' },
        email: { type: 'string', example: '<EMAIL>' },
        phone: { type: 'string', example: '(11) 99999-9999' },
        createdAt: { type: 'string', format: 'date-time' },
        role: {
          type: 'object',
          properties: {
            id: { type: 'number', example: 1 },
            name: { type: 'string', example: 'admin' }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 404, description: 'Usuário não encontrado' })
  async getUser(@Param('id') id: string) {
    return this.userService.findOne(Number(id));
  }

  @Get()
  @ApiOperation({ 
    summary: 'Listar todos os usuários',
    description: 'Retorna uma lista com todos os usuários cadastrados no sistema.'
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de usuários',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'number', example: 1 },
          name: { type: 'string', example: 'João Silva' },
          email: { type: 'string', example: '<EMAIL>' },
          phone: { type: 'string', example: '(11) 99999-9999' },
          isActive: { type: 'boolean', example: true },
          role: {
            type: 'object',
            properties: {
              id: { type: 'number', example: 1 },
              name: { type: 'string', example: 'admin' }
            }
          }
        }
      }
    }
  })
  async findAll() {
    return this.userService.findAll();
  }

  @Delete('delete/:id')
  @ApiOperation({ 
    summary: 'Deletar usuário (soft delete)',
    description: 'Desativa um usuário no sistema (soft delete). O usuário não é removido permanentemente.'
  })
  @ApiParam({ name: 'id', description: 'ID do usuário', type: 'number', example: 1 })
  @ApiResponse({
    status: 200,
    description: 'Usuário desativado com sucesso',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'Usuário desativado com sucesso' }
      }
    }
  })
  @ApiResponse({ status: 404, description: 'Usuário não encontrado' })
  async deleteUser(@Param('id', ParseIntPipe) id: number) {
    return this.userService.softDelete(id);
  }

  @Post()
  @ApiOperation({ 
    summary: 'Criar novo usuário',
    description: 'Cria um novo usuário no sistema. Requer permissões administrativas.'
  })
  @ApiBody({
    type: CreateUserDto,
    description: 'Dados do novo usuário',
    examples: {
      create: {
        summary: 'Exemplo de criação',
        value: {
          name: 'Maria Santos',
          email: '<EMAIL>',
          password: 'senha123',
          phone: '(11) 88888-8888',
          roleId: 2
        }
      }
    }
  })
  @ApiResponse({
    status: 201,
    description: 'Usuário criado com sucesso',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'number', example: 2 },
        name: { type: 'string', example: 'Maria Santos' },
        email: { type: 'string', example: '<EMAIL>' },
        phone: { type: 'string', example: '(11) 88888-8888' },
        role: {
          type: 'object',
          properties: {
            id: { type: 'number', example: 2 },
            name: { type: 'string', example: 'user' }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos ou email já existe' })
  async createUser(@Body() body: CreateUserDto) {
    return this.userService.create(body);
  }
}
