import { Repository } from 'typeorm';
import { User } from './entity/user.entity';
import { UpdateUserDto } from './dto/update-user.dto';
import { RolesEntity } from '../role/entity/role.entity';
import { CreateUserDto } from './dto/create-user.dto';
export declare class UserService {
    private userRepo;
    private roleRepo;
    constructor(userRepo: Repository<User>, roleRepo: Repository<RolesEntity>);
    update(id: number, dto: UpdateUserDto): Promise<User>;
    findByEmail(email: string): Promise<User | null>;
    findOne(id: number): Promise<User>;
    findAll(): Promise<User[]>;
    updateProfile(userId: number, data: UpdateUserDto): Promise<User>;
    create(dto: CreateUserDto): Promise<User>;
    softDelete(id: number): Promise<{
        message: string;
    }>;
}
