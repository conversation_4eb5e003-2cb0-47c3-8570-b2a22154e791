import { Module } from '@nestjs/common';
import { OracleService } from './oracle.service';

@Module({
  providers: [
    {
      provide: 'OracleConnectionFactory',
      useFactory: async () => {
        const oracledb = require('oracledb');
        const libDir = process.env.ORACLE_CLIENT_LIB_DIR || 'D:/oracle/instantclient_19_26';
        try {
          oracledb.initOracleClient({ libDir });
        } catch (err) {
        }
        return async () => {
          return await oracledb.getConnection({
            user: process.env.ORACLE_USER,
            password: process.env.ORACLE_PASSWORD,
            connectString: process.env.ORACLE_CONNECTION_STRING,
          });
        };
      },
    },
    OracleService,
  ],
  exports: ['OracleConnectionFactory', OracleService],
})
export class OracleModule {}
