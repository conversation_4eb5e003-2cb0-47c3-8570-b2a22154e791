// src/seed/seed.service.ts
import { Injectable, OnModuleInit } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { RolesEntity } from 'src/modules/role/entity/role.entity';
import { seedRoles } from './role.seeder';
import { UserService } from 'src/modules/user/user.service';
import { CreateUserDto } from 'src/modules/user/dto/create-user.dto';

@Injectable()
export class SeedService implements OnModuleInit {
  constructor(
    @InjectRepository(RolesEntity)
    private readonly roleRepo: Repository<RolesEntity>,
    private readonly userService: UserService, // injetar UserService
  ) {}

  async onModuleInit() {
    await seedRoles(this.roleRepo);
    console.log('Seed de roles executada com sucesso');

    // Seed de usuário admin de teste
    const adminDto: CreateUserDto = {
      name: 'Admin Teste',
      email: '<EMAIL>',
      password: 'admin123',
      phone: '999999999',
      role: 'ADMIN',
    };
    try {
      await this.userService.create(adminDto);
      console.log('Usuário admin de teste criado com sucesso');
    } catch (e) {
      if (e.message && e.message.includes('E-mail já cadastrado')) {
        console.log('Usuário admin de teste já existe');
      } else {
        console.error('Erro ao criar usuário admin de teste:', e);
      }
    }
  }
}
