"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VnfError = void 0;
const typeorm_1 = require("typeorm");
let VnfError = class VnfError {
    id;
    numeroNota;
    clienteExternalCode;
    nomeFornecedor;
    tipoErro;
    compradorEmail;
    fornecedorEmail;
    dataEmailEnviado;
    podeRecusar;
    createdAt;
    deletedAt;
};
exports.VnfError = VnfError;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], VnfError.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'numero_nota', type: 'varchar', length: 50 }),
    __metadata("design:type", String)
], VnfError.prototype, "numeroNota", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'cliente_external_code',
        type: 'varchar',
        length: 100,
        nullable: true,
    }),
    __metadata("design:type", String)
], VnfError.prototype, "clienteExternalCode", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'nome_fornecedor',
        type: 'varchar',
        length: 255,
        nullable: true,
    }),
    __metadata("design:type", String)
], VnfError.prototype, "nomeFornecedor", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'tipo_erro', type: 'varchar', length: 255 }),
    __metadata("design:type", String)
], VnfError.prototype, "tipoErro", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'comprador_email',
        type: 'varchar',
        length: 255,
        nullable: true,
    }),
    __metadata("design:type", String)
], VnfError.prototype, "compradorEmail", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'fornecedor_email',
        type: 'varchar',
        length: 255,
        nullable: true,
    }),
    __metadata("design:type", String)
], VnfError.prototype, "fornecedorEmail", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'data_email_enviado', type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], VnfError.prototype, "dataEmailEnviado", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'pode_recusar', type: 'boolean', default: true }),
    __metadata("design:type", Boolean)
], VnfError.prototype, "podeRecusar", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", Date)
], VnfError.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.DeleteDateColumn)({ name: 'deleted_at' }),
    __metadata("design:type", Date)
], VnfError.prototype, "deletedAt", void 0);
exports.VnfError = VnfError = __decorate([
    (0, typeorm_1.Entity)('vnf_errors')
], VnfError);
//# sourceMappingURL=vnf-error.entity.js.map