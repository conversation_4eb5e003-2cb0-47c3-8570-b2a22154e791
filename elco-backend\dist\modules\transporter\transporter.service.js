"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TransporterService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const transporter_entity_1 = require("./entity/transporter.entity");
let TransporterService = class TransporterService {
    transporterRepository;
    dataSource;
    constructor(transporterRepository, dataSource) {
        this.transporterRepository = transporterRepository;
        this.dataSource = dataSource;
    }
    findAll() {
        return this.transporterRepository.find({ order: { name: 'ASC' } });
    }
    async findOne(id) {
        const transporter = await this.transporterRepository.findOne({
            where: { id },
        });
        if (!transporter)
            throw new common_1.NotFoundException('Transportador não encontrado');
        return transporter;
    }
    create(dto) {
        const transporter = this.transporterRepository.create(dto);
        return this.transporterRepository.save(transporter);
    }
    async update(id, dto) {
        const transporter = await this.findOne(id);
        Object.assign(transporter, dto);
        return this.transporterRepository.save(transporter);
    }
    async delete(id) {
        const vinculatedCarrier = await this.dataSource
            .createQueryBuilder()
            .select('COUNT(rm.id)', 'count')
            .from('romaneio', 'rm')
            .where('rm.carrier_id = :id', { id: id })
            .andWhere('rm.deleted_at IS NULL')
            .execute();
        const romaneiosCount = parseInt(vinculatedCarrier[0]?.count, 10) || 0;
        if (romaneiosCount > 0) {
            throw new common_1.ConflictException(`Transportador vinculado a ${romaneiosCount} romaneio(s)`);
        }
        const result = await this.transporterRepository.delete(id);
        return result.affected > 0;
    }
};
exports.TransporterService = TransporterService;
exports.TransporterService = TransporterService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(transporter_entity_1.Transporter)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource])
], TransporterService);
//# sourceMappingURL=transporter.service.js.map