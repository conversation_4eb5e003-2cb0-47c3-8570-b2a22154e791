"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Invoice = void 0;
const typeorm_1 = require("typeorm");
const invoice_item_entity_1 = require("./invoice-item.entity");
let Invoice = class Invoice {
    id;
    idOrdem;
    idRomaneio;
    filInCodigo;
    notInCodigo;
    notInNumero;
    tpdInCodigo;
    notDtEmissao;
    notHrHoraemissao;
    notDtSaida;
    notHrHorasaida;
    notStUf;
    notStMunicipio;
    notStCgc;
    notStIncrestadual;
    cfopStDescricao;
    notStChaveacesso;
    ccfInReduzido;
    projeto;
    xml;
    status;
    pdfBase64;
    numeroDanfe;
    items;
    createdAt;
    updatedAt;
    deletedAt;
};
exports.Invoice = Invoice;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], Invoice.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'id_ordem' }),
    __metadata("design:type", Number)
], Invoice.prototype, "idOrdem", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'id_romaneio' }),
    __metadata("design:type", Number)
], Invoice.prototype, "idRomaneio", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'fil_in_codigo' }),
    __metadata("design:type", Number)
], Invoice.prototype, "filInCodigo", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'not_in_codigo' }),
    __metadata("design:type", Number)
], Invoice.prototype, "notInCodigo", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'not_in_numero' }),
    __metadata("design:type", String)
], Invoice.prototype, "notInNumero", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'tpd_in_codigo' }),
    __metadata("design:type", Number)
], Invoice.prototype, "tpdInCodigo", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'not_dt_emissao' }),
    __metadata("design:type", Date)
], Invoice.prototype, "notDtEmissao", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'not_hr_horaemissao' }),
    __metadata("design:type", String)
], Invoice.prototype, "notHrHoraemissao", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'not_dt_saida' }),
    __metadata("design:type", Date)
], Invoice.prototype, "notDtSaida", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'not_hr_horasaida' }),
    __metadata("design:type", Date)
], Invoice.prototype, "notHrHorasaida", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'not_st_uf', length: 2 }),
    __metadata("design:type", String)
], Invoice.prototype, "notStUf", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'not_st_municipio' }),
    __metadata("design:type", String)
], Invoice.prototype, "notStMunicipio", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'not_st_cgc' }),
    __metadata("design:type", String)
], Invoice.prototype, "notStCgc", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'not_st_increstadual' }),
    __metadata("design:type", String)
], Invoice.prototype, "notStIncrestadual", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'cfop_st_descricao' }),
    __metadata("design:type", String)
], Invoice.prototype, "cfopStDescricao", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'not_st_chaveacesso' }),
    __metadata("design:type", String)
], Invoice.prototype, "notStChaveacesso", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'ccf_in_reduzido' }),
    __metadata("design:type", Number)
], Invoice.prototype, "ccfInReduzido", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'projeto' }),
    __metadata("design:type", String)
], Invoice.prototype, "projeto", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'xml', type: 'text', nullable: true }),
    __metadata("design:type", String)
], Invoice.prototype, "xml", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'status', type: 'varchar', length: 50, nullable: true }),
    __metadata("design:type", String)
], Invoice.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'pdf_base64', type: 'longtext', nullable: true }),
    __metadata("design:type", String)
], Invoice.prototype, "pdfBase64", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'numero_danfe', type: 'text', nullable: true }),
    __metadata("design:type", String)
], Invoice.prototype, "numeroDanfe", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => invoice_item_entity_1.InvoiceItem, item => item.invoice),
    __metadata("design:type", Array)
], Invoice.prototype, "items", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", Date)
], Invoice.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", Date)
], Invoice.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.DeleteDateColumn)({ name: 'deleted_at' }),
    __metadata("design:type", Date)
], Invoice.prototype, "deletedAt", void 0);
exports.Invoice = Invoice = __decorate([
    (0, typeorm_1.Entity)('invoices')
], Invoice);
//# sourceMappingURL=invoice.entity.js.map