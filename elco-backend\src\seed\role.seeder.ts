// src/seed/role.seeder.ts
import { Repository } from 'typeorm';
import { RolesEntity } from 'src/modules/role/entity/role.entity';

export async function seedRoles(repo: Repository<RolesEntity>) {
  const roles = [
    { key: 'ADMI<PERSON>', name: '<PERSON><PERSON><PERSON><PERSON>' },
    { key: 'FIS<PERSON><PERSON>', name: '<PERSON>sca<PERSON>' },
    { key: 'COORD', name: '<PERSON><PERSON><PERSON><PERSON>' },
    { key: 'OPERADOR_OBRA', name: 'Opera<PERSON> de Obra' },
    { key: 'ESTOQUISTA', name: '<PERSON><PERSON><PERSON><PERSON>' },
  ];

  for (const role of roles) {
    const exists = await repo.findOne({ where: { key: role.key } });
    if (!exists) {
      await repo.save(repo.create(role));
    }
  }
}
