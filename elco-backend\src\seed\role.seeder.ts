import { Repository } from 'typeorm';
import { RolesEntity } from 'src/modules/role/entity/role.entity';

export async function seedRoles(repo: Repository<RolesEntity>) {
  const roles = [
    { key: 'ADMIN', name: '<PERSON>ministrador' },
    { key: '<PERSON><PERSON><PERSON><PERSON>', name: '<PERSON><PERSON><PERSON>' },
    { key: 'COORD', name: '<PERSON>ordena<PERSON>' },
    { key: 'OPERADOR_OBRA', name: 'Operador de Obra' },
    { key: 'ESTOQUISTA', name: '<PERSON>stoquist<PERSON>' },
  ];

  for (const role of roles) {
    const exists = await repo.findOne({ where: { key: role.key } });
    if (!exists) {
      await repo.save(repo.create(role));
    }
  }
}
