import { Injectable, Inject } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SupplierEmail } from './entity/supplier-email.entity';

@Injectable()
export class SupplierEmailService {
  constructor(
    @InjectRepository(SupplierEmail)
    private readonly supplierEmailRepository: Repository<SupplierEmail>,
    @Inject('OracleConnectionFactory')
    private readonly oracleConnectionFactory: () => Promise<any>,
  ) {}

  findAll(): Promise<SupplierEmail[]> {
    return this.supplierEmailRepository.find();
  }

  findOne(id: number): Promise<SupplierEmail | null> {
    return this.supplierEmailRepository.findOneBy({ id });
  }

  create(data: Partial<SupplierEmail>): Promise<SupplierEmail> {
    const entity = this.supplierEmailRepository.create(data);
    return this.supplierEmailRepository.save(entity);
  }

  async update(id: number, data: Partial<SupplierEmail>): Promise<SupplierEmail | null> {
    await this.supplierEmailRepository.update(id, data);
    return this.findOne(id);
  }

  async remove(id: number): Promise<void> {
    await this.supplierEmailRepository.softDelete(id);
  }

  async fetchProjects(search?: string): Promise<{ projectCode: number; projectDescription: string }[]> {
    let query = `SELECT PRO_IN_REDUZIDO, PRO_ST_DESCRICAO FROM ELCO.glo_projetos@ELCO`;
    const binds: any[] = [];
    if (search) {
      query += ` WHERE LOWER(PRO_ST_DESCRICAO) LIKE :search`;
      binds.push(`%${search.toLowerCase()}%`);
    }
    const connection = await this.oracleConnectionFactory();
    try {
      const result = await connection.execute(query, binds);
      const cols = Array.isArray(result.metaData)
        ? result.metaData.map((m: any) => m.name)
        : [];
      const projects = Array.isArray(result.rows)
        ? result.rows.map((row: any[]) => {
            return {
              projectCode: row[cols.indexOf('PRO_IN_REDUZIDO')],
              projectDescription: row[cols.indexOf('PRO_ST_DESCRICAO')],
            };
          })
        : [];
      return projects;
    } finally {
      await connection.close();
    }
  }

  async createSupplierEmail(data: { supplierName: string; email: string }): Promise<SupplierEmail> {
    const entity = this.supplierEmailRepository.create({
      supplierName: data.supplierName,
      email: data.email,
    });
    return this.supplierEmailRepository.save(entity);
  }

  async listSupplierEmails(): Promise<{ id: number; supplierName: string; email: string; createdAt: Date }[]> {
    const emails = await this.supplierEmailRepository.find({
      select: ['id', 'supplierName', 'email', 'createdAt'],
      order: { createdAt: 'DESC' },
    });
    return emails;
  }

  async updateSupplierEmail(id: number, data: { supplierName: string; email: string }): Promise<SupplierEmail | null> {
    await this.supplierEmailRepository.update(id, {
      supplierName: data.supplierName,
      email: data.email,
    });
    return this.findOne(id);
  }

  async removeSupplierEmail(id: number): Promise<void> {
    await this.supplierEmailRepository.softDelete(id);
  }

  async hardDeleteSupplierEmail(id: number): Promise<void> {
    await this.supplierEmailRepository.delete(id);
  }
} 