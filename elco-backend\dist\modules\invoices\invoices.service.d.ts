import { Repository } from 'typeorm';
import { Invoice } from './entities/invoice.entity';
import { InvoiceItem } from './entities/invoice-item.entity';
import { InvoiceError } from './entities/invoice-error.entity';
import { InvoiceListResponseDto, InvoiceErrorListResponseDto } from './dto/invoice-list.dto';
export declare class InvoicesService {
    private readonly invoiceRepository;
    private readonly invoiceItemRepository;
    private readonly invoiceErrorRepository;
    constructor(invoiceRepository: Repository<Invoice>, invoiceItemRepository: Repository<InvoiceItem>, invoiceErrorRepository: Repository<InvoiceError>);
    findAll(page?: number, limit?: number): Promise<InvoiceListResponseDto>;
    listInvoices(): Promise<InvoiceListResponseDto>;
    listInvoiceErrors(): Promise<InvoiceErrorListResponseDto>;
}
