"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InvoiceError = void 0;
const typeorm_1 = require("typeorm");
let InvoiceError = class InvoiceError {
    id;
    idRomaneio;
    idOrdem;
    errorMessage;
    xmlEnviado;
    soapEnvelope;
    data;
    createdAt;
    updatedAt;
    deletedAt;
};
exports.InvoiceError = InvoiceError;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], InvoiceError.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'id_romaneio' }),
    __metadata("design:type", Number)
], InvoiceError.prototype, "idRomaneio", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'id_ordem' }),
    __metadata("design:type", Number)
], InvoiceError.prototype, "idOrdem", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'error_message', type: 'text' }),
    __metadata("design:type", String)
], InvoiceError.prototype, "errorMessage", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'xml_enviado', type: 'text', nullable: true }),
    __metadata("design:type", String)
], InvoiceError.prototype, "xmlEnviado", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'soap_envelope', type: 'text', nullable: true }),
    __metadata("design:type", String)
], InvoiceError.prototype, "soapEnvelope", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'data', type: 'datetime', nullable: true, default: () => 'CURRENT_TIMESTAMP' }),
    __metadata("design:type", Date)
], InvoiceError.prototype, "data", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", Date)
], InvoiceError.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", Date)
], InvoiceError.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.DeleteDateColumn)({ name: 'deleted_at' }),
    __metadata("design:type", Date)
], InvoiceError.prototype, "deletedAt", void 0);
exports.InvoiceError = InvoiceError = __decorate([
    (0, typeorm_1.Entity)('invoice_errors')
], InvoiceError);
//# sourceMappingURL=invoice-error.entity.js.map