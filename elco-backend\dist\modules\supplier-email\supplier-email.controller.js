"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SupplierEmailController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const supplier_email_service_1 = require("./supplier-email.service");
let SupplierEmailController = class SupplierEmailController {
    supplierEmailService;
    constructor(supplierEmailService) {
        this.supplierEmailService = supplierEmailService;
    }
    findAll() {
        return this.supplierEmailService.findAll();
    }
    create(data) {
        return this.supplierEmailService.create(data);
    }
    async createSupplierEmail(body) {
        return await this.supplierEmailService.createSupplierEmail(body);
    }
    async updateSupplierEmail(id, body) {
        return await this.supplierEmailService.updateSupplierEmail(Number(id), body);
    }
    async removeSupplierEmail(id) {
        await this.supplierEmailService.removeSupplierEmail(Number(id));
        return { success: true };
    }
    async hardDeleteSupplierEmail(id) {
        await this.supplierEmailService.hardDeleteSupplierEmail(Number(id));
        return { success: true };
    }
    async fetchProjects(search) {
        return await this.supplierEmailService.fetchProjects(search);
    }
    async listSupplierEmails() {
        return await this.supplierEmailService.listSupplierEmails();
    }
    findOne(id) {
        return this.supplierEmailService.findOne(Number(id));
    }
};
exports.SupplierEmailController = SupplierEmailController;
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Listar todos os emails de fornecedores',
        description: 'Retorna uma lista com todos os emails de fornecedores cadastrados.',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de emails de fornecedores',
        schema: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    id: { type: 'number', example: 1 },
                    supplierCode: { type: 'number', example: 12345 },
                    supplierName: { type: 'string', example: 'Fornecedor ABC Ltda' },
                    email: { type: 'string', example: '<EMAIL>' },
                    isActive: { type: 'boolean', example: true },
                    createdAt: { type: 'string', format: 'date-time' },
                },
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], SupplierEmailController.prototype, "findAll", null);
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Criar email de fornecedor (método genérico)',
        description: 'Cria um novo registro de email de fornecedor usando dados parciais.',
    }),
    (0, swagger_1.ApiBody)({
        description: 'Dados parciais do fornecedor',
        schema: {
            type: 'object',
            properties: {
                supplierCode: { type: 'number', example: 12345 },
                supplierName: { type: 'string', example: 'Fornecedor XYZ' },
                email: { type: 'string', example: '<EMAIL>' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Email de fornecedor criado com sucesso',
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], SupplierEmailController.prototype, "create", null);
__decorate([
    (0, common_1.Post)('create-supplier-email'),
    (0, swagger_1.ApiOperation)({
        summary: 'Criar email de fornecedor',
        description: 'Cria um novo email de fornecedor no sistema.',
    }),
    (0, swagger_1.ApiBody)({
        description: 'Dados do fornecedor',
        schema: {
            type: 'object',
            properties: {
                supplierCode: { type: 'number', example: 12345 },
                supplierName: { type: 'string', example: 'Fornecedor ABC Ltda' },
                email: { type: 'string', example: '<EMAIL>' },
            },
            required: ['supplierCode', 'supplierName', 'email'],
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Email de fornecedor criado com sucesso',
        schema: {
            type: 'object',
            properties: {
                id: { type: 'number', example: 1 },
                supplierCode: { type: 'number', example: 12345 },
                supplierName: { type: 'string', example: 'Fornecedor ABC Ltda' },
                email: { type: 'string', example: '<EMAIL>' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dados inválidos' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], SupplierEmailController.prototype, "createSupplierEmail", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Atualizar email de fornecedor',
        description: 'Atualiza os dados de um email de fornecedor existente.',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID do email do fornecedor',
        type: 'string',
        example: '1',
    }),
    (0, swagger_1.ApiBody)({
        description: 'Novos dados do fornecedor',
        schema: {
            type: 'object',
            properties: {
                supplierCode: { type: 'number', example: 12345 },
                supplierName: { type: 'string', example: 'Fornecedor ABC Ltda' },
                email: { type: 'string', example: '<EMAIL>' },
            },
            required: ['supplierCode', 'supplierName', 'email'],
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Email de fornecedor atualizado com sucesso',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Email de fornecedor não encontrado',
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], SupplierEmailController.prototype, "updateSupplierEmail", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Deletar email de fornecedor (soft delete)',
        description: 'Desativa um email de fornecedor (soft delete).',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID do email do fornecedor',
        type: 'string',
        example: '1',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Email de fornecedor desativado com sucesso',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Email de fornecedor não encontrado',
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SupplierEmailController.prototype, "removeSupplierEmail", null);
__decorate([
    (0, common_1.Delete)('hard/:id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Deletar email de fornecedor permanentemente',
        description: 'Remove permanentemente um email de fornecedor do sistema.',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID do email do fornecedor',
        type: 'string',
        example: '1',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Email de fornecedor removido permanentemente',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Email de fornecedor não encontrado',
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SupplierEmailController.prototype, "hardDeleteSupplierEmail", null);
__decorate([
    (0, common_1.Get)('projects'),
    (0, swagger_1.ApiOperation)({
        summary: 'Buscar projetos',
        description: 'Busca projetos disponíveis, com opção de filtro por texto.',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'search',
        required: false,
        description: 'Termo de busca para filtrar projetos',
        example: 'projeto',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de projetos encontrados',
        schema: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    id: { type: 'string', example: 'proj-001' },
                    name: { type: 'string', example: 'Projeto Alpha' },
                    description: { type: 'string', example: 'Descrição do projeto' },
                },
            },
        },
    }),
    __param(0, (0, common_1.Query)('search')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SupplierEmailController.prototype, "fetchProjects", null);
__decorate([
    (0, common_1.Get)('list'),
    (0, swagger_1.ApiOperation)({
        summary: 'Listar emails de fornecedores (método alternativo)',
        description: 'Retorna uma lista formatada dos emails de fornecedores.',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista formatada de emails de fornecedores',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], SupplierEmailController.prototype, "listSupplierEmails", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Obter email de fornecedor por ID',
        description: 'Retorna os detalhes de um email de fornecedor específico.',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID do email do fornecedor',
        type: 'string',
        example: '1',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Dados do email do fornecedor',
        schema: {
            type: 'object',
            properties: {
                id: { type: 'number', example: 1 },
                supplierCode: { type: 'number', example: 12345 },
                supplierName: { type: 'string', example: 'Fornecedor ABC Ltda' },
                email: { type: 'string', example: '<EMAIL>' },
                isActive: { type: 'boolean', example: true },
                createdAt: { type: 'string', format: 'date-time' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Email de fornecedor não encontrado',
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SupplierEmailController.prototype, "findOne", null);
exports.SupplierEmailController = SupplierEmailController = __decorate([
    (0, swagger_1.ApiTags)('Fornecedores'),
    (0, common_1.Controller)('supplier-emails'),
    __metadata("design:paramtypes", [supplier_email_service_1.SupplierEmailService])
], SupplierEmailController);
//# sourceMappingURL=supplier-email.controller.js.map