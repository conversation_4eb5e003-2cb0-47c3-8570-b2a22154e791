import { Repository } from 'typeorm';
import { Invoice } from '../invoices/entities/invoice.entity';
import { InvoiceItem } from '../invoices/entities/invoice-item.entity';
import { InvoiceError } from '../invoices/entities/invoice-error.entity';
import { Product } from '../products/entities/product.entity';
import { Romaneio } from '../romaneios/entities/romaneio.entity';
export interface ConsultaXmlResult {
    success: boolean;
    status?: string;
    message?: string;
    xml?: string | null;
    pdfBase64?: string | null;
}
export declare class MegaService {
    private readonly soapClient;
    private readonly oracleConnectionFactory;
    private readonly invoiceRepository;
    private readonly invoiceItemRepository;
    private readonly invoiceErrorRepository;
    private readonly productRepository;
    private readonly romaneioRepository;
    constructor(soapClient: any, oracleConnectionFactory: () => Promise<any>, invoiceRepository: Repository<Invoice>, invoiceItemRepository: Repository<InvoiceItem>, invoiceErrorRepository: Repository<InvoiceError>, productRepository: Repository<Product>, romaneioRepository: Repository<Romaneio>);
    private xmlParaDanfeBase64;
    private gerarComMeuDanfe;
    private extrairPKMega;
    private consultarNotaOracle;
    consultarXml(chaveAcesso: string): Promise<ConsultaXmlResult>;
    enviarNotaFiscal(dados: any, idOrdem: number, idRomaneio: number): Promise<any>;
    reenviarXML(errorId: number, xmlEnviado: string): Promise<any>;
    consultarXmls(chavesAcesso: string[]): Promise<any>;
    salvarPdfBase64PorChaveAcesso(chaveAcesso: string, pdfBase64: string): Promise<{
        success: boolean;
        message: string;
    }>;
    private updateRomaneioStatusWithRetry;
    consultarProdutosRomaneio(romaneioId: number): Promise<{
        success: boolean;
        produtos: any[];
    }>;
}
