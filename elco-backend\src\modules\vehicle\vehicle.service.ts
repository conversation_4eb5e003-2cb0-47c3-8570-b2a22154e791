import { ConflictException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import { Vehicle } from './entity/vehicle.entity';
import { CreateVehicleDto } from './dto/create-vehicle.dto';
import { UpdateVehicleDto } from './dto/update-vehicle.dto';

@Injectable()
export class VehicleService {
  constructor(
    @InjectRepository(Vehicle)
    private vehicleRepository: Repository<Vehicle>,
    private readonly dataSource: DataSource,
  ) {}

  findAll() {
    return this.vehicleRepository.find({ order: { plate: 'ASC' } });
  }

  async findOne(id: number) {
    const vehicle = await this.vehicleRepository.findOne({ where: { id } });
    if (!vehicle) throw new NotFoundException('Veículo não encontrado');
    return vehicle;
  }

  async create(dto: CreateVehicleDto, file?: Express.Multer.File) {
    const vehicle = this.vehicleRepository.create({
      ...dto,
      vehicleDocument: file?.buffer,
    });
  
    return this.vehicleRepository.save(vehicle);
  }

  async update(id: number, dto: UpdateVehicleDto, file?: Express.Multer.File) {
    
    const vehicle = await this.findOne(id);
    
    if (dto.plate) {
      vehicle.plate = dto.plate;
    }
    if (dto.model) {
      vehicle.model = dto.model;  
    }
    if (dto.brand) {
      vehicle.brand = dto.brand;
    }
    
    if (file) {
      vehicle.vehicleDocument = file.buffer;
    }
    
    const result = await this.vehicleRepository.save(vehicle);
    
    return result;
  }

  async delete(id: number) {
    const vinculatedVehicle = await this.dataSource
      .createQueryBuilder()
      .select('COUNT(rm.id)', 'count')
      .from('romaneio', 'rm')
      .where('rm.vehicle_id = :id', { id: id })
      .andWhere('rm.deleted_at IS NULL')
      .execute();

    const romaneiosCount = parseInt(vinculatedVehicle[0]?.count, 10) || 0;

    if (romaneiosCount > 0) {
      throw new ConflictException(`Veículo vinculado a ${romaneiosCount} romaneio(s)`);
    }
    const result: any = await this.vehicleRepository.delete(id);
    return result.affected > 0;
  }
}
