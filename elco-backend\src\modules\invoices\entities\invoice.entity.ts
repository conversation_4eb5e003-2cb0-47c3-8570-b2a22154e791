import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedC<PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  OneToMany,
} from 'typeorm';
import { InvoiceItem } from './invoice-item.entity';

@Entity('invoices')
export class Invoice {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'id_ordem' })
  idOrdem: number;

  @Column({ name: 'id_romaneio' })
  idRomaneio: number;

  @Column({ name: 'fil_in_codigo' })
  filInCodigo: number;

  @Column({ name: 'not_in_codigo' })
  notInCodigo: number;

  @Column({ name: 'not_in_numero' })
  notInNumero: string;

  @Column({ name: 'tpd_in_codigo' })
  tpdInCodigo: number;

  @Column({ name: 'not_dt_emissao' })
  notDtEmissao: Date;

  @Column({ name: 'not_hr_horaemissao' })
  notHrHoraemissao: string;

  @Column({ name: 'not_dt_saida' })
  notDtSaida: Date;

  @Column({ name: 'not_hr_horasaida' })
  notHrHorasaida: Date;

  @Column({ name: 'not_st_uf', length: 2 })
  notStUf: string;

  @Column({ name: 'not_st_municipio' })
  notStMunicipio: string;

  @Column({ name: 'not_st_cgc' })
  notStCgc: string;

  @Column({ name: 'not_st_increstadual' })
  notStIncrestadual: string;

  @Column({ name: 'cfop_st_descricao' })
  cfopStDescricao: string;

  @Column({ name: 'not_st_chaveacesso' })
  notStChaveacesso: string;

  @Column({ name: 'ccf_in_reduzido' })
  ccfInReduzido: number;

  @Column({ name: 'projeto' })
  projeto: string;

  @Column({ name: 'xml', type: 'text', nullable: true })
  xml?: string;

  @Column({ name: 'status', type: 'varchar', length: 50, nullable: true })
  status?: string;

  @Column({ name: 'pdf_base64', type: 'longtext', nullable: true })
  pdfBase64?: string;

  @Column({ name: 'numero_danfe', type: 'text', nullable: true })
  numeroDanfe?: string;

  @OneToMany(() => InvoiceItem, (item) => item.invoice)
  items: InvoiceItem[];

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @DeleteDateColumn({ name: 'deleted_at' })
  deletedAt: Date;
}
