import { ConflictException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import { Transporter } from './entity/transporter.entity';
import { CreateTransporterDto } from './dto/create-transporter.dto';
import { UpdateTransporterDto } from './dto/update-transporter.dto';

@Injectable()
export class TransporterService {
  constructor(
    @InjectRepository(Transporter)
    private transporterRepository: Repository<Transporter>,
    private readonly dataSource: DataSource,
  ) {}

  findAll() {
    return this.transporterRepository.find({ order: { name: 'ASC' } });
  }

  async findOne(id: string) {
    const transporter = await this.transporterRepository.findOne({ where: { id } });
    if (!transporter) throw new NotFoundException('Transportador não encontrado');
    return transporter;
  }

  create(dto: CreateTransporterDto) {
    const transporter = this.transporterRepository.create(dto);
    return this.transporterRepository.save(transporter);
  }

  async update(id: string, dto: UpdateTransporterDto) {
    const transporter = await this.findOne(id);
    Object.assign(transporter, dto);
    return this.transporterRepository.save(transporter);
  }

  async delete(id: string) {
    const vinculatedCarrier = await this.dataSource
      .createQueryBuilder()
      .select('COUNT(rm.id)', 'count')
      .from('romaneio', 'rm')
      .where('rm.carrier_id = :id', { id: id })
      .andWhere('rm.deleted_at IS NULL')
      .execute();

    const romaneiosCount = parseInt(vinculatedCarrier[0]?.count, 10) || 0;

    if (romaneiosCount > 0) {
      throw new ConflictException(`Transportador vinculado a ${romaneiosCount} romaneio(s)`);
    }
    
    const result: any = await this.transporterRepository.delete(id);
    return result.affected > 0;
  }
}
