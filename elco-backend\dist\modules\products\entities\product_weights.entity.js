"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductWeights = void 0;
const typeorm_1 = require("typeorm");
let ProductWeights = class ProductWeights {
    id;
    productCode;
    productName;
    weight;
    unit;
    createdAt;
    updatedAt;
    deletedAt;
};
exports.ProductWeights = ProductWeights;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", String)
], ProductWeights.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'product_code', type: 'varchar', length: 100 }),
    __metadata("design:type", String)
], ProductWeights.prototype, "productCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'product_name', type: 'varchar', length: 255 }),
    __metadata("design:type", String)
], ProductWeights.prototype, "productName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'weight', type: 'float' }),
    __metadata("design:type", Number)
], ProductWeights.prototype, "weight", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'unit', type: 'varchar', length: 50 }),
    __metadata("design:type", String)
], ProductWeights.prototype, "unit", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", Date)
], ProductWeights.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", Date)
], ProductWeights.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.DeleteDateColumn)({ name: 'deleted_at' }),
    __metadata("design:type", Date)
], ProductWeights.prototype, "deletedAt", void 0);
exports.ProductWeights = ProductWeights = __decorate([
    (0, typeorm_1.Entity)('product_weights')
], ProductWeights);
//# sourceMappingURL=product_weights.entity.js.map