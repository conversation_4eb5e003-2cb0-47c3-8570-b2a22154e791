"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VnfConsultaProcessada = void 0;
const typeorm_1 = require("typeorm");
let VnfConsultaProcessada = class VnfConsultaProcessada {
    id;
    chaveAcesso;
    numeroNota;
    nomeFornecedor;
    numeroPedido;
    statusConsulta;
    temDivergencias;
    observacoes;
    dataProcessamento;
    dataDocumento;
};
exports.VnfConsultaProcessada = VnfConsultaProcessada;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], VnfConsultaProcessada.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'chave_acesso', type: 'varchar', length: 44, unique: true }),
    (0, typeorm_1.Index)('idx_chave_acesso'),
    __metadata("design:type", String)
], VnfConsultaProcessada.prototype, "chaveAcesso", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'numero_nota', type: 'varchar', length: 50, nullable: true }),
    __metadata("design:type", String)
], VnfConsultaProcessada.prototype, "numeroNota", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'nome_fornecedor',
        type: 'varchar',
        length: 255,
        nullable: true,
    }),
    __metadata("design:type", String)
], VnfConsultaProcessada.prototype, "nomeFornecedor", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'numero_pedido',
        type: 'varchar',
        length: 50,
        nullable: true,
    }),
    __metadata("design:type", String)
], VnfConsultaProcessada.prototype, "numeroPedido", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'status_consulta',
        type: 'enum',
        enum: ['sucesso', 'erro', 'limite_atingido', 'erro_sefaz'],
    }),
    __metadata("design:type", String)
], VnfConsultaProcessada.prototype, "statusConsulta", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'tem_divergencias', type: 'boolean', default: false }),
    __metadata("design:type", Boolean)
], VnfConsultaProcessada.prototype, "temDivergencias", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'observacoes', type: 'text', nullable: true }),
    __metadata("design:type", String)
], VnfConsultaProcessada.prototype, "observacoes", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'data_processamento' }),
    __metadata("design:type", Date)
], VnfConsultaProcessada.prototype, "dataProcessamento", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'data_documento', type: 'date', nullable: true }),
    __metadata("design:type", Date)
], VnfConsultaProcessada.prototype, "dataDocumento", void 0);
exports.VnfConsultaProcessada = VnfConsultaProcessada = __decorate([
    (0, typeorm_1.Entity)('vnf_consultas_processadas')
], VnfConsultaProcessada);
//# sourceMappingURL=vnf-consulta-processada.entity.js.map