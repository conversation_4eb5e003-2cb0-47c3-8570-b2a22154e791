"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MegaModule = void 0;
const common_1 = require("@nestjs/common");
const mega_service_1 = require("./mega.service");
const mega_controller_1 = require("./mega.controller");
const oracle_module_1 = require("../oracle/oracle.module");
const invoices_module_1 = require("../invoices/invoices.module");
const products_module_1 = require("../products/products.module");
const typeorm_1 = require("@nestjs/typeorm");
const product_entity_1 = require("../products/entities/product.entity");
const romaneio_entity_1 = require("../romaneios/entities/romaneio.entity");
const MegaSoapClientProvider = {
    provide: 'MegaSoapClient',
    useValue: {
        EnviarNotaFiscalAsync: async (args) => {
            return [{ mensagem: 'Mock de resposta do MegaSoapClient', args }];
        },
    },
};
let MegaModule = class MegaModule {
};
exports.MegaModule = MegaModule;
exports.MegaModule = MegaModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([product_entity_1.Product, romaneio_entity_1.Romaneio]),
            oracle_module_1.OracleModule,
            invoices_module_1.InvoicesModule,
            (0, common_1.forwardRef)(() => products_module_1.ProductsModule),
        ],
        controllers: [mega_controller_1.MegaController],
        providers: [mega_service_1.MegaService, MegaSoapClientProvider],
        exports: [mega_service_1.MegaService],
    })
], MegaModule);
//# sourceMappingURL=mega.module.js.map