import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  DeleteDateColumn,
} from 'typeorm';

@Entity('vnf_errors')
export class VnfError {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'numero_nota', type: 'varchar', length: 50 })
  numeroNota: string;

  @Column({
    name: 'cliente_external_code',
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  clienteExternalCode: string;

  @Column({
    name: 'nome_fornecedor',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  nomeFornecedor: string;

  @Column({ name: 'tipo_erro', type: 'varchar', length: 255 })
  tipoErro: string;

  @Column({
    name: 'comprador_email',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  compradorEmail: string;

  @Column({
    name: 'fornecedor_email',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  fornecedorEmail: string;

  @Column({ name: 'data_email_enviado', type: 'timestamp', nullable: true })
  dataEmailEnviado: Date;

  @Column({ name: 'pode_recusar', type: 'boolean', default: true })
  podeRecusar: boolean;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @DeleteDateColumn({ name: 'deleted_at' })
  deletedAt: Date;
}
