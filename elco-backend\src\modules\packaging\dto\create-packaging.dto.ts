import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ber, Min, Length } from 'class-validator';

export class CreatePackagingDto {
  @IsString()
  @Length(2)
  name: string;

  @IsNumber()
  @Min(0.1)
  weight: number;

  @IsNumber()
  @Min(0.1)
  height: number;

  @IsNumber()
  @Min(0.1)
  width: number;

  @IsNumber()
  @Min(0.1)
  length: number;

  @IsString()
  @Length(2)
  type: string;

  @IsNumber()
  @Min(1)
  volume: number;
}
