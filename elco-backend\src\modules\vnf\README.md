# Módulo VNF - Validação de Notas Fiscais

Este módulo implementa a validação e comparação entre pedidos do portal de compras e notas fiscais eletrônicas.

## Funcionalidades

### 1. Consulta de XML por Chave de Acesso
- Consulta XML de notas fiscais através da chave de acesso
- Extração automática do número do pedido das observações da NF
- Consulta automática do pedido no portal de compras
- Comparação automática entre pedido e nota fiscal

### 2. Comparação entre Pedido e Nota Fiscal
O sistema realiza uma validação completa comparando os seguintes campos:

#### Validações de Cabeçalho:
- **Número do pedido**: Deve estar presente nas observações da NF
- **CNPJ do fornecedor**: Deve coincidir com o emitente da NF
- **CNPJ da planta**: Deve coincidir com o destinatário da NF
- **Nome do fornecedor**: Comparação ignorando maiúsculas, minúsculas e acentos
- **Condição de pagamento**: Deve estar presente no texto "Condicao Pgto:" da NF

#### Validações de Produtos:
- **Sequencial**: Deve corresponder entre pedido e NF
- **Unidade de medida**: Deve ser idêntica
- **NCM**: Deve ser idêntico
- **Valor unitário**: Admite diferença de até R$ 0,01
- **Quantidade**: NF não pode ultrapassar quantidade do pedido

#### Validações Finais:
- **Limite de itens**: NF não pode ter mais de 30 itens
- **Prazo limite de emissão**: NF deve ser emitida até o dia 25 do mês
- **Desconto**: Deve estar zerado
- **Valor total**: Deve igualar o valor total do pedido com frete (margem de R$ 0,01)

## Estrutura de Dados

### Interfaces

#### PedidoPortal
```typescript
interface PedidoPortal {
  pedidoERP: string;
  cnpjFornecedor: string;
  cnpjPlanta: string;
  nomeFornecedor: string;
  condicaoPagamento: string;
  valorTotalComFrete: number;
  itens: ItemPedido[];
  // ... outros campos
}
```

#### NotaFiscal
```typescript
interface NotaFiscal {
  numeroNota: string;
  emitente: Emitente;
  destinatario: Destinatario;
  produtos: Produto[];
  total: Total;
  informacoesAdicionais: InformacoesAdicionais;
  // ... outros campos
}
```

## Endpoints

### POST /vnf/consultar-por-chave
Consulta XML por chave de acesso e realiza comparação automática.

**Body:**
```json
{
  "chaveAcesso": "41250735629600000106550010000091991140041600"
}
```

### POST /vnf/comparar-pedido-nota
Compara manualmente um pedido com uma nota fiscal.

**Body:**
```json
{
  "pedido": {
    "pedidoERP": "40514",
    "cnpjFornecedor": "16891049000111",
    "cnpjPlanta": "77521375000121",
    "nomeFornecedor": "8.8 PARAFUSOS E TINTAS",
    "condicaoPagamento": "07D",
    "valorTotalComFrete": 10.00,
    "itens": [
      {
        "sequencial": 1,
        "codigoUnidadeMedida": "PC",
        "nmc": "0000.00.00",
        "valorUnitario": 1.00,
        "quantidade": 10.00
      }
    ]
  },
  "notaFiscal": {
    "numeroNota": "9199",
    "emitente": {
      "cnpj": "35629600000106",
      "nome": "PERFISAELETRO INDUSTRIA DE ELETROCALHAS LTDA"
    },
    "destinatario": {
      "cnpj": "77521375000121",
      "nome": "ELCO ENGENHARIA DE MONTAGENS LTDA"
    },
    "produtos": [
      {
        "numeroItem": "1",
        "unidade": "PC",
        "ncm": "73089010",
        "valorUnitario": "6.9700000000",
        "quantidade": "50.0000"
      }
    ],
    "total": {
      "vDesc": "0.00",
      "vNF": "1912.50"
    },
    "informacoesAdicionais": {
      "infCpl": "PEDIDO N. 56524 - ENTREGA ELCO CIC.",
      "obsCont": [
        { "$": { "xCampo": "Condicao Pgto:" }, "xTexto": "028-28 DIAS" }
      ]
    }
  },
  "numeroNota": "9199"
}
```

### GET /vnf/erros
Lista todos os erros de validação salvos no banco de dados.

## Tratamento de Erros

Cada divergência encontrada gera uma mensagem padronizada iniciando com "NF Emitida Incoerente!" e é salva na tabela `vnf_errors` com os seguintes campos:

- `numero_nota`: Número da nota fiscal
- `cliente_external_code`: Identificador do cliente (ex: número do pedido)
- `tipo_erro`: Texto completo da divergência
- `comprador_email`: Email do comprador
- `fornecedor_email`: Email do fornecedor (quando disponível)
- `data_email_enviado`: Data de envio do email (quando implementado)
- `created_at`: Data de criação do registro

## Exemplos de Mensagens de Erro

- "NF Emitida Incoerente! Número do pedido não encontrado ou divergente. Esperado: 40514, Encontrado: 56524"
- "NF Emitida Incoerente! CNPJ do fornecedor divergente. Pedido: 16891049000111, NF: 35629600000106"
- "NF Emitida Incoerente! Nome do fornecedor divergente. Pedido: FORNECEDOR A, NF: FORNECEDOR B"
- "NF Emitida Incoerente! Condição de pagamento divergente. Pedido: 07D, NF: 028"
- "NF Emitida Incoerente! Nota fiscal possui mais de 30 itens (31)"
- "NF Emitida Incoerente! Nota fiscal emitida após o prazo limite. Emitida no dia 26 de janeiro de 2024 (limite: dia 25)"
- "NF Emitida Incoerente! Valor unitário do item 1 divergente. Pedido: 10, NF: 6.97"
- "NF Emitida Incoerente! Quantidade do item 1 excede o pedido. Pedido: 10, NF: 50"
- "NF Emitida Incoerente! Nota fiscal possui desconto (100)"
- "NF Emitida Incoerente! Valor total da nota fiscal divergente. Pedido: 1000, NF: 1912.5"

## Configuração

O módulo utiliza as seguintes variáveis de ambiente:

- `PORTAL_COMPRAS_USUARIO`: Usuário para acesso ao portal de compras
- `PORTAL_COMPRAS_SENHA`: Senha para acesso ao portal de compras

## Testes

Os testes unitários cobrem todos os cenários de validação e estão localizados em `__tests__/vnf.service.spec.ts`.

Para executar os testes:
```bash
npm run test vnf.service.spec.ts
``` 