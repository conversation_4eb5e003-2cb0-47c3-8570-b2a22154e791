import { CreateRomaneioOrderDto } from 'src/modules/romaneio-orders/dto/create-romaneio-order.dto';
export declare class CreateProductPackagingDto {
    readonly orderCode: string;
    readonly productCode: string;
    readonly productName: string;
    readonly productQuantity: number;
    readonly productType: string;
    readonly packagingId: number;
    readonly secondaryVolume: number;
    readonly totalSecondaryVolumes: number;
    readonly productWeight: number;
}
export declare class CreateRomaneioDto {
    readonly dateIssue: Date;
    readonly address: string;
    readonly carrierId: number;
    readonly driverId: number;
    readonly vehicleId: number;
    readonly totalLength: number;
    readonly totalWidth: number;
    readonly totalHeight: number;
    readonly totalWeight: number;
    readonly totalVolume: number;
    readonly mainPackagingId: number;
    readonly secondaryPackagingId?: number;
    readonly userId: number;
    readonly linking: string;
    readonly observations: string;
    readonly romaneioOrders: CreateRomaneioOrderDto[];
    readonly productPackagings: CreateProductPackagingDto[];
}
