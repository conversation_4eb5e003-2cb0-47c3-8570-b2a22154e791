import { ConflictException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from './entity/user.entity';
import { UpdateUserDto } from './dto/update-user.dto';
import { RolesEntity } from '../role/entity/role.entity';
import { CreateUserDto } from './dto/create-user.dto';
import * as bcrypt from 'bcrypt';

@Injectable()
export class UserService {
  constructor(
    @InjectRepository(User)
    private userRepo: Repository<User>,
    @InjectRepository(RolesEntity)
    private roleRepo: Repository<RolesEntity>,
  ) { }

  async update(id: number, dto: UpdateUserDto) {
    const user = await this.userRepo.findOne({
      where: { id },
      relations: ['role'],
    });

    if (!user) {
      throw new NotFoundException('<PERSON>u<PERSON><PERSON> não encontrado');
    }

    if (dto.name !== undefined) user.name = dto.name;
    if (dto.phone !== undefined) user.phone = dto.phone;
    if (dto.email !== undefined) user.email = dto.email;

    if (dto.role) {
      const role = await this.roleRepo.findOneBy({ key: dto.role });
      if (!role) {
        throw new NotFoundException(`Tipo de perfil '${dto.role}' não encontrado`);
      }
      user.role_id = role.id;
      user.role = role;
    }

    return this.userRepo.save(user);
  }

  async findByEmail(email: string) {
    return this.userRepo.findOne({
      where: { email },
      withDeleted: false
    });
  }

  async findOne(id: number) {
    const user = await this.userRepo.findOne({
      where: { id },
      withDeleted: false 
    });
    if (!user) throw new NotFoundException('Usuário não encontrado');
    return user;
  }

  findAll() {
    return this.userRepo.find({
      withDeleted: false, 
      relations: ['role']
    });
  }

  async updateProfile(userId: number, data: UpdateUserDto) {
    const user = await this.userRepo.findOne({
      where: { id: userId },
      withDeleted: false 
    });

    if (!user) {
      throw new NotFoundException('Usuário não encontrado');
    }

    user.name = data.name ?? user.name;
    user.email = data.email ?? user.email;
    user.phone = data.phone ?? user.phone;

    return await this.userRepo.save(user);
  }

  async create(dto: CreateUserDto) {  
    const existingUser = await this.userRepo.findOne({
      where: { email: dto.email },
      withDeleted: true 
    });
    
    if (existingUser) {
      if (existingUser.deleted_at) {
        await this.userRepo.restore(existingUser.id);
        
        const role = await this.roleRepo.findOneBy({ key: dto.role });
        if (!role) {
          throw new NotFoundException(`Tipo de perfil '${dto.role}' não encontrado`);
        }
        
        const hashedPassword = await bcrypt.hash(dto.password, 10);
        
        existingUser.name = dto.name;
        existingUser.phone = dto.phone;
        existingUser.password = hashedPassword;
        existingUser.role_id = role.id;
        existingUser.role = role;
        existingUser.deleted_at = undefined;
        
        return this.userRepo.save(existingUser);
      } else {
        throw new ConflictException('E-mail já cadastrado');
      }
    }
  
    const role = await this.roleRepo.findOneBy({ key: dto.role });
    if (!role) {
      throw new NotFoundException(`Tipo de perfil '${dto.role}' não encontrado`);
    }
  
    const hashedPassword = await bcrypt.hash(dto.password, 10);
  
    const user = this.userRepo.create({
      name: dto.name,
      email: dto.email,
      phone: dto.phone,
      password: hashedPassword,
      role_id: role.id,
      role: role
    });
  
    return this.userRepo.save(user);
  }

  async softDelete(id: number) {
    const user = await this.userRepo.findOne({ 
      where: { id },
      withDeleted: false 
    });

    if (!user) {
      throw new NotFoundException('Usuário não encontrado');
    }

    await this.userRepo.softDelete(id);

    return { message: 'Usuário excluído com sucesso (deleção lógica)' };
  }
}
