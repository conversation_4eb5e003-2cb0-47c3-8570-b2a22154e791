import { RomaneiosService } from './romaneios.service';
import { CreateRomaneioDto } from './dto/create-romaneio.dto';
import { UpdateRomaneioDto } from './dto/update-romaneio.dto';
export declare class RomaneiosController {
    private readonly romaneiosService;
    constructor(romaneiosService: RomaneiosService);
    create(createRomaneioDto: CreateRomaneioDto): Promise<{
        response: CreateRomaneioDto;
    }>;
    findAll(): Promise<any>;
    findOne(id: string): Promise<import("./entities/romaneio.entity").Romaneio | null>;
    update(id: string, updateRomaneioDto: UpdateRomaneioDto): Promise<import("./entities/romaneio.entity").Romaneio | null>;
    remove(id: string): Promise<{
        message: string;
        deletedProducts: boolean;
        deletedRomaneioOrders: number;
        updatedSeparationOrders: number;
    }>;
    saveDraft(draftData: any): Promise<import("./entities/romaneio-draft.entity").RomaneioDraft>;
    getDraftByExternalCode(externalCode: string): Promise<{
        data: any;
        id: number;
        externalCode: string;
        createdAt: Date;
        updatedAt: Date;
    } | null>;
    calcularPesoRomaneio(id: string): Promise<{
        pesoLiquido: number;
        pesoBruto: number;
    }>;
}
