import { OnModuleInit } from '@nestjs/common';
import { Repository } from 'typeorm';
import { RolesEntity } from 'src/modules/role/entity/role.entity';
import { UserService } from 'src/modules/user/user.service';
export declare class SeedService implements OnModuleInit {
    private readonly roleRepo;
    private readonly userService;
    constructor(roleRepo: Repository<RolesEntity>, userService: UserService);
    onModuleInit(): Promise<void>;
}
