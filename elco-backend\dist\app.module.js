"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const typeorm_1 = require("@nestjs/typeorm");
const user_module_1 = require("./modules/user/user.module");
const app_controller_1 = require("./app.controller");
const app_service_1 = require("./app.service");
const env_validation_1 = require("./config/env.validation");
const auth_module_1 = require("./modules/auth/auth.module");
const seed_module_1 = require("./seed/seed.module");
const schedule_1 = require("@nestjs/schedule");
const separation_orders_controller_1 = require("./modules/separation-orders/separation-orders.controller");
const separation_orders_module_1 = require("./modules/separation-orders/separation-orders.module");
const sync_module_1 = require("./modules/sync/sync.module");
const role_module_1 = require("./modules/role/role.module");
const romaneios_module_1 = require("./modules/romaneios/romaneios.module");
const romaneio_orders_module_1 = require("./modules/romaneio-orders/romaneio-orders.module");
const packaging_module_1 = require("./modules/packaging/packaging.module");
const driver_module_1 = require("./modules/driver/driver.module");
const transporter_module_1 = require("./modules/transporter/transporter.module");
const vehicle_module_1 = require("./modules/vehicle/vehicle.module");
const products_module_1 = require("./modules/products/products.module");
const vnf_module_1 = require("./modules/vnf/vnf.module");
const supplier_email_module_1 = require("./modules/supplier-email/supplier-email.module");
const addresses_module_1 = require("./modules/addresses/addresses.module");
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
                validationSchema: env_validation_1.envSchema,
            }),
            schedule_1.ScheduleModule.forRoot(),
            typeorm_1.TypeOrmModule.forRoot({
                type: 'mysql',
                host: process.env.DB_HOST,
                port: Number(process.env.DB_PORT),
                username: process.env.DB_USER,
                password: process.env.DB_PASS,
                database: process.env.DB_NAME,
                entities: [__dirname + '/**/*.entity{.ts,.js}'],
                synchronize: true,
                logging: false,
                extra: {
                    connectionLimit: 20,
                    acquireTimeout: 30000,
                    timeout: 30000,
                    reconnect: true,
                    idleTimeout: 300000,
                    charset: 'utf8mb4',
                    sql_mode: 'TRADITIONAL',
                    innodb_lock_wait_timeout: 10,
                    transaction_isolation: 'READ-COMMITTED',
                },
                maxQueryExecutionTime: 30000,
                subscribers: [],
                migrations: [],
            }),
            user_module_1.UserModule,
            auth_module_1.AuthModule,
            seed_module_1.SeedModule,
            separation_orders_module_1.SeparationOrderModule,
            sync_module_1.SyncModule,
            role_module_1.RoleModule,
            romaneios_module_1.RomaneiosModule,
            romaneio_orders_module_1.RomaneioOrdersModule,
            packaging_module_1.PackagingModule,
            driver_module_1.DriverModule,
            transporter_module_1.TransporterModule,
            vehicle_module_1.VehicleModule,
            products_module_1.ProductsModule,
            vnf_module_1.VnfModule,
            supplier_email_module_1.SupplierEmailModule,
            addresses_module_1.AddressesModule,
        ],
        controllers: [app_controller_1.AppController, separation_orders_controller_1.SeparationOrderController],
        providers: [app_service_1.AppService],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map