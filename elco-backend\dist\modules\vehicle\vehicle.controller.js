"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VehicleController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const vehicle_service_1 = require("./vehicle.service");
const create_vehicle_dto_1 = require("./dto/create-vehicle.dto");
const update_vehicle_dto_1 = require("./dto/update-vehicle.dto");
const platform_express_1 = require("@nestjs/platform-express");
let VehicleController = class VehicleController {
    vehicleService;
    constructor(vehicleService) {
        this.vehicleService = vehicleService;
    }
    findAll() {
        return this.vehicleService.findAll();
    }
    async findOne(id) {
        return this.vehicleService.findOne(id);
    }
    create(dto, file) {
        return this.vehicleService.create(dto, file);
    }
    async update(id, dto, file) {
        return this.vehicleService.update(id, dto, file);
    }
    async delete(id) {
        const deleted = await this.vehicleService.delete(id);
        if (!deleted)
            throw new common_1.NotFoundException('Veículo não encontrado');
        return { message: 'Veículo excluído com sucesso' };
    }
};
exports.VehicleController = VehicleController;
__decorate([
    (0, common_1.Get)('/list'),
    (0, swagger_1.ApiOperation)({
        summary: 'Listar todos os veículos',
        description: 'Retorna uma lista com todos os veículos cadastrados na frota.',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de veículos',
        schema: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    id: { type: 'number', example: 1 },
                    plate: { type: 'string', example: 'ABC-1234' },
                    model: { type: 'string', example: 'Volvo FH' },
                    brand: { type: 'string', example: 'Volvo' },
                    year: { type: 'number', example: 2020 },
                    capacity: { type: 'number', example: 25000 },
                    isActive: { type: 'boolean', example: true },
                },
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], VehicleController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('/view/:id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Obter veículo por ID',
        description: 'Retorna os detalhes completos de um veículo específico.',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID do veículo',
        type: 'number',
        example: 1,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Dados do veículo',
        schema: {
            type: 'object',
            properties: {
                id: { type: 'number', example: 1 },
                plate: { type: 'string', example: 'ABC-1234' },
                model: { type: 'string', example: 'Volvo FH' },
                brand: { type: 'string', example: 'Volvo' },
                year: { type: 'number', example: 2020 },
                capacity: { type: 'number', example: 25000 },
                vehicleDocument: { type: 'string', example: 'path/to/document.pdf' },
                isActive: { type: 'boolean', example: true },
                createdAt: { type: 'string', format: 'date-time' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Veículo não encontrado' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], VehicleController.prototype, "findOne", null);
__decorate([
    (0, common_1.Post)('/create'),
    (0, swagger_1.ApiOperation)({
        summary: 'Criar novo veículo',
        description: 'Adiciona um novo veículo à frota. Pode incluir upload de documentos.',
    }),
    (0, swagger_1.ApiConsumes)('multipart/form-data'),
    (0, swagger_1.ApiBody)({
        description: 'Dados do veículo e documentos (opcional)',
        schema: {
            type: 'object',
            properties: {
                plate: { type: 'string', example: 'ABC-1234' },
                model: { type: 'string', example: 'Volvo FH' },
                brand: { type: 'string', example: 'Volvo' },
                year: { type: 'number', example: 2020 },
                capacity: { type: 'number', example: 25000 },
                vehicleDocument: {
                    type: 'string',
                    format: 'binary',
                    description: 'Documento do veículo (PDF, JPG, PNG)',
                },
            },
            required: ['plate', 'model', 'brand', 'year'],
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Veículo criado com sucesso',
        schema: {
            type: 'object',
            properties: {
                id: { type: 'number', example: 1 },
                plate: { type: 'string', example: 'ABC-1234' },
                model: { type: 'string', example: 'Volvo FH' },
                brand: { type: 'string', example: 'Volvo' },
                year: { type: 'number', example: 2020 },
                capacity: { type: 'number', example: 25000 },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dados inválidos' }),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('vehicleDocument')),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.UploadedFile)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_vehicle_dto_1.CreateVehicleDto, Object]),
    __metadata("design:returntype", void 0)
], VehicleController.prototype, "create", null);
__decorate([
    (0, common_1.Put)('/update/:id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Atualizar veículo',
        description: 'Atualiza os dados de um veículo existente.',
    }),
    (0, swagger_1.ApiConsumes)('multipart/form-data'),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID do veículo',
        type: 'number',
        example: 1,
    }),
    (0, swagger_1.ApiBody)({
        description: 'Dados do veículo e documentos (opcional)',
        schema: {
            type: 'object',
            properties: {
                plate: { type: 'string', example: 'ABC-1234' },
                model: { type: 'string', example: 'Volvo FH' },
                brand: { type: 'string', example: 'Volvo' },
                vehicleDocument: {
                    type: 'string',
                    format: 'binary',
                    description: 'Documento do veículo (PDF, JPG, PNG)',
                },
                keepExistingDocument: { type: 'string', example: 'true' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Veículo atualizado com sucesso',
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Veículo não encontrado' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dados inválidos' }),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('vehicleDocument')),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.UploadedFile)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, update_vehicle_dto_1.UpdateVehicleDto, Object]),
    __metadata("design:returntype", Promise)
], VehicleController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)('/delete/:id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Deletar veículo',
        description: 'Remove um veículo da frota.',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID do veículo',
        type: 'number',
        example: 1,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Veículo excluído com sucesso',
        schema: {
            type: 'object',
            properties: {
                message: { type: 'string', example: 'Veículo excluído com sucesso' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Veículo não encontrado' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], VehicleController.prototype, "delete", null);
exports.VehicleController = VehicleController = __decorate([
    (0, swagger_1.ApiTags)('Veículos'),
    (0, common_1.Controller)('vehicles'),
    __metadata("design:paramtypes", [vehicle_service_1.VehicleService])
], VehicleController);
//# sourceMappingURL=vehicle.controller.js.map