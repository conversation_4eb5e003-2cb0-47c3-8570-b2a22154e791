import { DataSource, Repository } from 'typeorm';
import { SeparationOrder } from './entity/separation-order.entity';
interface FindAllFilters {
    search?: string;
    status?: string;
    depositorName?: string;
    clientFilter?: string;
}
export declare class SeparationOrderService {
    private readonly repo;
    private readonly dataSource;
    constructor(repo: Repository<SeparationOrder>, dataSource: DataSource);
    private findProductByCode;
    saveNew(orders: SeparationOrder[]): Promise<SeparationOrder[]>;
    findAll(page?: number, limit?: number, filters?: FindAllFilters): Promise<{
        orders: SeparationOrder[];
        total: number;
        page: number;
        limit: number;
        totalPages: number;
    }>;
    getStats(): Promise<{
        totalOrders: number;
        completedCount: number;
        linkedCount: number;
        pendingCount: number;
        errorCount: number;
        completedPercentage: number;
        linkedPercentage: number;
        pendingPercentage: number;
    }>;
    getFilterOptions(): Promise<{
        statuses: any[];
        depositors: any[];
        clients: string[];
    }>;
    getProductDetails(internalCode: string): Promise<any[]>;
    linkOrder(internalCode: string, tipo: 'MATRIZ' | 'FILIAL'): Promise<SeparationOrder>;
    linkOrderVolume(orderId: number, volume?: number): Promise<void>;
}
export {};
