"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SupplierEmailModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const supplier_email_entity_1 = require("./entity/supplier-email.entity");
const supplier_email_service_1 = require("./supplier-email.service");
const supplier_email_controller_1 = require("./supplier-email.controller");
const oracle_module_1 = require("../oracle/oracle.module");
let SupplierEmailModule = class SupplierEmailModule {
};
exports.SupplierEmailModule = SupplierEmailModule;
exports.SupplierEmailModule = SupplierEmailModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([supplier_email_entity_1.SupplierEmail]), oracle_module_1.OracleModule],
        providers: [supplier_email_service_1.SupplierEmailService],
        controllers: [supplier_email_controller_1.SupplierEmailController],
    })
], SupplierEmailModule);
//# sourceMappingURL=supplier-email.module.js.map