"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const products_service_1 = require("./products.service");
const create_product_dto_1 = require("./dto/create-product.dto");
const create_product_weight_dto_1 = require("./dto/create-product-weight.dto");
const update_product_weight_dto_1 = require("./dto/update-product-weight.dto");
const verify_multiple_products_dto_1 = require("./dto/verify-multiple-products.dto");
let ProductsController = class ProductsController {
    productsService;
    constructor(productsService) {
        this.productsService = productsService;
    }
    create(createProductDto) {
        return this.productsService.create(createProductDto);
    }
    findAll() {
        return this.productsService.findAll();
    }
    async findAllProductWeights() {
        return this.productsService.findAllProductWeights();
    }
    findOne(id) {
        return this.productsService.findOne(+id);
    }
    remove(id) {
        return this.productsService.remove(+id);
    }
    async verifyMultipleProducts(body) {
        if (!body.romaneioId) {
            throw new Error('RomaneioId é obrigatório');
        }
        return this.productsService.verifyMultipleProducts(body.romaneioId, body.products, body.observations);
    }
    verifyManyProducts(body) {
        return this.productsService.verifyManyProducts(body.ids);
    }
    syncProductPackaging(data) {
        return this.productsService.syncProductPackaging(data);
    }
    updateWeight(id, weight) {
        return this.productsService.updateWeight(+id, weight);
    }
    async createProductWeight(body) {
        if (!body.productCode && !body.productName) {
            return { error: 'É obrigatório informar o código OU o nome do produto.' };
        }
        return this.productsService.createProductWeight(body);
    }
    async updateProductWeight(id, updateProductWeightDto) {
        return this.productsService.updateProductWeight(id, updateProductWeightDto);
    }
    async deleteProductWeight(id) {
        return this.productsService.deleteProductWeight(id);
    }
};
exports.ProductsController = ProductsController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Criar novo produto',
        description: 'Cadastra um novo produto no sistema.',
    }),
    (0, swagger_1.ApiBody)({ type: create_product_dto_1.CreateProductDto }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Produto criado com sucesso',
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dados inválidos' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_product_dto_1.CreateProductDto]),
    __metadata("design:returntype", void 0)
], ProductsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Listar todos os produtos',
        description: 'Retorna uma lista com todos os produtos cadastrados.',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de produtos',
        schema: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    id: { type: 'number', example: 1 },
                    code: { type: 'string', example: 'PROD001' },
                    name: { type: 'string', example: 'Produto Exemplo' },
                    description: { type: 'string', example: 'Descrição do produto' },
                    weight: { type: 'number', example: 1.5 },
                    isActive: { type: 'boolean', example: true },
                },
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], ProductsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('/weights'),
    (0, swagger_1.ApiOperation)({
        summary: 'Listar pesos de produtos',
        description: 'Retorna uma lista com todos os pesos de produtos configurados.',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de pesos de produtos',
        schema: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    id: { type: 'string', example: 'uuid-string' },
                    productCode: { type: 'string', example: 'PROD001' },
                    productName: { type: 'string', example: 'Produto Exemplo' },
                    weight: { type: 'number', example: 1.5 },
                    unit: { type: 'string', example: 'kg' },
                },
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ProductsController.prototype, "findAllProductWeights", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Obter produto por ID',
        description: 'Retorna os detalhes de um produto específico.',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID do produto',
        type: 'string',
        example: '1',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Dados do produto',
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Produto não encontrado' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ProductsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Deletar produto',
        description: 'Remove um produto do sistema.',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID do produto',
        type: 'string',
        example: '1',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Produto removido com sucesso',
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Produto não encontrado' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ProductsController.prototype, "remove", null);
__decorate([
    (0, common_1.Patch)('/verify'),
    (0, swagger_1.ApiOperation)({
        summary: 'Verificar múltiplos produtos',
        description: 'Verifica múltiplos produtos em um romaneio com observações.',
    }),
    (0, swagger_1.ApiBody)({
        type: verify_multiple_products_dto_1.VerifyMultipleProductsDto,
        description: 'Dados para verificação de produtos',
        examples: {
            verify: {
                summary: 'Exemplo de verificação',
                value: {
                    romaneioId: 1,
                    products: [
                        { id: 1, verified: true },
                        { id: 2, verified: false },
                    ],
                    observations: 'Produto 2 com avaria',
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Produtos verificados com sucesso',
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'RomaneioId é obrigatório' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [verify_multiple_products_dto_1.VerifyMultipleProductsDto]),
    __metadata("design:returntype", Promise)
], ProductsController.prototype, "verifyMultipleProducts", null);
__decorate([
    (0, common_1.Patch)('/verify-many'),
    (0, swagger_1.ApiOperation)({
        summary: 'Verificar produtos por IDs',
        description: 'Verifica vários produtos de uma vez usando uma lista de IDs.',
    }),
    (0, swagger_1.ApiBody)({
        description: 'Lista de IDs dos produtos',
        schema: {
            type: 'object',
            properties: {
                ids: {
                    type: 'array',
                    items: { type: 'number' },
                    example: [1, 2, 3, 4],
                },
            },
            required: ['ids'],
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Produtos verificados com sucesso',
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], ProductsController.prototype, "verifyManyProducts", null);
__decorate([
    (0, common_1.Post)('/product-packaging'),
    (0, swagger_1.ApiOperation)({
        summary: 'Sincronizar produto com embalagem',
        description: 'Vincula um produto a uma embalagem em um pedido específico.',
    }),
    (0, swagger_1.ApiBody)({
        description: 'Dados para vinculação produto-embalagem',
        schema: {
            type: 'object',
            properties: {
                orderId: { type: 'number', example: 1 },
                productId: { type: 'number', example: 1 },
                packagingId: { type: 'number', example: 1 },
            },
            required: ['orderId', 'productId', 'packagingId'],
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Vinculação realizada com sucesso',
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], ProductsController.prototype, "syncProductPackaging", null);
__decorate([
    (0, common_1.Patch)(':id/weight'),
    (0, swagger_1.ApiOperation)({
        summary: 'Atualizar peso do produto',
        description: 'Atualiza o peso de um produto específico.',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID do produto',
        type: 'string',
        example: '1',
    }),
    (0, swagger_1.ApiBody)({
        description: 'Novo peso do produto',
        schema: {
            type: 'object',
            properties: {
                weight: { type: 'number', example: 2.5 },
            },
            required: ['weight'],
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Peso atualizado com sucesso',
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)('weight')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number]),
    __metadata("design:returntype", void 0)
], ProductsController.prototype, "updateWeight", null);
__decorate([
    (0, common_1.Post)('/weights'),
    (0, swagger_1.ApiOperation)({
        summary: 'Criar peso de produto',
        description: 'Cria uma nova configuração de peso para um produto. Requer código OU nome do produto.',
    }),
    (0, swagger_1.ApiBody)({
        type: create_product_weight_dto_1.CreateProductWeightDto,
        description: 'Dados do peso do produto',
        examples: {
            byCode: {
                summary: 'Por código do produto',
                value: {
                    productCode: 'PROD001',
                    weight: 1.5,
                    unit: 'kg',
                },
            },
            byName: {
                summary: 'Por nome do produto',
                value: {
                    productName: 'Produto Exemplo',
                    weight: 2.0,
                    unit: 'kg',
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Peso de produto criado com sucesso',
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'É obrigatório informar o código OU o nome do produto',
        schema: {
            type: 'object',
            properties: {
                error: {
                    type: 'string',
                    example: 'É obrigatório informar o código OU o nome do produto.',
                },
            },
        },
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ProductsController.prototype, "createProductWeight", null);
__decorate([
    (0, common_1.Patch)('/weights/:id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Atualizar peso de produto',
        description: 'Atualiza uma configuração de peso de produto existente.',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID do peso do produto',
        type: 'string',
    }),
    (0, swagger_1.ApiBody)({ type: update_product_weight_dto_1.UpdateProductWeightDto }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Peso de produto atualizado com sucesso',
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Peso de produto não encontrado' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_product_weight_dto_1.UpdateProductWeightDto]),
    __metadata("design:returntype", Promise)
], ProductsController.prototype, "updateProductWeight", null);
__decorate([
    (0, common_1.Delete)('/weights/:id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Deletar peso de produto',
        description: 'Remove uma configuração de peso de produto.',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID do peso do produto',
        type: 'string',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Peso de produto removido com sucesso',
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Peso de produto não encontrado' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ProductsController.prototype, "deleteProductWeight", null);
exports.ProductsController = ProductsController = __decorate([
    (0, swagger_1.ApiTags)('Produtos'),
    (0, common_1.Controller)('products'),
    __metadata("design:paramtypes", [products_service_1.ProductsService])
], ProductsController);
//# sourceMappingURL=products.controller.js.map