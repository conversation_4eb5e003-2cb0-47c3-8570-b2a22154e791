
import React from 'react';
import { cn } from '@/lib/utils';
import { cva } from 'class-variance-authority';

interface StatCardProps {
  title: string;
  value: string | number;
  progress?: number;
  className?: string;
  variant?: "green" | "amber" | "blue" | "rose" | "red";
  icon?: React.ReactElement;
  change?: {
    value: string;
    positive: boolean;
  };
}

const statCardVariants = cva(
  "relative rounded-xl shadow-sm p-5 bg-white overflow-hidden",
  {
    variants: {
      variant: {
        green: "border-b-4 border-emerald-500",
        amber: "border-b-4 border-amber-500",
        blue: "border-b-4 border-blue-500",
        red: "border-b-4 border-red-500",
        rose: "border-b-4 border-pink-500"
      }
    },
    defaultVariants: {
      variant: "amber"
    }
  }
)

const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  progress,
  className,
  variant = "amber",
  icon,
  change
}) => {
  const radius = 22;
  const circumference = 2 * Math.PI * radius;
  const strokeDashoffset = progress !== undefined ? circumference - (progress / 100) * circumference : circumference;

  const progressColorClass = (variant: StatCardProps['variant']) => {
    switch(variant) {
      case 'green': return 'text-emerald-500';
      case 'amber': return 'text-amber-500';
      case 'blue': return 'text-blue-500';
      case 'red': return 'text-red-500';
      case 'rose': return 'text-pink-500';
      default: return 'text-gray-500';
    }
  }

  return (
    <div className={cn(statCardVariants({ variant }), className)}>
      <div className="flex justify-between items-start">
        <div className="flex flex-col">
          <div className="flex items-center gap-2 mb-1">
            {icon && (
              <div className={cn("p-1 rounded-lg", progressColorClass(variant))}>
                {React.cloneElement(icon, { className: "h-5 w-5" })}
              </div>
            )}
            <p className="text-xs font-semibold text-gray-500 uppercase tracking-wider">
              {title}
            </p>
          </div>
          <p className="text-3xl font-bold text-gray-800 mb-2">
            {value}
          </p>
          {change && (
            <div className={cn(
              "flex items-center gap-1 text-sm font-medium",
              change.positive ? "text-green-600" : "text-red-600"
            )}>
              <span>{change.positive ? "↗" : "↘"}</span>
              <span>{change.value}</span>
            </div>
          )}
        </div>

        {progress !== undefined && (
          <div className="relative w-12 h-12">
            <svg className="w-full h-full transform -rotate-90">
              <circle
                className="text-gray-200"
                strokeWidth="4"
                stroke="currentColor"
                fill="transparent"
                r={radius}
                cx="24"
                cy="24"
              />
              <circle
                className={cn("transition-all duration-500", progressColorClass(variant))}
                strokeWidth="4"
                strokeDasharray={circumference}
                strokeDashoffset={strokeDashoffset}
                strokeLinecap="round"
                stroke="currentColor"
                fill="transparent"
                r={radius}
                cx="24"
                cy="24"
              />
            </svg>
            <span className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-xs font-bold text-gray-700">
              {progress}
            </span>
          </div>
        )}
      </div>
    </div>
  );
};

export default StatCard;
