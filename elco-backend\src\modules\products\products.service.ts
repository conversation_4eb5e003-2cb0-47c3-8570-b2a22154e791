import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Product } from './entities/product.entity';
import { In, Repository, UpdateResult } from 'typeorm';
import { ProductWeights } from './entities/product_weights.entity';
import { CreateProductWeightDto } from './dto/create-product-weight.dto';
import { UpdateProductWeightDto } from './dto/update-product-weight.dto';
import { Romaneio } from '../romaneios/entities/romaneio.entity';
import { VerifyProductItemDto } from './dto/verify-multiple-products.dto';


@Injectable()
export class ProductsService {
  constructor(
    @InjectRepository(Product)
    private readonly productRepository: Repository<Product>,
    @InjectRepository(ProductWeights)
    private readonly productWeightsRepository: Repository<ProductWeights>,
    @InjectRepository(Romaneio)
    private readonly romaneioRepository: Repository<Romaneio>,
    @InjectRepository(ProductWeights)
    private readonly productWeightRepository: Repository<ProductWeights>
  ) { }

  async create(createProductDto: CreateProductDto) {
    const newData = new Product();

    newData.idEnterpriseExternal = createProductDto.idEnterpriseExternal;
    newData.idDepositorExternal = createProductDto.idDepositorExternal;
    newData.depositor = createProductDto.depositor;
    newData.idExternal = createProductDto.idExternal;
    newData.name = createProductDto.name;
    newData.fullName = createProductDto.fullName;
    newData.description = createProductDto.description;
    const response = await this.productRepository.save(newData);

    return {
      id: response.id,
      idEnterpriseExternal: response.idEnterpriseExternal,
      idDepositorExternal: response.idDepositorExternal,
      depositor: response.depositor,
      idExternal: response.idExternal,
      name: response.name,
      fullName: response.fullName,
      description: response.description,
    };
  }

  findAll() {
    return `This action returns all products`;
  }

  findOne(id: number) {
    return `This action returns a #${id} product`;
  }

  update(id: number, updateProductDto: UpdateProductDto) {
    return `This action updates a #${id} product`;
  }

  remove(id: number) {
    return `This action removes a #${id} product`;
  }

  async verifyMultipleProducts(
    romaneioId: number,
    products: VerifyProductItemDto[],
    observations?: string
  ): Promise<Product[]> {
    let updatedCount = 0;
  
    const productIds = products.map(p => p.id);
    const foundProducts = await this.productRepository.find({
      where: { id: In(productIds) }
    });
  
    const now = new Date();
  
    for (const product of foundProducts) {
      const updateData = products.find(p => p.id === product.id);
      if (!updateData) continue;
  
      const verifiedAtDate = updateData.verifiedAt ? new Date(updateData.verifiedAt) : now;
      const year = verifiedAtDate.getFullYear();
      const month = (verifiedAtDate.getMonth() + 1).toString().padStart(2, '0');
      const day = verifiedAtDate.getDate().toString().padStart(2, '0');
      const hours = verifiedAtDate.getHours().toString().padStart(2, '0');
      const minutes = verifiedAtDate.getMinutes().toString().padStart(2, '0');
      const seconds = verifiedAtDate.getSeconds().toString().padStart(2, '0');
  
      const verifiedDate = `${year}-${month}-${day}`;
      const verifiedTime = `${hours}:${minutes}:${seconds}`;
  
      product.verified = updateData.verified !== undefined ? updateData.verified : true;
      product.verifiedDate = verifiedDate;
      product.verifiedTime = verifiedTime;
      product.verifiedBy = updateData.userName || 'Sistema';
      product.updatedAt = now;
      updatedCount++;
    }
  
    if (updatedCount > 0) {
      await this.productRepository.save(foundProducts);
    }
  
    const romaneio = await this.romaneioRepository.findOneBy({ id: romaneioId });
    if (romaneio) {
      romaneio.statusId = 5; // Status 5: Verificado Manualmente
      romaneio.verifiedObservations = observations || '';
      await this.romaneioRepository.save(romaneio);
    }
  
    return foundProducts;
  }

  async verifyManyProducts(ids: number[]): Promise<{ updated: number }> {
    const products = await this.productRepository.find({ where: { id: In(ids) } });

    let updatedCount = 0;
    for (const product of products) {
      product.verified = !product.verified;
      product.updatedAt = new Date();
      updatedCount++;
    }

    if (updatedCount > 0) {
      await this.productRepository.save(products);
    }

    return { updated: updatedCount };
  }

  async syncProductPackaging(
    data: any,
  ): Promise<{ updated: number }> {
    const { orderId, productId, packagingId } = data;

    const result: UpdateResult = await this.productRepository.update(
      { id: productId, idSeparationOrder: orderId },
      { idPackaging: packagingId },
    );

    if (!result.affected || result.affected === 0) {
      throw new NotFoundException(
        `Produto ${productId} no pedido ${orderId} não encontrado.`,
      );
    }

    return { updated: result.affected };
  }

  async updateWeight(id: number, weight: number): Promise<{ updated: number }> {
    const product = await this.productRepository.findOneBy({ id });
    if (!product) {
      throw new NotFoundException(`Produto ${id} não encontrado.`);
    }
    product.weight = weight;
    product.updatedAt = new Date();
    await this.productRepository.save(product);
    return { updated: 1 };
  }

  async createProductWeight(body: Partial<CreateProductWeightDto>) {
    if (!body.productCode && !body.productName) {
      throw new Error('É obrigatório informar o código OU o nome do produto.');
    }
    const newWeight = this.productWeightsRepository.create({
      productCode: body.productCode ?? undefined,
      productName: body.productName ?? undefined,
      weight: body.weight,
      unit: body.unit,
    } as Partial<ProductWeights>);
    const saved = await this.productWeightsRepository.save(newWeight);
    return saved;
  }

  async findAllProductWeights() {
    return this.productWeightsRepository.find();
  }

  async updateProductWeight(id: string, updateProductWeightDto: UpdateProductWeightDto) {
    const weight = await this.productWeightsRepository.findOneBy({ id });

    if (!weight) {
      throw new NotFoundException(`Peso de produto com ID ${id} não encontrado.`);
    }

    Object.assign(weight, updateProductWeightDto);

    return this.productWeightsRepository.save(weight);
  }

  async deleteProductWeight(id: string) {
    const result = await this.productWeightsRepository.delete(id);

    if (result.affected === 0) {
      throw new NotFoundException(`Peso de produto com ID ${id} não encontrado.`);
    }

    return { deleted: true };
  }
}
