import { Module, forwardRef } from '@nestjs/common';
import { MegaService } from './mega.service';
import { MegaController } from './mega.controller';
import { OracleModule } from '../oracle/oracle.module';
import { InvoicesModule } from '../invoices/invoices.module';
import { ProductsModule } from '../products/products.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Product } from '../products/entities/product.entity';
import { Romaneio } from '../romaneios/entities/romaneio.entity';


const MegaSoapClientProvider = {
  provide: 'MegaSoapClient',
  useValue: {
    EnviarNotaFiscalAsync: async (args: any) => {
      return [{ mensagem: 'Mock de resposta do MegaSoapClient', args }];
    },
  },
};

@Module({
  imports: [
    TypeOrmModule.forFeature([Product, Romaneio]),
    OracleModule, InvoicesModule, forwardRef(() => ProductsModule)
  ],
  controllers: [MegaController],
  providers: [MegaService, MegaSoapClientProvider],
  exports: [MegaService]
})
export class MegaModule {}
