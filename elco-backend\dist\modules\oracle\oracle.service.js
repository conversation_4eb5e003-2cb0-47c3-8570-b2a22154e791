"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OracleService = void 0;
const common_1 = require("@nestjs/common");
let OracleService = class OracleService {
    oracleConnectionFactory;
    constructor(oracleConnectionFactory) {
        this.oracleConnectionFactory = oracleConnectionFactory;
    }
    async executeQuery(query, params = []) {
        const connection = await this.oracleConnectionFactory();
        try {
            const result = await connection.execute(query, params);
            return result.rows;
        }
        catch (error) {
            throw new Error(`Erro ao executar query Oracle: ${error.message}`);
        }
        finally {
            await connection.close();
        }
    }
};
exports.OracleService = OracleService;
exports.OracleService = OracleService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)('OracleConnectionFactory')),
    __metadata("design:paramtypes", [Function])
], OracleService);
//# sourceMappingURL=oracle.service.js.map