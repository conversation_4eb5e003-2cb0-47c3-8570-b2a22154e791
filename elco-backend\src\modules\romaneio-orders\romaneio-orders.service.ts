import { Injectable } from '@nestjs/common';
import { CreateRomaneioOrderDto } from './dto/create-romaneio-order.dto';
import { UpdateRomaneioOrderDto } from './dto/update-romaneio-order.dto';

@Injectable()
export class RomaneioOrdersService {
  create(createRomaneioOrderDto: CreateRomaneioOrderDto) {
    return 'This action adds a new romaneioOrder';
  }

  findAll() {
    return `This action returns all romaneioOrders`;
  }

  findOne(id: number) {
    return `This action returns a #${id} romaneioOrder`;
  }

  update(id: number, updateRomaneioOrderDto: UpdateRomaneioOrderDto) {
    return `This action updates a #${id} romaneioOrder`;
  }

  remove(id: number) {
    return `This action removes a #${id} romaneioOrder`;
  }
}
