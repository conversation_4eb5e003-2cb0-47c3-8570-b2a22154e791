"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VnfController = void 0;
const common_1 = require("@nestjs/common");
const vnf_service_1 = require("./vnf.service");
const swagger_1 = require("@nestjs/swagger");
const processar_lote_por_data_dto_1 = require("./dto/processar-lote-por-data.dto");
let VnfController = class VnfController {
    vnfService;
    constructor(vnfService) {
        this.vnfService = vnfService;
    }
    async processarXml(body) {
        return this.vnfService.processarXmlNfe(body);
    }
    async listarErros() {
        return this.vnfService.listarErrosVnf();
    }
    async consultarPorChave(chaveAcesso) {
        return this.vnfService.consultarXmlPorChave(chaveAcesso);
    }
    async compararPedidoNota(body) {
        return this.vnfService.processarComparacaoPedidoNotaFiscal(body.pedido, body.notaFiscal, body.numeroNota, body.clienteExternalCode);
    }
    async enviarEmailDivergencia(erroId) {
        return this.vnfService.enviarEmailDivergencia(erroId);
    }
    async enviarEmailsLote(body) {
        console.log('[VnfController] Recebendo requisição para enviar emails em lote');
        console.log('[VnfController] Erro IDs:', body.erroIds);
        console.log('[VnfController] Emails adicionais:', body.emailsAdicionais);
        return this.vnfService.enviarEmailsEmLote(body.erroIds, body.emailsAdicionais);
    }
    async testarEmail() {
        return this.vnfService.testarConexaoEmail();
    }
    async verificarConfiguracaoEmail() {
        return {
            host: process.env.SMTP_HOST || 'smtp.gmail.com',
            port: parseInt(process.env.SMTP_PORT || '587'),
            secure: process.env.SMTP_SECURE === 'true',
            user: process.env.SMTP_USER || 'não configurado',
            fromName: process.env.SMTP_FROM_NAME ||
                'Departamento de Compras - Elco Engenharia',
            hasPassword: !!process.env.SMTP_PASS,
        };
    }
    async testarEnvioEmail(email) {
        return this.vnfService.testarEnvioEmail(email);
    }
    async aceitarErro(erroId) {
        return this.vnfService.aceitarErro(erroId);
    }
    async recusarErro(erroId) {
        return this.vnfService.recusarErro(erroId);
    }
    async salvarEmailFornecedor(body) {
        return this.vnfService.salvarEmailFornecedor(body.nomeFornecedor, body.email);
    }
    async processarLotePorData(body) {
        return this.vnfService.processarLoteXmlsPorData(body.data);
    }
    async listarConsultasProcessadas(data) {
        return this.vnfService.listarConsultasProcessadas(data);
    }
    async obterStatusProcessamentoAutomatico() {
        const status = this.vnfService.getStatusProcessamentoAutomatico();
        return {
            ...status,
            cronExpression: '0 */15 * * * *',
            description: 'Processamento automático executado a cada 15 minutos (00:00, 00:15, 00:30, 00:45 de cada hora)',
        };
    }
};
exports.VnfController = VnfController;
__decorate([
    (0, common_1.Post)('processar-xml'),
    (0, swagger_1.ApiOperation)({
        summary: 'Processar XML NFe',
        description: 'Processa um arquivo XML de nota fiscal eletrônica e extrai as informações relevantes.',
    }),
    (0, swagger_1.ApiBody)({
        description: 'Dados do XML para processamento',
        schema: {
            type: 'object',
            properties: {
                xmlContent: {
                    type: 'string',
                    description: 'Conteúdo XML da nota fiscal eletrônica',
                    example: '<?xml version="1.0" encoding="UTF-8"?><nfeProc>...</nfeProc>',
                },
                fileName: {
                    type: 'string',
                    example: 'nota_fiscal_123.xml',
                    description: 'Nome do arquivo XML (opcional)',
                },
                origem: {
                    type: 'string',
                    example: 'UPLOAD_MANUAL',
                    description: 'Origem do XML (opcional)',
                },
            },
            required: ['xmlContent'],
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'XML processado com sucesso',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                chaveAcesso: {
                    type: 'string',
                    example: '35200714200166000187550010000000007907734918',
                },
                numero: { type: 'string', example: '000000007' },
                serie: { type: 'string', example: '001' },
                valorTotal: { type: 'number', example: 1250.5 },
                dataEmissao: { type: 'string', format: 'date-time' },
                fornecedor: {
                    type: 'object',
                    properties: {
                        cnpj: { type: 'string', example: '14.200.166/0001-87' },
                        razaoSocial: {
                            type: 'string',
                            example: 'Empresa Fornecedora Ltda',
                        },
                    },
                },
                itens: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            codigo: { type: 'string', example: 'PROD001' },
                            descricao: { type: 'string', example: 'Produto Exemplo' },
                            quantidade: { type: 'number', example: 10 },
                            valorUnitario: { type: 'number', example: 125.05 },
                        },
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Erro no processamento do XML',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: false },
                error: { type: 'string', example: 'XML inválido ou mal formado' },
                details: {
                    type: 'string',
                    example: 'Elemento <infNFe> não encontrado',
                },
            },
        },
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], VnfController.prototype, "processarXml", null);
__decorate([
    (0, common_1.Get)('erros'),
    (0, swagger_1.ApiOperation)({
        summary: 'Listar erros de VNF',
        description: 'Retorna uma lista com todos os erros ocorridos no processamento de VNF/XMLs.',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de erros de VNF',
        schema: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    id: { type: 'number', example: 1 },
                    chaveAcesso: {
                        type: 'string',
                        example: '35200714200166000187550010000000007907734918',
                    },
                    errorMessage: { type: 'string', example: 'Erro na validação do XML' },
                    errorDetails: {
                        type: 'string',
                        example: 'Campo obrigatório não encontrado: <emit>',
                    },
                    xmlContent: {
                        type: 'string',
                        description: 'Conteúdo XML que causou o erro',
                    },
                    status: {
                        type: 'string',
                        enum: ['PENDENTE', 'RESOLVIDO', 'IGNORADO'],
                        example: 'PENDENTE',
                    },
                    createdAt: { type: 'string', format: 'date-time' },
                    updatedAt: { type: 'string', format: 'date-time' },
                },
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], VnfController.prototype, "listarErros", null);
__decorate([
    (0, common_1.Post)('consultar-por-chave'),
    (0, swagger_1.ApiOperation)({
        summary: 'Consultar XML por chave de acesso',
        description: 'Consulta um XML específico através da chave de acesso da nota fiscal.',
    }),
    (0, swagger_1.ApiBody)({
        description: 'Chave de acesso para consulta',
        schema: {
            type: 'object',
            properties: {
                chaveAcesso: {
                    type: 'string',
                    example: '35200714200166000187550010000000007907734918',
                    description: 'Chave de acesso da nota fiscal (44 dígitos)',
                },
            },
            required: ['chaveAcesso'],
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'XML encontrado e processado',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: 'XML consultado com sucesso' },
                xml: { type: 'string', description: 'Conteúdo XML da nota fiscal' },
                pdfBase64: {
                    type: 'string',
                    description: 'PDF da nota fiscal em base64',
                },
                chaveAcesso: {
                    type: 'string',
                    example: '35200714200166000187550010000000007907734918',
                },
                numeroPedido: {
                    type: 'string',
                    example: '56524',
                    description: 'Número do pedido extraído do XML',
                },
                dadosPedido: {
                    type: 'object',
                    description: 'Dados do pedido consultados no portal de compras',
                    properties: {
                        success: { type: 'boolean', example: true },
                        data: {
                            type: 'object',
                            description: 'Dados retornados pelo portal de compras',
                        },
                        error: {
                            type: 'string',
                            description: 'Mensagem de erro caso a consulta falhe',
                        },
                    },
                },
                notaFiscal: {
                    type: 'object',
                    description: 'Dados estruturados da nota fiscal',
                    properties: {
                        versao: { type: 'string', example: '4.00' },
                        numeroNota: { type: 'string', example: '9199' },
                        dataEmissao: {
                            type: 'string',
                            example: '2025-07-03T14:00:41-03:00',
                        },
                        emitente: {
                            type: 'object',
                            properties: {
                                cnpj: { type: 'string', example: '35629600000106' },
                                nome: {
                                    type: 'string',
                                    example: 'PERFISAELETRO INDUSTRIA DE ELETROCALHAS LTDA',
                                },
                            },
                        },
                        destinatario: {
                            type: 'object',
                            properties: {
                                cnpj: { type: 'string', example: '77521375000121' },
                                nome: {
                                    type: 'string',
                                    example: 'ELCO ENGENHARIA DE MONTAGENS LTDA',
                                },
                            },
                        },
                        produtos: {
                            type: 'array',
                            items: {
                                type: 'object',
                                properties: {
                                    codigo: { type: 'string', example: 'PCE13664' },
                                    descricao: {
                                        type: 'string',
                                        example: 'ABRACADEIRA U VERG. 1 X 1/4 CPTO. G.F',
                                    },
                                    quantidade: { type: 'string', example: '50.0000' },
                                    valorTotal: { type: 'string', example: '348.50' },
                                },
                            },
                        },
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'XML não encontrado',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: false },
                message: {
                    type: 'string',
                    example: 'XML não encontrado para a chave de acesso informada',
                },
                chaveAcesso: {
                    type: 'string',
                    example: '35200714200166000187550010000000007907734918',
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Chave de acesso inválida' }),
    __param(0, (0, common_1.Body)('chaveAcesso')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], VnfController.prototype, "consultarPorChave", null);
__decorate([
    (0, common_1.Post)('comparar-pedido-nota'),
    (0, swagger_1.ApiOperation)({
        summary: 'Comparar pedido com nota fiscal',
        description: 'Compara um pedido do portal de compras com uma nota fiscal e retorna as divergências encontradas.',
    }),
    (0, swagger_1.ApiBody)({
        description: 'Dados do pedido e nota fiscal para comparação',
        schema: {
            type: 'object',
            properties: {
                pedido: {
                    type: 'object',
                    description: 'Dados do pedido do portal de compras',
                    example: {
                        pedidoERP: '40514',
                        cnpjFornecedor: '16891049000111',
                        cnpjPlanta: '77521375000121',
                        nomeFornecedor: '8.8 PARAFUSOS E TINTAS',
                        condicaoPagamento: '07D',
                        valorTotalComFrete: 10.0,
                        itens: [
                            {
                                sequencial: 1,
                                codigoUnidadeMedida: 'PC',
                                nmc: '0000.00.00',
                                valorUnitario: 1.0,
                                quantidade: 10.0,
                            },
                        ],
                    },
                },
                notaFiscal: {
                    type: 'object',
                    description: 'Dados da nota fiscal',
                    example: {
                        numeroNota: '9199',
                        emitente: {
                            cnpj: '35629600000106',
                            nome: 'PERFISAELETRO INDUSTRIA DE ELETROCALHAS LTDA',
                        },
                        destinatario: {
                            cnpj: '77521375000121',
                            nome: 'ELCO ENGENHARIA DE MONTAGENS LTDA',
                        },
                        produtos: [
                            {
                                numeroItem: '1',
                                unidade: 'PC',
                                ncm: '73089010',
                                valorUnitario: '6.9700000000',
                                quantidade: '50.0000',
                            },
                        ],
                        total: {
                            vDesc: '0.00',
                            vNF: '1912.50',
                        },
                        informacoesAdicionais: {
                            infCpl: 'PEDIDO N. 56524 - ENTREGA ELCO CIC.',
                            obsCont: [
                                { $: { xCampo: 'Condicao Pgto:' }, xTexto: '028-28 DIAS' },
                            ],
                        },
                    },
                },
                numeroNota: {
                    type: 'string',
                    description: 'Número da nota fiscal',
                    example: '9199',
                },
                clienteExternalCode: {
                    type: 'string',
                    description: 'Código externo do cliente (opcional)',
                    example: '40514',
                },
            },
            required: ['pedido', 'notaFiscal', 'numeroNota'],
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Comparação realizada com sucesso',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                errors: {
                    type: 'array',
                    items: { type: 'string' },
                    example: [
                        'NF Emitida Incoerente! Número do pedido não encontrado ou divergente. Esperado: 40514, Encontrado: 56524',
                        'NF Emitida Incoerente! CNPJ do fornecedor divergente. Pedido: 16891049000111, NF: 35629600000106',
                    ],
                },
            },
        },
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], VnfController.prototype, "compararPedidoNota", null);
__decorate([
    (0, common_1.Post)('enviar-email-divergencia'),
    (0, swagger_1.ApiOperation)({
        summary: 'Enviar email de divergência',
        description: 'Envia email de divergência para o fornecedor de um erro específico.',
    }),
    (0, swagger_1.ApiBody)({
        description: 'ID do erro para envio de email',
        schema: {
            type: 'object',
            properties: {
                erroId: {
                    type: 'number',
                    example: 1,
                    description: 'ID do erro de VNF',
                },
            },
            required: ['erroId'],
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Email enviado com sucesso',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: {
                    type: 'string',
                    example: 'Email enviado com <NAME_EMAIL>',
                },
            },
        },
    }),
    __param(0, (0, common_1.Body)('erroId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], VnfController.prototype, "enviarEmailDivergencia", null);
__decorate([
    (0, common_1.Post)('enviar-emails-lote'),
    (0, swagger_1.ApiOperation)({
        summary: 'Enviar emails em lote',
        description: 'Envia emails de divergência para múltiplos fornecedores em lote, incluindo emails adicionais.',
    }),
    (0, swagger_1.ApiBody)({
        description: 'IDs dos erros e emails adicionais para envio em lote',
        schema: {
            type: 'object',
            properties: {
                erroIds: {
                    type: 'array',
                    items: { type: 'number' },
                    example: [1, 2, 3],
                    description: 'Array com IDs dos erros de VNF',
                },
                emailsAdicionais: {
                    type: 'array',
                    items: { type: 'string' },
                    example: ['<EMAIL>', '<EMAIL>'],
                    description: 'Array com emails adicionais que receberão as notificações',
                },
            },
            required: ['erroIds'],
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Emails processados em lote',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                results: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            id: { type: 'number', example: 1 },
                            success: { type: 'boolean', example: true },
                            message: { type: 'string', example: 'Email enviado com sucesso' },
                        },
                    },
                },
            },
        },
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], VnfController.prototype, "enviarEmailsLote", null);
__decorate([
    (0, common_1.Get)('testar-email'),
    (0, swagger_1.ApiOperation)({
        summary: 'Testar conexão SMTP',
        description: 'Testa a conexão com o servidor SMTP configurado.',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Teste de conexão realizado',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: {
                    type: 'string',
                    example: 'Conexão SMTP configurada corretamente',
                },
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], VnfController.prototype, "testarEmail", null);
__decorate([
    (0, common_1.Get)('configuracao-email'),
    (0, swagger_1.ApiOperation)({
        summary: 'Verificar configuração de email',
        description: 'Verifica as configurações de email atuais (sem mostrar senhas).',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Configuração de email',
        schema: {
            type: 'object',
            properties: {
                host: { type: 'string' },
                port: { type: 'number' },
                secure: { type: 'boolean' },
                user: { type: 'string' },
                fromName: { type: 'string' },
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], VnfController.prototype, "verificarConfiguracaoEmail", null);
__decorate([
    (0, common_1.Post)('testar-envio-email'),
    (0, swagger_1.ApiOperation)({
        summary: 'Testar envio de email',
        description: 'Envia um email de teste para verificar se o sistema está funcionando.',
    }),
    (0, swagger_1.ApiBody)({
        description: 'Email de destino para teste',
        schema: {
            type: 'object',
            properties: {
                email: {
                    type: 'string',
                    example: '<EMAIL>',
                    description: 'Email para onde enviar o teste',
                },
            },
            required: ['email'],
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Email de teste enviado',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: {
                    type: 'string',
                    example: 'Email de teste enviado com sucesso',
                },
            },
        },
    }),
    __param(0, (0, common_1.Body)('email')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], VnfController.prototype, "testarEnvioEmail", null);
__decorate([
    (0, common_1.Post)('aceitar-erro'),
    (0, swagger_1.ApiOperation)({
        summary: 'Aceitar erro e enviar email',
        description: 'Aceita um erro específico e envia automaticamente o email de divergência para o fornecedor.',
    }),
    (0, swagger_1.ApiBody)({
        description: 'ID do erro para aceitar',
        schema: {
            type: 'object',
            properties: {
                erroId: {
                    type: 'number',
                    example: 1,
                    description: 'ID do erro a ser aceito',
                },
            },
            required: ['erroId'],
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Erro aceito e email enviado',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: {
                    type: 'string',
                    example: 'Email enviado com <NAME_EMAIL>',
                },
            },
        },
    }),
    __param(0, (0, common_1.Body)('erroId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], VnfController.prototype, "aceitarErro", null);
__decorate([
    (0, common_1.Post)('recusar-erro'),
    (0, swagger_1.ApiOperation)({
        summary: 'Recusar erro de VNF',
        description: 'Recusa um erro de VNF (soft delete).',
    }),
    (0, swagger_1.ApiBody)({
        description: 'ID do erro a ser recusado',
        schema: {
            type: 'object',
            properties: {
                erroId: {
                    type: 'number',
                    example: 1,
                    description: 'ID do erro a ser recusado',
                },
            },
            required: ['erroId'],
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Erro recusado com sucesso',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: 'Erro recusado com sucesso' },
            },
        },
    }),
    __param(0, (0, common_1.Body)('erroId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], VnfController.prototype, "recusarErro", null);
__decorate([
    (0, common_1.Post)('salvar-email-fornecedor'),
    (0, swagger_1.ApiOperation)({
        summary: 'Salvar/atualizar email de fornecedor',
        description: 'Salva ou atualiza o email de um fornecedor na tabela supplier_email.',
    }),
    (0, swagger_1.ApiBody)({
        description: 'Dados do fornecedor e email',
        schema: {
            type: 'object',
            properties: {
                nomeFornecedor: {
                    type: 'string',
                    example: 'FORNECEDOR ABC LTDA',
                    description: 'Nome do fornecedor',
                },
                email: {
                    type: 'string',
                    example: '<EMAIL>',
                    description: 'Email do fornecedor',
                },
            },
            required: ['nomeFornecedor', 'email'],
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Email salvo/atualizado com sucesso',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: {
                    type: 'string',
                    example: 'Email do fornecedor salvo com sucesso',
                },
                fornecedor: {
                    type: 'object',
                    properties: {
                        id: { type: 'number', example: 1 },
                        supplierName: { type: 'string', example: 'FORNECEDOR ABC LTDA' },
                        email: { type: 'string', example: '<EMAIL>' },
                    },
                },
            },
        },
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], VnfController.prototype, "salvarEmailFornecedor", null);
__decorate([
    (0, common_1.Post)('processar-lote-por-data'),
    (0, swagger_1.ApiOperation)({
        summary: 'Processar lote de XMLs por data',
        description: 'Busca chaves de acesso XML no Oracle para uma data específica e processa cada uma automaticamente',
    }),
    (0, swagger_1.ApiBody)({ type: processar_lote_por_data_dto_1.ProcessarLotePorDataDto }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Processamento em lote concluído',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: {
                    type: 'string',
                    example: 'Processamento concluído: 15 sucessos, 2 erros de 17 chaves processadas',
                },
                totalProcessados: { type: 'number', example: 17 },
                sucessos: { type: 'number', example: 15 },
                erros: { type: 'number', example: 2 },
                detalhes: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            chaveAcesso: {
                                type: 'string',
                                example: '35200714200166000187550010000000007907734918',
                            },
                            numeroNota: { type: 'string', example: '000000007' },
                            status: {
                                type: 'string',
                                enum: ['sucesso', 'erro'],
                                example: 'sucesso',
                            },
                            message: { type: 'string', example: 'Processado com sucesso' },
                            temErros: { type: 'boolean', example: false },
                            quantidadeErros: { type: 'number', example: 0 },
                        },
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Data inválida' }),
    (0, swagger_1.ApiResponse)({ status: 500, description: 'Erro interno do servidor' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [processar_lote_por_data_dto_1.ProcessarLotePorDataDto]),
    __metadata("design:returntype", Promise)
], VnfController.prototype, "processarLotePorData", null);
__decorate([
    (0, common_1.Get)('consultas-processadas'),
    (0, swagger_1.ApiOperation)({
        summary: 'Listar consultas processadas',
        description: 'Lista todas as chaves de acesso XML que já foram consultadas no SEFAZ',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'data',
        required: false,
        description: 'Filtrar por data no formato DD/MM/YYYY',
        type: 'string',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de consultas processadas',
        schema: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    id: { type: 'number', example: 1 },
                    chaveAcesso: {
                        type: 'string',
                        example: '35200714200166000187550010000000007907734918',
                    },
                    numeroNota: { type: 'string', example: '000000007' },
                    nomeFornecedor: {
                        type: 'string',
                        example: 'EMPRESA FORNECEDORA LTDA',
                    },
                    numeroPedido: { type: 'string', example: '12345' },
                    statusConsulta: {
                        type: 'string',
                        enum: ['sucesso', 'erro', 'limite_atingido', 'erro_sefaz'],
                        example: 'sucesso',
                    },
                    temDivergencias: { type: 'boolean', example: false },
                    observacoes: { type: 'string', example: 'Processado com sucesso' },
                    dataProcessamento: { type: 'string', format: 'date-time' },
                    dataDocumento: { type: 'string', format: 'date' },
                },
            },
        },
    }),
    __param(0, (0, common_1.Query)('data')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], VnfController.prototype, "listarConsultasProcessadas", null);
__decorate([
    (0, common_1.Get)('status-processamento-automatico'),
    (0, swagger_1.ApiOperation)({
        summary: 'Status do processamento automático',
        description: 'Retorna informações sobre o cron job de processamento automático de XMLs',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Status do processamento automático',
        schema: {
            type: 'object',
            properties: {
                isProcessing: {
                    type: 'boolean',
                    example: false,
                    description: 'Se está processando no momento',
                },
                lastExecution: {
                    type: 'string',
                    format: 'date-time',
                    nullable: true,
                    description: 'Última execução do cron',
                },
                nextExecution: {
                    type: 'string',
                    format: 'date-time',
                    description: 'Próxima execução agendada',
                },
                cronExpression: {
                    type: 'string',
                    example: '0 */15 * * * *',
                    description: 'Expressão cron (a cada 15 minutos)',
                },
                description: {
                    type: 'string',
                    example: 'Processamento automático executado a cada 15 minutos',
                    description: 'Descrição do agendamento',
                },
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], VnfController.prototype, "obterStatusProcessamentoAutomatico", null);
exports.VnfController = VnfController = __decorate([
    (0, swagger_1.ApiTags)('VNF'),
    (0, common_1.Controller)('vnf'),
    __metadata("design:paramtypes", [vnf_service_1.VnfService])
], VnfController);
//# sourceMappingURL=vnf.controller.js.map