const axios = require('axios');

async function testarEnvioLote() {
  try {
    console.log('Testando envio de emails em lote...');
    
    const response = await axios.post('http://localhost:3000/vnf/enviar-emails-lote', {
      erroIds: [51], // ID real de erro
      emailsAdicionais: ['<EMAIL>', '<EMAIL>', '<EMAIL>']
    });
    
    console.log('Resposta do servidor:', response.data);
    
  } catch (error) {
    console.error('Erro ao testar:', error.response?.data || error.message);
  }
}

testarEnvioLote(); 