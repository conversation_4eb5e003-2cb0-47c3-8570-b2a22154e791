"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TransporterController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const transporter_service_1 = require("./transporter.service");
const create_transporter_dto_1 = require("./dto/create-transporter.dto");
const update_transporter_dto_1 = require("./dto/update-transporter.dto");
let TransporterController = class TransporterController {
    transporterService;
    constructor(transporterService) {
        this.transporterService = transporterService;
    }
    findAll() {
        return this.transporterService.findAll();
    }
    async findOne(id) {
        return this.transporterService.findOne(id);
    }
    create(dto) {
        return this.transporterService.create(dto);
    }
    async update(id, dto) {
        return this.transporterService.update(id, dto);
    }
    async delete(id) {
        const deleted = await this.transporterService.delete(id);
        if (!deleted)
            throw new common_1.NotFoundException('Transportador não encontrado');
        return { message: 'Transportador excluído com sucesso' };
    }
};
exports.TransporterController = TransporterController;
__decorate([
    (0, common_1.Get)('/list'),
    (0, swagger_1.ApiOperation)({
        summary: 'Listar todas as transportadoras',
        description: 'Retorna uma lista com todas as empresas transportadoras cadastradas.',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de transportadoras',
        schema: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    id: { type: 'string', example: 'uuid-string' },
                    name: { type: 'string', example: 'Transportadora ABC Ltda' },
                    document: { type: 'string', example: '12.345.678/0001-90' },
                    phone: { type: 'string', example: '(11) 3333-4444' },
                    email: { type: 'string', example: '<EMAIL>' },
                    address: { type: 'string', example: 'Rua das Empresas, 456' },
                    isActive: { type: 'boolean', example: true },
                },
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], TransporterController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('/view/:id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Obter transportadora por ID',
        description: 'Retorna os detalhes completos de uma transportadora específica.',
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'ID da transportadora', type: 'string' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Dados da transportadora',
        schema: {
            type: 'object',
            properties: {
                id: { type: 'string', example: 'uuid-string' },
                name: { type: 'string', example: 'Transportadora ABC Ltda' },
                document: { type: 'string', example: '12.345.678/0001-90' },
                phone: { type: 'string', example: '(11) 3333-4444' },
                email: { type: 'string', example: '<EMAIL>' },
                address: { type: 'string', example: 'Rua das Empresas, 456' },
                isActive: { type: 'boolean', example: true },
                createdAt: { type: 'string', format: 'date-time' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Transportadora não encontrada' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], TransporterController.prototype, "findOne", null);
__decorate([
    (0, common_1.Post)('/create'),
    (0, swagger_1.ApiOperation)({
        summary: 'Criar nova transportadora',
        description: 'Cadastra uma nova empresa transportadora no sistema.',
    }),
    (0, swagger_1.ApiBody)({
        type: create_transporter_dto_1.CreateTransporterDto,
        description: 'Dados da nova transportadora',
        examples: {
            create: {
                summary: 'Exemplo de transportadora',
                value: {
                    name: 'Transportadora XYZ Ltda',
                    document: '98.765.432/0001-10',
                    phone: '(11) 5555-6666',
                    email: '<EMAIL>',
                    address: 'Av. Principal, 789',
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Transportadora criada com sucesso',
        schema: {
            type: 'object',
            properties: {
                id: { type: 'string', example: 'uuid-string' },
                name: { type: 'string', example: 'Transportadora XYZ Ltda' },
                document: { type: 'string', example: '98.765.432/0001-10' },
                phone: { type: 'string', example: '(11) 5555-6666' },
                email: { type: 'string', example: '<EMAIL>' },
                address: { type: 'string', example: 'Av. Principal, 789' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dados inválidos' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_transporter_dto_1.CreateTransporterDto]),
    __metadata("design:returntype", void 0)
], TransporterController.prototype, "create", null);
__decorate([
    (0, common_1.Put)('/update/:id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Atualizar transportadora',
        description: 'Atualiza os dados de uma transportadora existente.',
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'ID da transportadora', type: 'string' }),
    (0, swagger_1.ApiBody)({ type: update_transporter_dto_1.UpdateTransporterDto }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Transportadora atualizada com sucesso',
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Transportadora não encontrada' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dados inválidos' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_transporter_dto_1.UpdateTransporterDto]),
    __metadata("design:returntype", Promise)
], TransporterController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)('/delete/:id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Deletar transportadora',
        description: 'Remove uma transportadora do sistema.',
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'ID da transportadora', type: 'string' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Transportadora excluída com sucesso',
        schema: {
            type: 'object',
            properties: {
                message: {
                    type: 'string',
                    example: 'Transportador excluído com sucesso',
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Transportadora não encontrada' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], TransporterController.prototype, "delete", null);
exports.TransporterController = TransporterController = __decorate([
    (0, swagger_1.ApiTags)('Transportadoras'),
    (0, common_1.Controller)('transporters'),
    __metadata("design:paramtypes", [transporter_service_1.TransporterService])
], TransporterController);
//# sourceMappingURL=transporter.controller.js.map