"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RomaneioOrder = void 0;
const typeorm_1 = require("typeorm");
let RomaneioOrder = class RomaneioOrder {
    id;
    romaneioId;
    separationOrderId;
    volume;
    numberPackaging;
    length;
    width;
    height;
    netWeight;
    createdAt;
    romaneio;
};
exports.RomaneioOrder = RomaneioOrder;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], RomaneioOrder.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'romaneio_id', type: 'integer', nullable: false }),
    __metadata("design:type", Number)
], RomaneioOrder.prototype, "romaneioId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'separation_order_id', type: 'integer', nullable: true }),
    __metadata("design:type", Object)
], RomaneioOrder.prototype, "separationOrderId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'integer', nullable: true }),
    __metadata("design:type", Number)
], RomaneioOrder.prototype, "volume", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'number_packaging', type: 'integer', nullable: true }),
    __metadata("design:type", Number)
], RomaneioOrder.prototype, "numberPackaging", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'integer', nullable: true }),
    __metadata("design:type", Number)
], RomaneioOrder.prototype, "length", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'integer', nullable: true }),
    __metadata("design:type", Number)
], RomaneioOrder.prototype, "width", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'integer', nullable: true }),
    __metadata("design:type", Number)
], RomaneioOrder.prototype, "height", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'net_weight', type: 'integer', nullable: true }),
    __metadata("design:type", Number)
], RomaneioOrder.prototype, "netWeight", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'created_at',
        type: 'timestamp',
        nullable: false,
        default: () => 'CURRENT_TIMESTAMP',
    }),
    __metadata("design:type", Date)
], RomaneioOrder.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)('Romaneio', (romaneio) => romaneio.romaneioOrders),
    (0, typeorm_1.JoinColumn)({ name: 'romaneio_id' }),
    __metadata("design:type", Object)
], RomaneioOrder.prototype, "romaneio", void 0);
exports.RomaneioOrder = RomaneioOrder = __decorate([
    (0, typeorm_1.Entity)()
], RomaneioOrder);
//# sourceMappingURL=romaneio-order.entity.js.map