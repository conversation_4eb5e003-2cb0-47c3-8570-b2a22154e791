// src/separation-orders/separation-order.controller.ts
import { BadRequestException, Body, Controller, Get, Param, Patch, Post, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBody, ApiParam, ApiQuery } from '@nestjs/swagger';
import { SeparationOrderService } from './separation-orders.service';

@ApiTags('Pedidos de Separação')
@Controller('separation-orders')
export class SeparationOrderController {
    constructor(private readonly service: SeparationOrderService) { }

    @Get()
    @ApiOperation({
        summary: 'Listar todos os pedidos de separação',
        description: 'Retorna uma lista paginada com todos os pedidos de separação cadastrados no sistema.'
    })
    @ApiQuery({
        name: 'page',
        required: false,
        type: Number,
        description: 'Número da página (padrão: 1)',
        example: 1
    })
    @ApiQuery({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Quantidade de itens por página (padrão: 10)',
        example: 10
    })
    @ApiQuery({
        name: 'search',
        required: false,
        type: String,
        description: 'Termo de busca (código interno, externo ou depositante)',
        example: 'SEP001'
    })
    @ApiQuery({
        name: 'status',
        required: false,
        type: String,
        description: 'Filtrar por status',
        example: 'PENDING'
    })
    @ApiQuery({
        name: 'depositorName',
        required: false,
        type: String,
        description: 'Filtrar por depositante',
        example: 'Depositante Exemplo'
    })
    @ApiQuery({
        name: 'clientFilter',
        required: false,
        type: String,
        description: 'Filtrar por cliente (código externo)',
        example: 'CLI001'
    })
    @ApiResponse({
        status: 200,
        description: 'Lista paginada de pedidos de separação',
        schema: {
            type: 'object',
            properties: {
                orders: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            id: { type: 'number', example: 1 },
                            internalCode: { type: 'string', example: 'SEP001' },
                            externalCode: { type: 'string', example: 'EXT001' },
                            status: {
                                type: 'string',
                                enum: ['PENDING', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED'],
                                example: 'PENDING'
                            },
                            priority: { type: 'string', example: 'HIGH' },
                            type: {
                                type: 'string',
                                enum: ['MATRIZ', 'FILIAL'],
                                example: 'MATRIZ'
                            },
                            createdAt: { type: 'string', format: 'date-time' },
                            products: {
                                type: 'array',
                                items: {
                                    type: 'object',
                                    properties: {
                                        productCode: { type: 'string', example: 'PROD001' },
                                        quantity: { type: 'number', example: 10 },
                                        separated: { type: 'number', example: 8 }
                                    }
                                }
                            }
                        }
                    }
                },
                total: { type: 'number', example: 100, description: 'Total de registros' },
                page: { type: 'number', example: 1, description: 'Página atual' },
                limit: { type: 'number', example: 10, description: 'Itens por página' },
                totalPages: { type: 'number', example: 10, description: 'Total de páginas' }
            }
        }
    })
    getAll(
        @Query('page') page = 1,
        @Query('limit') limit = 10,
        @Query('search') search?: string,
        @Query('status') status?: string,
        @Query('depositorName') depositorName?: string,
        @Query('clientFilter') clientFilter?: string,
    ) {
        const filters = { search, status, depositorName, clientFilter };
        return this.service.findAll(page, limit, filters);
    }

    @Get('stats')
    @ApiOperation({
        summary: 'Obter estatísticas gerais dos pedidos',
        description: 'Retorna estatísticas gerais de todos os pedidos de separação.'
    })
    @ApiResponse({
        status: 200,
        description: 'Estatísticas gerais dos pedidos',
        schema: {
            type: 'object',
            properties: {
                totalOrders: { type: 'number', example: 150 },
                completedCount: { type: 'number', example: 45 },
                linkedCount: { type: 'number', example: 60 },
                pendingCount: { type: 'number', example: 40 },
                errorCount: { type: 'number', example: 5 },
                completedPercentage: { type: 'number', example: 30 },
                linkedPercentage: { type: 'number', example: 40 },
                pendingPercentage: { type: 'number', example: 27 }
            }
        }
    })
    getStats() {
        return this.service.getStats();
    }

    @Get('filter-options')
    @ApiOperation({
        summary: 'Obter opções para filtros',
        description: 'Retorna listas de valores únicos para uso em filtros.'
    })
    @ApiResponse({
        status: 200,
        description: 'Opções disponíveis para filtros',
        schema: {
            type: 'object',
            properties: {
                statuses: { 
                    type: 'array', 
                    items: { type: 'string' },
                    example: ['PENDING', 'LINKED', 'ROMANIO']
                },
                depositors: { 
                    type: 'array', 
                    items: { type: 'string' },
                    example: ['Depositante A', 'Depositante B']
                },
                clients: { 
                    type: 'array', 
                    items: { type: 'string' },
                    example: ['CLI001 - Cliente A', 'CLI002 - Cliente B']
                }
            }
        }
    })
    getFilterOptions() {
        return this.service.getFilterOptions();
    }

    @Get('external-products/:internalCode')
    @ApiOperation({
        summary: 'Buscar produtos externos por código interno',
        description: 'Busca informações de produtos em sistemas externos através do código interno do pedido.'
    })
    @ApiParam({
        name: 'internalCode',
        description: 'Código interno do pedido de separação',
        type: 'string',
        example: 'SEP001'
    })
    @ApiResponse({
        status: 200,
        description: 'Detalhes dos produtos encontrados',
        schema: {
            type: 'object',
            properties: {
                internalCode: { type: 'string', example: 'SEP001' },
                found: { type: 'boolean', example: true },
                products: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            code: { type: 'string', example: 'PROD001' },
                            name: { type: 'string', example: 'Produto Exemplo' },
                            description: { type: 'string', example: 'Descrição do produto' },
                            stock: { type: 'number', example: 100 },
                            location: { type: 'string', example: 'A-01-15' },
                            weight: { type: 'number', example: 1.5 }
                        }
                    }
                },
                lastUpdate: { type: 'string', format: 'date-time' }
            }
        }
    })
    @ApiResponse({ status: 404, description: 'Pedido não encontrado ou sem produtos' })
    async fetchExternalProducts(@Param('internalCode') internalCode: string) {
        return this.service.getProductDetails(internalCode);
    }

    @Patch('vincular/:internalCode')
    @ApiOperation({
        summary: 'Vincular pedido de separação',
        description: 'Vincula um pedido de separação a um tipo específico (MATRIZ ou FILIAL).'
    })
    @ApiParam({
        name: 'internalCode',
        description: 'Código interno do pedido de separação',
        type: 'string',
        example: 'SEP001'
    })
    @ApiBody({
        description: 'Tipo de vinculação',
        schema: {
            type: 'object',
            properties: {
                tipo: { 
                    type: 'string', 
                    enum: ['MATRIZ', 'FILIAL'],
                    example: 'MATRIZ'
                }
            },
            required: ['tipo']
        }
    })
    @ApiResponse({
        status: 200,
        description: 'Pedido vinculado com sucesso',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                internalCode: { type: 'string', example: 'SEP001' },
                linkedTo: { type: 'string', example: 'MATRIZ' },
                message: { type: 'string', example: 'Pedido vinculado com sucesso' },
                updatedAt: { type: 'string', format: 'date-time' }
            }
        }
    })
    @ApiResponse({ status: 404, description: 'Pedido não encontrado' })
    @ApiResponse({ status: 400, description: 'Tipo inválido ou pedido já vinculado' })
    async linkOrder(@Param('internalCode') internalCode: string, @Body() body: { tipo: 'MATRIZ' | 'FILIAL' }) {
        return this.service.linkOrder(internalCode, body.tipo);
    }

    @Patch('/order-volume')
    @ApiOperation({
        summary: 'Sincronizar volume do pedido',
        description: 'Atualiza o volume/quantidade de um pedido específico.'
    })
    @ApiBody({
        description: 'Dados para sincronização de volume',
        schema: {
            type: 'object',
            properties: {
                orderId: { type: 'number', example: 123 },
                volume: { type: 'number', example: 15.5 }
            },
            required: ['orderId', 'volume']
        }
    })
    @ApiResponse({
        status: 200,
        description: 'Volume sincronizado com sucesso',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                orderId: { type: 'number', example: 123 },
                previousVolume: { type: 'number', example: 10.0 },
                newVolume: { type: 'number', example: 15.5 },
                message: { type: 'string', example: 'Volume atualizado com sucesso' },
                updatedAt: { type: 'string', format: 'date-time' }
            }
        }
    })
    @ApiResponse({ status: 404, description: 'Pedido não encontrado' })
    @ApiResponse({ status: 400, description: 'Volume inválido' })
    async syncOrderVolume(@Body() body: { orderId: number, volume: number }) {
        return this.service.linkOrderVolume(body.orderId, body.volume);
    }
}
