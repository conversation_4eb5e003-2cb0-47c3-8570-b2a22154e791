"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createNotaFiscalFixture = exports.createPedidoPortalFixture = void 0;
const createPedidoPortalFixture = (overrides = {}) => ({
    cliente: 'ELCO',
    pedidoERP: '40514',
    pedidoEproc: 40514,
    cnpjFornecedor: '16891049000111',
    cnpjPlanta: '77521375000121',
    nomeFornecedor: '8.8 PARAFUSOS E TINTAS',
    condicaoPagamento: '28D',
    formaPagamento: 'BOLETO',
    codigoPlanta: 'PLANTA001',
    nomePlanta: 'PLANTA TESTE',
    codigoFornecedor: 'FORN001',
    tipoPedidoERP: 'COMPRA',
    codigoTransportadora: 'TRANS001',
    nomeTransportadora: 'TRANSPORTADORA TESTE',
    codigoFrete: 'FRETE001',
    valorFrete: 0.0,
    dataDeCriacao: '2024-01-01T00:00:00Z',
    dataDeEmissao: '2024-01-01T00:00:00Z',
    loginComprador: '<EMAIL>',
    situacaoPedido: 'ATIVO',
    enderecoEntrega: 'Rua Teste, 123',
    enderecoEntregaNumero: '123',
    enderecoEntregaComplemento: '',
    enderecoEntregaCep: '01234-567',
    enderecoEntregaCidade: 'São Paulo',
    enderecoEntregaBairro: 'Centro',
    enderecoEntregaEstado: 'SP',
    enderecoEntregaPais: 'BRASIL',
    codigoMoeda: 'BRL',
    valorTotalComFrete: 10.0,
    tipoDeCompra: 'COMPRA',
    observacoes: [],
    itens: [],
    tags: [],
    sDsMensagemRetorno: null,
    ...overrides,
});
exports.createPedidoPortalFixture = createPedidoPortalFixture;
const createNotaFiscalFixture = (overrides = {}) => ({
    versao: '4.00',
    numeroNota: '9199',
    dataEmissao: '2024-01-16T00:00:00Z',
    naturezaOperacao: 'VENDA',
    modelo: '55',
    serie: '1',
    emitente: {
        cnpj: '16891049000111',
        nome: '8.8 PARAFUSOS E TINTAS',
        endereco: {
            xLgr: 'Rua Teste',
            nro: '123',
            xBairro: 'Centro',
            cMun: '1234567',
            xMun: 'São Paulo',
            UF: 'SP',
            CEP: '01234-567',
            cPais: '1058',
            xPais: 'BRASIL',
        },
        inscricaoEstadual: '123456789',
        regimeTributario: '1',
    },
    destinatario: {
        cnpj: '77521375000121',
        nome: 'ELCO ENGENHARIA DE MONTAGENS LTDA',
        endereco: {
            xLgr: 'Rua Teste',
            nro: '123',
            xBairro: 'Centro',
            cMun: '1234567',
            xMun: 'São Paulo',
            UF: 'SP',
            CEP: '01234-567',
            cPais: '1058',
            xPais: 'BRASIL',
        },
        inscricaoEstadual: '123456789',
        indicadorIE: '1',
    },
    produtos: [],
    total: {
        vBC: '10.00',
        vICMS: '1.80',
        vICMSDeson: '0.00',
        vFCPUFDest: '0.00',
        vICMSUFDest: '0.00',
        vICMSUFRemet: '0.00',
        vFCP: '0.00',
        vBCST: '0.00',
        vST: '0.00',
        vFCPST: '0.00',
        vFCPSTRet: '0.00',
        qBCMono: '0.00',
        vICMSMono: '0.00',
        qBCMonoReten: '0.00',
        vICMSMonoReten: '0.00',
        qBCMonoRet: '0.00',
        vICMSMonoRet: '0.00',
        vProd: '10.00',
        vFrete: '0.00',
        vSeg: '0.00',
        vDesc: '0.00',
        vII: '0.00',
        vIPI: '0.00',
        vIPIDevol: '0.00',
        vPIS: '0.00',
        vCOFINS: '0.00',
        vOutro: '0.00',
        vNF: '10.00',
        vTotTrib: '0.00',
    },
    transporte: {
        modFrete: '1',
    },
    pagamento: {
        detPag: {
            indPag: '0',
            tPag: '01',
            vPag: '10.00',
        },
    },
    informacoesAdicionais: {
        infCpl: 'PEDIDO N. 40514 - ENTREGA ELCO CIC.',
    },
    protocolo: {
        $: { Id: 'ID001' },
        tpAmb: '2',
        verAplic: '1.0',
        chNFe: '12345678901234567890123456789012345678901234',
        dhRecbto: '2024-01-16T10:00:00Z',
        nProt: '123456789012345',
        digVal: 'ABCDEF',
        cStat: '100',
        xMotivo: 'Autorizado o uso da NF-e',
    },
    ...overrides,
});
exports.createNotaFiscalFixture = createNotaFiscalFixture;
//# sourceMappingURL=vnf-test-fixtures.js.map