import { Injectable, Inject } from '@nestjs/common';

@Injectable()
export class OracleService {
  constructor(
    @Inject('OracleConnectionFactory')
    private readonly oracleConnectionFactory: () => Promise<any>,
  ) {}

  async executeQuery(query: string, params: any[] = []): Promise<any> {
    const connection = await this.oracleConnectionFactory();
    try {
      const result = await connection.execute(query, params);
      return result.rows;
    } catch (error) {
      throw new Error(`Erro ao executar query Oracle: ${error.message}`);
    } finally {
      await connection.close();
    }
  }
}
