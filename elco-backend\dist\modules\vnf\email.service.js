"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmailService = void 0;
const common_1 = require("@nestjs/common");
const nodemailer = require("nodemailer");
let EmailService = class EmailService {
    transporter;
    constructor() {
        this.initializeTransporter();
    }
    initializeTransporter() {
        const smtpConfig = {
            host: process.env.SMTP_HOST || 'smtp.gmail.com',
            port: parseInt(process.env.SMTP_PORT || '587'),
            secure: process.env.SMTP_SECURE === 'true',
            auth: {
                user: process.env.SMTP_USER,
                pass: process.env.SMTP_PASS,
            },
            tls: {
                rejectUnauthorized: false,
            },
        };
        this.transporter = nodemailer.createTransport(smtpConfig);
    }
    async enviarEmailDivergencia(destinatario, numeroNota, conteudo, nomeFornecedor) {
        try {
            const mailOptions = {
                from: {
                    name: process.env.SMTP_FROM_NAME ||
                        'Departamento de Compras - Elco Engenharia',
                    address: process.env.SMTP_USER || '<EMAIL>',
                },
                to: destinatario,
                subject: nomeFornecedor
                    ? `Divergência Identificada - ${nomeFornecedor} - NF nº ${numeroNota}`
                    : `Divergência Identificada - Sujeita à Recusa da NF nº ${numeroNota}`,
                text: conteudo,
                html: this.converterParaHtml(conteudo),
            };
            const info = await this.transporter.sendMail(mailOptions);
            console.log(`[EmailService] E-mail enviado com sucesso para ${destinatario}`);
            console.log(`[EmailService] Message ID: ${info.messageId}`);
            return {
                success: true,
                message: `E-mail enviado com sucesso para ${destinatario}`,
            };
        }
        catch (error) {
            console.error('[EmailService] Erro ao enviar e-mail:', error);
            return {
                success: false,
                message: `Erro ao enviar e-mail: ${error.message}`,
            };
        }
    }
    converterParaHtml(texto) {
        return texto
            .replace(/\n\n/g, '</p><p>')
            .replace(/\n/g, '<br>')
            .replace(/•/g, '&bull;')
            .replace(/Prezado Fornecedor,/, '<strong>Prezado Fornecedor,</strong>')
            .replace(/Atenciosamente,/, '<strong>Atenciosamente,</strong>')
            .replace(/Departamento de Compras/, '<strong>Departamento de Compras</strong>')
            .replace(/Elco Engenharia/, '<strong>Elco Engenharia</strong>')
            .replace(/Importante:/, '<strong>Importante:</strong>')
            .replace(/danfe@elco\.com\.br/g, '<a href="mailto:<EMAIL>"><EMAIL></a>')
            .replace(/24 horas/g, '<strong>24 horas</strong>')
            .replace(/<p>/, '<p style="margin: 10px 0; line-height: 1.6;">')
            .replace(/<\/p>$/, '</p>')
            .replace(/^/, '<div style="font-family: Arial, sans-serif; font-size: 14px; line-height: 1.6; color: #333;">')
            .replace(/$/, '</div>');
    }
    async testarConexao() {
        try {
            await this.transporter.verify();
            return {
                success: true,
                message: 'Conexão SMTP configurada corretamente',
            };
        }
        catch (error) {
            console.error('[EmailService] Erro na conexão SMTP:', error);
            return {
                success: false,
                message: `Erro na conexão SMTP: ${error.message}`,
            };
        }
    }
};
exports.EmailService = EmailService;
exports.EmailService = EmailService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], EmailService);
//# sourceMappingURL=email.service.js.map