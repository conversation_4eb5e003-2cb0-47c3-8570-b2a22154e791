import {
  Column,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>um<PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';

@Entity()
export class RomaneioOrder {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'romaneio_id', type: 'integer', nullable: false })
  romaneioId: number;

  @Column({ name: 'separation_order_id', type: 'integer', nullable: true })
  separationOrderId: number | null;

  @Column({ type: 'integer', nullable: true })
  volume: number;

  @Column({ name: 'number_packaging', type: 'integer', nullable: true })
  numberPackaging: number;

  @Column({ type: 'integer', nullable: true })
  length: number;

  @Column({ type: 'integer', nullable: true })
  width: number;

  @Column({ type: 'integer', nullable: true })
  height: number;

  @Column({ name: 'net_weight', type: 'integer', nullable: true })
  netWeight: number;

  @Column({
    name: 'created_at',
    type: 'timestamp',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
  })
  createdAt: Date;

  @ManyToOne('Romaneio', (romaneio: any) => romaneio.romaneioOrders)
  @JoinColumn({ name: 'romaneio_id' })
  romaneio: any;
}
