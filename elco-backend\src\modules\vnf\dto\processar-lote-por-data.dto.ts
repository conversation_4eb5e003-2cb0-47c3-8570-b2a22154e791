import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, Matches } from 'class-validator';

export class ProcessarLotePorDataDto {
  @ApiProperty({
    description: 'Data no formato DD/MM/YYYY para buscar as notas fiscais',
    example: '05/08/2025',
    pattern: '^\\d{2}/\\d{2}/\\d{4}$'
  })
  @IsNotEmpty({ message: 'Data é obrigatória' })
  @IsString({ message: 'Data deve ser uma string' })
  @Matches(/^\d{2}\/\d{2}\/\d{4}$/, { 
    message: 'Data deve estar no formato DD/MM/YYYY' 
  })
  data: string;
} 