"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PackagingController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const packaging_service_1 = require("./packaging.service");
const create_packaging_dto_1 = require("./dto/create-packaging.dto");
const update_packaging_dto_1 = require("./dto/update-packaging.dto");
let PackagingController = class PackagingController {
    packagingService;
    constructor(packagingService) {
        this.packagingService = packagingService;
    }
    async findAll() {
        return this.packagingService.findAll();
    }
    async create(dto) {
        return this.packagingService.create(dto);
    }
    async update(id, dto) {
        const updated = await this.packagingService.update(id, dto);
        if (!updated) {
            throw new common_1.NotFoundException('Embalagem não encontrada');
        }
        return updated;
    }
    async delete(id) {
        const deleted = await this.packagingService.delete(id);
        if (!deleted) {
            throw new common_1.NotFoundException('Embalagem não encontrada');
        }
        return { message: 'Embalagem excluída com sucesso' };
    }
    async findOne(id) {
        const packaging = await this.packagingService.findOne(id);
        if (!packaging) {
            throw new common_1.NotFoundException('Embalagem não encontrada');
        }
        return packaging;
    }
};
exports.PackagingController = PackagingController;
__decorate([
    (0, common_1.Get)('/list'),
    (0, swagger_1.ApiOperation)({
        summary: 'Listar todas as embalagens',
        description: 'Retorna uma lista com todas as embalagens cadastradas no sistema.',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de embalagens',
        schema: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    id: { type: 'string', example: 'uuid-string' },
                    name: { type: 'string', example: 'Caixa Papelão Média' },
                    type: { type: 'string', example: 'BOX' },
                    dimensions: { type: 'string', example: '30x20x15 cm' },
                    weight: { type: 'number', example: 0.5 },
                    capacity: { type: 'number', example: 10 },
                    isActive: { type: 'boolean', example: true },
                },
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], PackagingController.prototype, "findAll", null);
__decorate([
    (0, common_1.Post)('/create'),
    (0, swagger_1.ApiOperation)({
        summary: 'Criar nova embalagem',
        description: 'Cadastra um novo tipo de embalagem no sistema.',
    }),
    (0, swagger_1.ApiBody)({
        type: create_packaging_dto_1.CreatePackagingDto,
        description: 'Dados da nova embalagem',
        examples: {
            create: {
                summary: 'Exemplo de embalagem',
                value: {
                    name: 'Caixa Papelão Grande',
                    type: 'BOX',
                    dimensions: '50x40x30 cm',
                    weight: 1.2,
                    capacity: 25,
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Embalagem criada com sucesso',
        schema: {
            type: 'object',
            properties: {
                id: { type: 'string', example: 'uuid-string' },
                name: { type: 'string', example: 'Caixa Papelão Grande' },
                type: { type: 'string', example: 'BOX' },
                dimensions: { type: 'string', example: '50x40x30 cm' },
                weight: { type: 'number', example: 1.2 },
                capacity: { type: 'number', example: 25 },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dados inválidos' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_packaging_dto_1.CreatePackagingDto]),
    __metadata("design:returntype", Promise)
], PackagingController.prototype, "create", null);
__decorate([
    (0, common_1.Put)('/update/:id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Atualizar embalagem',
        description: 'Atualiza os dados de uma embalagem existente.',
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'ID da embalagem', type: 'string' }),
    (0, swagger_1.ApiBody)({ type: update_packaging_dto_1.UpdatePackagingDto }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Embalagem atualizada com sucesso',
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Embalagem não encontrada' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dados inválidos' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_packaging_dto_1.UpdatePackagingDto]),
    __metadata("design:returntype", Promise)
], PackagingController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)('/delete/:id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Deletar embalagem',
        description: 'Remove uma embalagem do sistema.',
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'ID da embalagem', type: 'string' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Embalagem excluída com sucesso',
        schema: {
            type: 'object',
            properties: {
                message: { type: 'string', example: 'Embalagem excluída com sucesso' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Embalagem não encontrada' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PackagingController.prototype, "delete", null);
__decorate([
    (0, common_1.Get)('/view/:id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Obter embalagem por ID',
        description: 'Retorna os detalhes completos de uma embalagem específica.',
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'ID da embalagem', type: 'string' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Dados da embalagem',
        schema: {
            type: 'object',
            properties: {
                id: { type: 'string', example: 'uuid-string' },
                name: { type: 'string', example: 'Caixa Papelão Média' },
                type: { type: 'string', example: 'BOX' },
                dimensions: { type: 'string', example: '30x20x15 cm' },
                weight: { type: 'number', example: 0.5 },
                capacity: { type: 'number', example: 10 },
                isActive: { type: 'boolean', example: true },
                createdAt: { type: 'string', format: 'date-time' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Embalagem não encontrada' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PackagingController.prototype, "findOne", null);
exports.PackagingController = PackagingController = __decorate([
    (0, swagger_1.ApiTags)('Embalagens'),
    (0, common_1.Controller)('packaging'),
    __metadata("design:paramtypes", [packaging_service_1.PackagingService])
], PackagingController);
//# sourceMappingURL=packaging.controller.js.map