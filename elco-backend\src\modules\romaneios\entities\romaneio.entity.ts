import { Column, Entity, OneToMany, PrimaryGeneratedColumn } from 'typeorm';

@Entity()
export class Romaneio {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    name: 'date_issue',
    type: 'timestamp',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
  })
  dateIssue: Date;

  @Column({ type: 'varchar', length: 500, nullable: false })
  address: string;

  @Column({ name: 'carrier_id', type: 'integer', nullable: false })
  carrierId: number;

  @Column({ name: 'driver_id', type: 'integer', nullable: false })
  driverId: number;

  @Column({ name: 'vehicle_id', type: 'integer', nullable: false })
  vehicleId: number;

  @Column({ name: 'total_length', type: 'integer', nullable: false })
  totalLength: number;

  @Column({ name: 'total_width', type: 'integer', nullable: false })
  totalWidth: number;

  @Column({ name: 'total_height', type: 'integer', nullable: false })
  totalHeight: number;

  @Column({ name: 'total_weight', type: 'integer', nullable: false })
  totalWeight: number;

  @Column({ name: 'total_volume', type: 'integer', nullable: false })
  totalVolume: number;

  @Column({ name: 'main_packaging_id', type: 'integer', nullable: false })
  mainPackagingId: number;

  @Column({ name: 'secondary_packaging_id', type: 'integer', nullable: true })
  secondaryPackagingId?: number;

  @Column({ name: 'user_id', type: 'integer', nullable: false })
  userId: number;

  @Column({ name: 'status_id', type: 'integer', nullable: false, default: 0 })
  statusId: number;

  @Column({ type: 'varchar', nullable: true })
  linking: string;

  @Column({ type: 'varchar', nullable: true })
  observations: string;

  @Column({ type: 'varchar', name: 'verified_observations', nullable: true })
  verifiedObservations: string;

  @Column({
    name: 'created_at',
    type: 'timestamp',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
  })
  createdAt: Date;

  @Column({
    name: 'updated_at',
    type: 'timestamp',
    nullable: true,
  })
  updatedAt: Date;

  @Column({
    name: 'deleted_at',
    type: 'timestamp',
    nullable: true,
  })
  deletedAt: Date;

  @OneToMany('RomaneioOrder', (romaneioOrder: any) => romaneioOrder.romaneioId)
  romaneioOrders: any[];
}
