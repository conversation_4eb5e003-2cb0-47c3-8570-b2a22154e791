"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DriverController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const platform_express_1 = require("@nestjs/platform-express");
const driver_service_1 = require("./driver.service");
const create_driver_dto_1 = require("./dto/create-driver.dto");
const update_driver_dto_1 = require("./dto/update-driver.dto");
let DriverController = class DriverController {
    driverService;
    constructor(driverService) {
        this.driverService = driverService;
    }
    findAll() {
        return this.driverService.findAll();
    }
    async findOne(id) {
        return this.driverService.findOne(id);
    }
    async create(dto, file) {
        return this.driverService.create(dto, file);
    }
    async update(id, dto) {
        return this.driverService.update(id, dto);
    }
    async delete(id) {
        const deleted = await this.driverService.delete(id);
        if (!deleted)
            throw new common_1.NotFoundException('Motorista não encontrado');
        return { message: 'Motorista excluído com sucesso' };
    }
};
exports.DriverController = DriverController;
__decorate([
    (0, common_1.Get)('/list'),
    (0, swagger_1.ApiOperation)({
        summary: 'Listar todos os motoristas',
        description: 'Retorna uma lista com todos os motoristas cadastrados no sistema.',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de motoristas',
        schema: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    id: { type: 'string', example: 'uuid-string' },
                    name: { type: 'string', example: 'João Silva' },
                    document: { type: 'string', example: '123.456.789-00' },
                    license: { type: 'string', example: '12345678901' },
                    phone: { type: 'string', example: '(11) 99999-9999' },
                    isActive: { type: 'boolean', example: true },
                },
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], DriverController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('/view/:id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Obter motorista por ID',
        description: 'Retorna os detalhes completos de um motorista específico.',
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'ID do motorista', type: 'string' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Dados do motorista',
        schema: {
            type: 'object',
            properties: {
                id: { type: 'string', example: 'uuid-string' },
                name: { type: 'string', example: 'João Silva' },
                document: { type: 'string', example: '123.456.789-00' },
                license: { type: 'string', example: '12345678901' },
                phone: { type: 'string', example: '(11) 99999-9999' },
                licenseDocument: { type: 'string', example: 'path/to/document.pdf' },
                isActive: { type: 'boolean', example: true },
                createdAt: { type: 'string', format: 'date-time' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Motorista não encontrado' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DriverController.prototype, "findOne", null);
__decorate([
    (0, common_1.Post)('/create'),
    (0, swagger_1.ApiOperation)({
        summary: 'Criar novo motorista',
        description: 'Cria um novo motorista no sistema. Pode incluir upload de documento da CNH.',
    }),
    (0, swagger_1.ApiConsumes)('multipart/form-data'),
    (0, swagger_1.ApiBody)({
        description: 'Dados do motorista e documento (opcional)',
        schema: {
            type: 'object',
            properties: {
                name: { type: 'string', example: 'João Silva' },
                document: { type: 'string', example: '123.456.789-00' },
                license: { type: 'string', example: '12345678901' },
                phone: { type: 'string', example: '(11) 99999-9999' },
                licenseDocument: {
                    type: 'string',
                    format: 'binary',
                    description: 'Arquivo da CNH (PDF, JPG, PNG)',
                },
            },
            required: ['name', 'document', 'license'],
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Motorista criado com sucesso',
        schema: {
            type: 'object',
            properties: {
                id: { type: 'string', example: 'uuid-string' },
                name: { type: 'string', example: 'João Silva' },
                document: { type: 'string', example: '123.456.789-00' },
                license: { type: 'string', example: '12345678901' },
                phone: { type: 'string', example: '(11) 99999-9999' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dados inválidos' }),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('licenseDocument')),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.UploadedFile)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_driver_dto_1.CreateDriverDto, Object]),
    __metadata("design:returntype", Promise)
], DriverController.prototype, "create", null);
__decorate([
    (0, common_1.Put)('/update/:id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Atualizar motorista',
        description: 'Atualiza os dados de um motorista existente.',
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'ID do motorista', type: 'string' }),
    (0, swagger_1.ApiBody)({ type: update_driver_dto_1.UpdateDriverDto }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Motorista atualizado com sucesso',
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Motorista não encontrado' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dados inválidos' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_driver_dto_1.UpdateDriverDto]),
    __metadata("design:returntype", Promise)
], DriverController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)('/delete/:id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Deletar motorista',
        description: 'Remove um motorista do sistema.',
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'ID do motorista', type: 'string' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Motorista excluído com sucesso',
        schema: {
            type: 'object',
            properties: {
                message: { type: 'string', example: 'Motorista excluído com sucesso' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Motorista não encontrado' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DriverController.prototype, "delete", null);
exports.DriverController = DriverController = __decorate([
    (0, swagger_1.ApiTags)('Motoristas'),
    (0, common_1.Controller)('drivers'),
    __metadata("design:paramtypes", [driver_service_1.DriverService])
], DriverController);
//# sourceMappingURL=driver.controller.js.map