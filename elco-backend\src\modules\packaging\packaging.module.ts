import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PackagingService } from './packaging.service';
import { PackagingController } from './packaging.controller';
import { Packaging } from './entity/packaging.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Packaging])],
  controllers: [PackagingController],
  providers: [PackagingService],
})
export class PackagingModule {}
