import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository, IsNull } from 'typeorm';
import { PackagingService } from '../packaging.service';
import { Packaging } from '../entity/packaging.entity';
import { CreatePackagingDto } from '../dto/create-packaging.dto';
import { UpdatePackagingDto } from '../dto/update-packaging.dto';
import { NotFoundException } from '@nestjs/common';

describe('PackagingService', () => {
  let service: PackagingService;
  let repository: Repository<Packaging>;

  const mockPackaging = {
    id: '1',
    name: 'Caixa Teste',
    weight: 1.5,
    height: 20,
    length: 30,
    width: 25,
    volume: 0.015,
    type: 'Caixa',
    createdAt: new Date(),
    deleted_at: null,
  };

  const mockRepository = {
    find: jest.fn(),
    findOne: jest.fn(),
    create: jest.fn(),
    save: jest.fn(),
    softDelete: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PackagingService,
        {
          provide: getRepositoryToken(Packaging),
          useValue: mockRepository,
        },
      ],
    }).compile();

    service = module.get<PackagingService>(PackagingService);
    repository = module.get<Repository<Packaging>>(getRepositoryToken(Packaging));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findAll', () => {
    it('should return all non-deleted packagings', async () => {
      const mockPackagings = [mockPackaging];
      mockRepository.find.mockResolvedValue(mockPackagings);

      const result = await service.findAll();

      expect(result).toEqual(mockPackagings);
      expect(mockRepository.find).toHaveBeenCalledWith({
        where: { deleted_at: IsNull() },
        order: { name: 'ASC' },
      });
    });
  });

  describe('create', () => {
    it('should create a new packaging', async () => {
      const createDto: CreatePackagingDto = {
        name: 'Nova Caixa',
        weight: 2.0,
        height: 25,
        length: 35,
        width: 30,
        volume: 0.02625,
        type: 'Caixa',
      };

      mockRepository.create.mockReturnValue(mockPackaging);
      mockRepository.save.mockResolvedValue(mockPackaging);

      const result = await service.create(createDto);

      expect(result).toEqual(mockPackaging);
      expect(mockRepository.create).toHaveBeenCalledWith(createDto);
      expect(mockRepository.save).toHaveBeenCalledWith(mockPackaging);
    });
  });

  describe('update', () => {
    it('should update an existing packaging', async () => {
      const updateDto: UpdatePackagingDto = {
        name: 'Caixa Atualizada',
        weight: 2.5,
      };

      const updatedPackaging = { ...mockPackaging, ...updateDto };

      mockRepository.findOne.mockResolvedValue(mockPackaging);
      mockRepository.save.mockResolvedValue(updatedPackaging);

      const result = await service.update('1', updateDto);

      expect(result).toEqual(updatedPackaging);
      expect(mockRepository.findOne).toHaveBeenCalledWith({
        where: { id: '1', deleted_at: IsNull() },
      });
      expect(mockRepository.save).toHaveBeenCalledWith(updatedPackaging);
    });

    it('should throw NotFoundException when packaging not found', async () => {
      mockRepository.findOne.mockResolvedValue(null);

      await expect(service.update('999', { name: 'Test' })).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('delete', () => {
    it('should soft delete a packaging', async () => {
      mockRepository.findOne.mockResolvedValue(mockPackaging);
      mockRepository.softDelete.mockResolvedValue({ affected: 1 });

      const result = await service.delete('1');

      expect(result).toBe(true);
      expect(mockRepository.findOne).toHaveBeenCalledWith({
        where: { id: '1', deleted_at: IsNull() },
      });
      expect(mockRepository.softDelete).toHaveBeenCalledWith('1');
    });

    it('should throw NotFoundException when packaging not found', async () => {
      mockRepository.findOne.mockResolvedValue(null);

      await expect(service.delete('999')).rejects.toThrow(NotFoundException);
    });
  });

  describe('findOne', () => {
    it('should return a packaging by id', async () => {
      mockRepository.findOne.mockResolvedValue(mockPackaging);

      const result = await service.findOne('1');

      expect(result).toEqual(mockPackaging);
      expect(mockRepository.findOne).toHaveBeenCalledWith({
        where: { id: '1', deleted_at: IsNull() },
      });
    });

    it('should return null when packaging not found', async () => {
      mockRepository.findOne.mockResolvedValue(null);

      const result = await service.findOne('999');

      expect(result).toBeNull();
    });
  });
});
