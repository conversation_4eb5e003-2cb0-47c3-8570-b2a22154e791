import { UserService } from './user.service';
import { User } from './entity/user.entity';
import { UpdateUserDto } from './dto/update-user.dto';
import { CreateUserDto } from './dto/create-user.dto';
interface AuthRequest extends Request {
    user: any;
}
export declare class UserController {
    private readonly userService;
    constructor(userService: UserService);
    getProfile(req: AuthRequest): Promise<{
        name: any;
        email: any;
        phone: any;
        role: any;
    }>;
    updateProfile(req: any, body: UpdateUserDto): Promise<User>;
    updateUser(id: string, body: UpdateUserDto): Promise<User>;
    getUser(id: string): Promise<User>;
    findAll(): Promise<User[]>;
    deleteUser(id: number): Promise<{
        message: string;
    }>;
    createUser(body: CreateUserDto): Promise<User>;
}
export {};
