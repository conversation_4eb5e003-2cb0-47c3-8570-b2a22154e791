import { Controller, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBody, ApiParam } from '@nestjs/swagger';
import { RomaneioOrdersService } from './romaneio-orders.service';
import { CreateRomaneioOrderDto } from './dto/create-romaneio-order.dto';
import { UpdateRomaneioOrderDto } from './dto/update-romaneio-order.dto';

@ApiTags('Pedidos de Romaneio')
@Controller('romaneio-orders')
export class RomaneioOrdersController {
  constructor(private readonly romaneioOrdersService: RomaneioOrdersService) {}

  @Post()
  @ApiOperation({
    summary: 'Criar novo pedido de romaneio',
    description: 'Cria um novo pedido vinculado a um romaneio.'
  })
  @ApiBody({
    type: CreateRomaneioOrderDto,
    description: 'Dados do novo pedido de romaneio',
    examples: {
      create: {
        summary: 'Exemplo de pedido',
        value: {
          romaneioId: 1,
          orderNumber: 'PED-001',
          clientName: 'Cliente ABC Ltda',
          products: [
            {
              productId: 1,
              quantity: 10,
              unitPrice: 25.50
            }
          ],
          observations: 'Entrega urgente'
        }
      }
    }
  })
  @ApiResponse({
    status: 201,
    description: 'Pedido de romaneio criado com sucesso',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'number', example: 1 },
        romaneioId: { type: 'number', example: 1 },
        orderNumber: { type: 'string', example: 'PED-001' },
        clientName: { type: 'string', example: 'Cliente ABC Ltda' },
        status: { type: 'string', example: 'PENDING' },
        totalValue: { type: 'number', example: 255.00 },
        createdAt: { type: 'string', format: 'date-time' }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  create(@Body() createRomaneioOrderDto: CreateRomaneioOrderDto) {
    return this.romaneioOrdersService.create(createRomaneioOrderDto);
  }

  @Get()
  @ApiOperation({
    summary: 'Listar todos os pedidos de romaneio',
    description: 'Retorna uma lista com todos os pedidos de romaneio cadastrados.'
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de pedidos de romaneio',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'number', example: 1 },
          romaneioId: { type: 'number', example: 1 },
          orderNumber: { type: 'string', example: 'PED-001' },
          clientName: { type: 'string', example: 'Cliente ABC Ltda' },
          status: { type: 'string', example: 'PENDING' },
          totalValue: { type: 'number', example: 255.00 },
          romaneio: {
            type: 'object',
            properties: {
              id: { type: 'number', example: 1 },
              externalCode: { type: 'string', example: 'ROM001' }
            }
          },
          createdAt: { type: 'string', format: 'date-time' }
        }
      }
    }
  })
  findAll() {
    return this.romaneioOrdersService.findAll();
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Obter pedido de romaneio por ID',
    description: 'Retorna os detalhes completos de um pedido de romaneio específico.'
  })
  @ApiParam({ name: 'id', description: 'ID do pedido de romaneio', type: 'string', example: '1' })
  @ApiResponse({
    status: 200,
    description: 'Dados do pedido de romaneio',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'number', example: 1 },
        romaneioId: { type: 'number', example: 1 },
        orderNumber: { type: 'string', example: 'PED-001' },
        clientName: { type: 'string', example: 'Cliente ABC Ltda' },
        status: { type: 'string', example: 'PENDING' },
        totalValue: { type: 'number', example: 255.00 },
        observations: { type: 'string', example: 'Entrega urgente' },
        romaneio: { type: 'object' },
        products: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              productId: { type: 'number', example: 1 },
              quantity: { type: 'number', example: 10 },
              unitPrice: { type: 'number', example: 25.50 },
              product: { type: 'object' }
            }
          }
        },
        createdAt: { type: 'string', format: 'date-time' }
      }
    }
  })
  @ApiResponse({ status: 404, description: 'Pedido de romaneio não encontrado' })
  findOne(@Param('id') id: string) {
    return this.romaneioOrdersService.findOne(+id);
  }

  @Patch(':id')
  @ApiOperation({
    summary: 'Atualizar pedido de romaneio',
    description: 'Atualiza os dados de um pedido de romaneio existente.'
  })
  @ApiParam({ name: 'id', description: 'ID do pedido de romaneio', type: 'string', example: '1' })
  @ApiBody({ type: UpdateRomaneioOrderDto })
  @ApiResponse({
    status: 200,
    description: 'Pedido de romaneio atualizado com sucesso'
  })
  @ApiResponse({ status: 404, description: 'Pedido de romaneio não encontrado' })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  update(@Param('id') id: string, @Body() updateRomaneioOrderDto: UpdateRomaneioOrderDto) {
    return this.romaneioOrdersService.update(+id, updateRomaneioOrderDto);
  }

  @Delete(':id')
  @ApiOperation({
    summary: 'Deletar pedido de romaneio',
    description: 'Remove um pedido de romaneio do sistema.'
  })
  @ApiParam({ name: 'id', description: 'ID do pedido de romaneio', type: 'string', example: '1' })
  @ApiResponse({
    status: 200,
    description: 'Pedido de romaneio removido com sucesso',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'Pedido de romaneio removido com sucesso' }
      }
    }
  })
  @ApiResponse({ status: 404, description: 'Pedido de romaneio não encontrado' })
  remove(@Param('id') id: string) {
    return this.romaneioOrdersService.remove(+id);
  }
}
