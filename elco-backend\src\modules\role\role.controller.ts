import { Controller, Get, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { RoleService } from './role.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';

@ApiTags('Roles')
@ApiBearerAuth('JWT-auth')
@Controller('role')
@UseGuards(JwtAuthGuard)
export class RoleController {
  constructor(private readonly roleService: RoleService) {}

  @Get()
  @ApiOperation({
    summary: 'Listar todas as roles',
    description: 'Retorna uma lista com todas as roles/permissões disponíveis no sistema.'
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de roles',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'number', example: 1 },
          name: { type: 'string', example: 'admin' },
          description: { type: 'string', example: 'Administrador do sistema' },
          permissions: {
            type: 'array',
            items: { type: 'string' },
            example: ['create_user', 'delete_user', 'manage_system']
          },
          isActive: { type: 'boolean', example: true },
          createdAt: { type: 'string', format: 'date-time' }
        }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Token inválido ou ausente' })
  findAll() {
    return this.roleService.findAll();
  }
}
