import { ConflictException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import { Driver } from './entity/driver.entity';
import { CreateDriverDto } from './dto/create-driver.dto';
import { UpdateDriverDto } from './dto/update-driver.dto';

@Injectable()
export class DriverService {
  constructor(
    @InjectRepository(Driver)
    private readonly driverRepository: Repository<Driver>,
    private readonly dataSource: DataSource,
  ) { }

  async findAll() {
    return this.driverRepository.find({ order: { name: 'ASC' } });
  }

  async findOne(id: string) {
    const driver = await this.driverRepository.findOne({ where: { id } });
    if (!driver) throw new NotFoundException('Motorista não encontrado');
    return driver;
  }

  async create(dto: CreateDriverDto, file?: Express.Multer.File) {
    const driver = this.driverRepository.create({
      ...dto,
      licenseExpirationDate: new Date(dto.licenseExpirationDate),
      licenseDocument: file?.buffer,
    });
    return this.driverRepository.save(driver);
  }

  async update(id: string, dto: UpdateDriverDto) {
    const driver = await this.findOne(id);
    Object.assign(driver, dto);
    return this.driverRepository.save(driver);
  }

  async delete(id: string) {
    const vinculatedRomanios = await this.dataSource
      .createQueryBuilder()
      .select('COUNT(rm.id)', 'count')
      .from('romaneio', 'rm')
      .where('rm.driver_id = :id', { id: id })
      .andWhere('rm.deleted_at IS NULL')
      .execute();

    const romaneiosCount = parseInt(vinculatedRomanios[0]?.count, 10) || 0;

    if (romaneiosCount > 0) {
      throw new ConflictException(`Motorista vinculado a ${romaneiosCount} romaneio(s)`);
    }

    const result: any = await this.driverRepository.delete(id);
    return result.affected > 0;
  }
}
