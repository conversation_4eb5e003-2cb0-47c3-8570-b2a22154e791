import { IsNumber, IsOptional, IsString } from 'class-validator';

export class CreateProductDto {
  @IsNumber()
  @IsOptional()
  readonly idEnterpriseExternal: number;

  @IsNumber()
  @IsOptional()
  readonly idDepositorExternal: number;

  @IsString()
  @IsOptional()
  readonly depositor: string;

  @IsNumber()
  @IsOptional()
  readonly idExternal: number;

  @IsString()
  @IsOptional()
  readonly name: string;

  @IsString()
  @IsOptional()
  readonly fullName: string;

  @IsString()
  @IsOptional()
  readonly description: string;
}
