-- Migration para adicionar campo deleted_at na tabela packagings
-- Data: 2024-12-19

-- Adiciona a coluna deleted_at na tabela packagings
ALTER TABLE packagings ADD COLUMN deleted_at TIMESTAMP NULL;

-- Cria um índice para melhorar performance das consultas com soft delete
CREATE INDEX idx_packagings_deleted_at ON packagings(deleted_at);

-- Comentário explicativo
COMMENT ON COLUMN packagings.deleted_at IS 'Campo para soft delete - quando preenchido, o registro é considerado deletado'; 