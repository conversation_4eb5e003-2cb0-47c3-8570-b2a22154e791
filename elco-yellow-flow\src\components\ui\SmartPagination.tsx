import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";

type SmartPaginationProps = {
  totalPages: number;
  currentPage: number;
  onPageChange: (page: number) => void;
};

const SmartPagination = ({
  totalPages,
  currentPage,
  onPageChange,
}: SmartPaginationProps) => {
  const siblings = 1;
  const totalPageNumbers = siblings * 2 + 5;
  const pages: (number | string)[] = [];

  const addPage = (page: number) => {
    pages.push(page);
  };

  const addEllipsis = () => {
    pages.push("...");
  };

  if (totalPageNumbers >= totalPages) {
    for (let i = 1; i <= totalPages; i++) {
      addPage(i);
    }
  } else {
    const left = Math.max(currentPage - siblings, 1);
    const right = Math.min(currentPage + siblings, totalPages);
    const showLeftDots = left > 2;
    const showRightDots = right < totalPages - 1;

    if (!showLeftDots && showRightDots) {
      for (let i = 1; i <= 3 + siblings * 2; i++) addPage(i);
      addEllipsis();
      addPage(totalPages);
    } else if (showLeftDots && !showRightDots) {
      addPage(1);
      addEllipsis();
      for (let i = totalPages - (2 + siblings * 2); i <= totalPages; i++) {
        addPage(i);
      }
    } else {
      addPage(1);
      addEllipsis();
      for (let i = left; i <= right; i++) {
        addPage(i);
      }
      addEllipsis();
      addPage(totalPages);
    }
  }

  return (
    <Pagination>
      <PaginationContent>
        <PaginationItem>
          <PaginationPrevious
            onClick={() => onPageChange(Math.max(currentPage - 1, 1))}
            className={
              currentPage === 1
                ? "pointer-events-none opacity-50"
                : "cursor-pointer"
            }
          />
        </PaginationItem>

        {pages.map((page, index) =>
          page === "..." ? (
            <PaginationItem key={`ellipsis-${index}`}>
              <PaginationEllipsis />
            </PaginationItem>
          ) : (
            <PaginationItem key={`page-${page}`}>
              <PaginationLink
                isActive={currentPage === page}
                onClick={() => onPageChange(Number(page))}
              >
                {page}
              </PaginationLink>
            </PaginationItem>
          )
        )}

        <PaginationItem>
          <PaginationNext
            onClick={() =>
              onPageChange(Math.min(currentPage + 1, totalPages))
            }
            className={
              currentPage === totalPages
                ? "pointer-events-none opacity-50"
                : "cursor-pointer"
            }
          />
        </PaginationItem>
      </PaginationContent>
    </Pagination>
  );
};

export default SmartPagination;
