import { Module, forwardRef } from '@nestjs/common';
import { VnfService } from './vnf.service';
import { VnfController } from './vnf.controller';
import { EmailService } from './email.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { VnfError } from './vnf-error.entity';
import { VnfConsultaProcessada } from './entities/vnf-consulta-processada.entity';
import { Invoice } from '../invoices/entities/invoice.entity';
import { InvoiceItem } from '../invoices/entities/invoice-item.entity';
import { Product } from '../products/entities/product.entity';
import { InvoicesModule } from '../invoices/invoices.module';
import { ProductsModule } from '../products/products.module';
import { SeparationOrder } from '../separation-orders/entity/separation-order.entity';
import { Romaneio } from '../romaneios/entities/romaneio.entity';
import { RomaneioOrder } from '../romaneio-orders/entities/romaneio-order.entity';
import { SupplierEmail } from '../supplier-email/entity/supplier-email.entity';
import { OracleModule } from '../oracle/oracle.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Invoice,
      InvoiceItem,
      Product,
      VnfError,
      VnfConsultaProcessada,
      SeparationOrder,
      Romaneio,
      RomaneioOrder,
      SupplierEmail,
    ]),
    forwardRef(() => InvoicesModule),
    forwardRef(() => ProductsModule),
    OracleModule,
  ],
  providers: [VnfService, EmailService],
  controllers: [VnfController],
  exports: [VnfService],
})
export class VnfModule {}
