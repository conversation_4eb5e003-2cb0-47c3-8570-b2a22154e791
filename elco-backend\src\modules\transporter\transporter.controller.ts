import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Body,
  NotFoundException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBody,
  ApiParam,
} from '@nestjs/swagger';
import { TransporterService } from './transporter.service';
import { CreateTransporterDto } from './dto/create-transporter.dto';
import { UpdateTransporterDto } from './dto/update-transporter.dto';

@ApiTags('Transportadoras')
@Controller('transporters')
export class TransporterController {
  constructor(private readonly transporterService: TransporterService) {}

  @Get('/list')
  @ApiOperation({
    summary: 'Listar todas as transportadoras',
    description:
      'Retorna uma lista com todas as empresas transportadoras cadastradas.',
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de transportadoras',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'string', example: 'uuid-string' },
          name: { type: 'string', example: 'Transportadora ABC Ltda' },
          document: { type: 'string', example: '12.345.678/0001-90' },
          phone: { type: 'string', example: '(11) 3333-4444' },
          email: { type: 'string', example: '<EMAIL>' },
          address: { type: 'string', example: 'Rua das Empresas, 456' },
          isActive: { type: 'boolean', example: true },
        },
      },
    },
  })
  findAll() {
    return this.transporterService.findAll();
  }

  @Get('/view/:id')
  @ApiOperation({
    summary: 'Obter transportadora por ID',
    description:
      'Retorna os detalhes completos de uma transportadora específica.',
  })
  @ApiParam({ name: 'id', description: 'ID da transportadora', type: 'string' })
  @ApiResponse({
    status: 200,
    description: 'Dados da transportadora',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string', example: 'uuid-string' },
        name: { type: 'string', example: 'Transportadora ABC Ltda' },
        document: { type: 'string', example: '12.345.678/0001-90' },
        phone: { type: 'string', example: '(11) 3333-4444' },
        email: { type: 'string', example: '<EMAIL>' },
        address: { type: 'string', example: 'Rua das Empresas, 456' },
        isActive: { type: 'boolean', example: true },
        createdAt: { type: 'string', format: 'date-time' },
      },
    },
  })
  @ApiResponse({ status: 404, description: 'Transportadora não encontrada' })
  async findOne(@Param('id') id: string) {
    return this.transporterService.findOne(id);
  }

  @Post('/create')
  @ApiOperation({
    summary: 'Criar nova transportadora',
    description: 'Cadastra uma nova empresa transportadora no sistema.',
  })
  @ApiBody({
    type: CreateTransporterDto,
    description: 'Dados da nova transportadora',
    examples: {
      create: {
        summary: 'Exemplo de transportadora',
        value: {
          name: 'Transportadora XYZ Ltda',
          document: '98.765.432/0001-10',
          phone: '(11) 5555-6666',
          email: '<EMAIL>',
          address: 'Av. Principal, 789',
        },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Transportadora criada com sucesso',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string', example: 'uuid-string' },
        name: { type: 'string', example: 'Transportadora XYZ Ltda' },
        document: { type: 'string', example: '98.765.432/0001-10' },
        phone: { type: 'string', example: '(11) 5555-6666' },
        email: { type: 'string', example: '<EMAIL>' },
        address: { type: 'string', example: 'Av. Principal, 789' },
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  create(@Body() dto: CreateTransporterDto) {
    return this.transporterService.create(dto);
  }

  @Put('/update/:id')
  @ApiOperation({
    summary: 'Atualizar transportadora',
    description: 'Atualiza os dados de uma transportadora existente.',
  })
  @ApiParam({ name: 'id', description: 'ID da transportadora', type: 'string' })
  @ApiBody({ type: UpdateTransporterDto })
  @ApiResponse({
    status: 200,
    description: 'Transportadora atualizada com sucesso',
  })
  @ApiResponse({ status: 404, description: 'Transportadora não encontrada' })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  async update(@Param('id') id: string, @Body() dto: UpdateTransporterDto) {
    return this.transporterService.update(id, dto);
  }

  @Delete('/delete/:id')
  @ApiOperation({
    summary: 'Deletar transportadora',
    description: 'Remove uma transportadora do sistema.',
  })
  @ApiParam({ name: 'id', description: 'ID da transportadora', type: 'string' })
  @ApiResponse({
    status: 200,
    description: 'Transportadora excluída com sucesso',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'Transportador excluído com sucesso',
        },
      },
    },
  })
  @ApiResponse({ status: 404, description: 'Transportadora não encontrada' })
  async delete(@Param('id') id: string) {
    const deleted = await this.transporterService.delete(id);
    if (!deleted) throw new NotFoundException('Transportador não encontrado');
    return { message: 'Transportador excluído com sucesso' };
  }
}
