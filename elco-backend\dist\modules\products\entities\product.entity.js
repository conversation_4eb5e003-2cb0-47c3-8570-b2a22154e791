"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Product = void 0;
const typeorm_1 = require("typeorm");
let Product = class Product {
    id;
    idEnterpriseExternal;
    idDepositorExternal;
    depositor;
    quantity;
    idExternal;
    name;
    fullName;
    description;
    type;
    idRomanio;
    verified;
    idPackaging;
    idSeparationOrder;
    volume;
    weight;
    verifiedDate;
    verifiedTime;
    verifiedBy;
    taxNoteNumber;
    unitValue;
    totalValue;
    createdAt;
    updatedAt;
    deletedAt;
};
exports.Product = Product;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], Product.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'id_enterprise_external', type: 'integer', nullable: false }),
    __metadata("design:type", Number)
], Product.prototype, "idEnterpriseExternal", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'id_depositor_external', type: 'integer', nullable: false }),
    __metadata("design:type", Number)
], Product.prototype, "idDepositorExternal", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', nullable: true }),
    __metadata("design:type", String)
], Product.prototype, "depositor", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'float', nullable: true }),
    __metadata("design:type", Number)
], Product.prototype, "quantity", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'id_external', type: 'integer', nullable: false }),
    __metadata("design:type", Number)
], Product.prototype, "idExternal", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', nullable: true }),
    __metadata("design:type", String)
], Product.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'full_name', type: 'varchar', nullable: true }),
    __metadata("design:type", String)
], Product.prototype, "fullName", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', nullable: true }),
    __metadata("design:type", String)
], Product.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'type', type: 'varchar', nullable: true }),
    __metadata("design:type", String)
], Product.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'id_romanio', type: 'integer', nullable: false }),
    __metadata("design:type", Number)
], Product.prototype, "idRomanio", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'verified', type: 'boolean', default: 0 }),
    __metadata("design:type", Boolean)
], Product.prototype, "verified", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'id_packaging', type: 'integer', nullable: true }),
    __metadata("design:type", Number)
], Product.prototype, "idPackaging", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'id_separation_order', type: 'integer', nullable: true }),
    __metadata("design:type", Number)
], Product.prototype, "idSeparationOrder", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'volume', type: 'varchar', nullable: true }),
    __metadata("design:type", String)
], Product.prototype, "volume", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'weight', type: 'float', nullable: true }),
    __metadata("design:type", Number)
], Product.prototype, "weight", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'verified_date', type: 'date', nullable: true }),
    __metadata("design:type", String)
], Product.prototype, "verifiedDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'verified_time', type: 'time', nullable: true }),
    __metadata("design:type", String)
], Product.prototype, "verifiedTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'verified_by', type: 'varchar', nullable: true }),
    __metadata("design:type", String)
], Product.prototype, "verifiedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'tax_note_number', type: 'varchar', nullable: true }),
    __metadata("design:type", String)
], Product.prototype, "taxNoteNumber", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'unit_value',
        type: 'decimal',
        precision: 10,
        scale: 2,
        nullable: true,
    }),
    __metadata("design:type", Number)
], Product.prototype, "unitValue", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'total_value',
        type: 'decimal',
        precision: 10,
        scale: 2,
        nullable: true,
    }),
    __metadata("design:type", Number)
], Product.prototype, "totalValue", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'created_at',
        type: 'timestamp',
        nullable: false,
        default: () => 'CURRENT_TIMESTAMP',
    }),
    __metadata("design:type", Date)
], Product.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'updated_at',
        type: 'timestamp',
        nullable: true,
    }),
    __metadata("design:type", Date)
], Product.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'deleted_at',
        type: 'timestamp',
        nullable: true,
    }),
    __metadata("design:type", Date)
], Product.prototype, "deletedAt", void 0);
exports.Product = Product = __decorate([
    (0, typeorm_1.Entity)()
], Product);
//# sourceMappingURL=product.entity.js.map