import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, IsNull } from 'typeorm';
import { CreatePackagingDto } from './dto/create-packaging.dto';
import { UpdatePackagingDto } from './dto/update-packaging.dto';
import { Packaging } from './entity/packaging.entity';

@Injectable()
export class PackagingService {
    constructor(
        @InjectRepository(Packaging)
        private readonly packagingRepository: Repository<Packaging>,
    ) { }

    async findAll(): Promise<Packaging[]> {
        return this.packagingRepository.find({ 
            where: { deleted_at: IsNull() },
            order: { name: 'ASC' } 
        });
    }

    async create(dto: CreatePackagingDto): Promise<Packaging> {
        const packaging = this.packagingRepository.create(dto);
        return this.packagingRepository.save(packaging);
    }

    async update(id: string, dto: UpdatePackagingDto): Promise<Packaging> {
        const packaging = await this.packagingRepository.findOne({ 
            where: { id, deleted_at: IsNull() } 
        });

        if (!packaging) {
            throw new NotFoundException('Embalagem não encontrada');    
        }

        Object.assign(packaging, dto);
        return this.packagingRepository.save(packaging);
    }

    async delete(id: string): Promise<boolean> {
        const packaging = await this.packagingRepository.findOne({ 
            where: { id, deleted_at: IsNull() } 
        });

        if (!packaging) {
            throw new NotFoundException('Embalagem não encontrada');
        }

        // Soft delete - marca como deletado mas mantém o registro
        await this.packagingRepository.softDelete(id);
        return true;
    }

    async findOne(id: string) {
        return this.packagingRepository.findOne({ 
            where: { id, deleted_at: IsNull() } 
        });
    }
}
