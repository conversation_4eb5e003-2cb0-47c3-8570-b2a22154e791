import { Controller, Get, Post, Body, Param, Put, Delete, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBody, ApiParam, ApiQuery } from '@nestjs/swagger';
import { AddressesService } from './addresses.service';
import { CreateAddressDto } from './dto/create-address.dto';
import { UpdateAddressDto } from './dto/update-address.dto';

@ApiTags('Endereços')
@Controller('addresses')
export class AddressesController {
  constructor(private readonly addressesService: AddressesService) {}

  @Post()
  @ApiOperation({
    summary: 'Criar novo endereço',
    description: 'Cria um novo endereço de entrega no sistema.'
  })
  @ApiBody({
    type: CreateAddressDto,
    description: 'Dados do novo endereço',
    examples: {
      create: {
        summary: 'Exemplo de endereço',
        value: {
          name: 'Endereço Principal',
          street: 'Rua das Flores, 123',
          neighborhood: 'Centro',
          city: 'São Paulo',
          state: 'SP',
          zipCode: '01234-567',
          projectName: 'Projeto Alpha'
        }
      }
    }
  })
  @ApiResponse({
    status: 201,
    description: 'Endereço criado com sucesso',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'number', example: 1 },
        name: { type: 'string', example: 'Endereço Principal' },
        street: { type: 'string', example: 'Rua das Flores, 123' },
        neighborhood: { type: 'string', example: 'Centro' },
        city: { type: 'string', example: 'São Paulo' },
        state: { type: 'string', example: 'SP' },
        zipCode: { type: 'string', example: '01234-567' },
        projectName: { type: 'string', example: 'Projeto Alpha' }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  create(@Body() createAddressDto: CreateAddressDto) {
    return this.addressesService.create(createAddressDto);
  }

  @Get()
  @ApiOperation({
    summary: 'Listar endereços',
    description: 'Lista todos os endereços ou filtra por nome do projeto.'
  })
  @ApiQuery({
    name: 'projectName',
    required: false,
    description: 'Nome do projeto para filtrar endereços',
    example: 'Projeto Alpha'
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de endereços',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'number', example: 1 },
          name: { type: 'string', example: 'Endereço Principal' },
          street: { type: 'string', example: 'Rua das Flores, 123' },
          neighborhood: { type: 'string', example: 'Centro' },
          city: { type: 'string', example: 'São Paulo' },
          state: { type: 'string', example: 'SP' },
          zipCode: { type: 'string', example: '01234-567' },
          projectName: { type: 'string', example: 'Projeto Alpha' }
        }
      }
    }
  })
  findAll(@Query('projectName') projectName?: string) {
    if (projectName) {
      return this.addressesService.findByProjectName(projectName);
    }
    return this.addressesService.findAll();
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Obter endereço por ID',
    description: 'Retorna os detalhes de um endereço específico.'
  })
  @ApiParam({ name: 'id', description: 'ID do endereço', type: 'string', example: '1' })
  @ApiResponse({
    status: 200,
    description: 'Dados do endereço',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'number', example: 1 },
        name: { type: 'string', example: 'Endereço Principal' },
        street: { type: 'string', example: 'Rua das Flores, 123' },
        neighborhood: { type: 'string', example: 'Centro' },
        city: { type: 'string', example: 'São Paulo' },
        state: { type: 'string', example: 'SP' },
        zipCode: { type: 'string', example: '01234-567' },
        projectName: { type: 'string', example: 'Projeto Alpha' }
      }
    }
  })
  @ApiResponse({ status: 404, description: 'Endereço não encontrado' })
  findOne(@Param('id') id: string) {
    return this.addressesService.findOne(Number(id));
  }

  @Put(':id')
  @ApiOperation({
    summary: 'Atualizar endereço',
    description: 'Atualiza os dados de um endereço existente.'
  })
  @ApiParam({ name: 'id', description: 'ID do endereço', type: 'string', example: '1' })
  @ApiBody({ type: UpdateAddressDto })
  @ApiResponse({
    status: 200,
    description: 'Endereço atualizado com sucesso'
  })
  @ApiResponse({ status: 404, description: 'Endereço não encontrado' })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  update(@Param('id') id: string, @Body() updateAddressDto: UpdateAddressDto) {
    return this.addressesService.update(Number(id), updateAddressDto);
  }

  @Delete(':id')
  @ApiOperation({
    summary: 'Deletar endereço',
    description: 'Remove um endereço do sistema.'
  })
  @ApiParam({ name: 'id', description: 'ID do endereço', type: 'string', example: '1' })
  @ApiResponse({
    status: 200,
    description: 'Endereço removido com sucesso',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'Endereço removido com sucesso' }
      }
    }
  })
  @ApiResponse({ status: 404, description: 'Endereço não encontrado' })
  remove(@Param('id') id: string) {
    return this.addressesService.remove(Number(id));
  }
} 