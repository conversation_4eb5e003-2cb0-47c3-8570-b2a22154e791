// src/sync/sync.service.ts
import { Injectable, Logger } from '@nestjs/common';
import { <PERSON>ron } from '@nestjs/schedule';
import axios from 'axios';
import { SeparationOrderService } from '../separation-orders/separation-orders.service';

@Injectable()
export class SyncService {
  private readonly logger = new Logger(SyncService.name);

  constructor(private readonly orderService: SeparationOrderService) {}

  @Cron('*/60 * * * * *') // Reduzir de 30s para 60s para diminuir conflitos
  async fetchAndSaveOrders() {
    this.logger.log('🔄 Fetching separation orders...');
    
    // Usar timeout menor para operações
    try {
      const timeout = new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Timeout na sincronização')), 15000)
      );
      
      const syncOperation = async () => {
        const allOrders: any[] = [];
        let page = 1;
        const pageSize = 100;

        while (true) {
          const res = await axios.get('https://apigateway.smartgo.com.br/expedicao', {
            params: {
              Page: page,
              PageSize: pageSize,
              Status: 'PEDIDO_EM_ATENDIMENTO',
              subStatus: 'SEPARADO'
            },
            headers: {
                Accept: 'application/json',
                api_key: process.env.WMS_API_KEY,
              },
            timeout: 10000, // 10s timeout para cada request
          });

          const data = res.data?.model?.items ?? [];
          allOrders.push(...data);

          if (data.length < pageSize) break;
          page++;
        }

        const mapped = allOrders.map((o) => ({
          internalCode: o.codigoInterno,
          depositorName: o.nomeDepositante,
          orderDate: new Date(o.dataDoPedido),
          status: 'PENDING',
          externalCode: o.codigoExterno,
          observation: o.observacao ?? '',
          environment: o.ambienteDeGeracao ?? '',
          typeOfBond: ''
        }));

        const result = await this.orderService.saveNew(mapped);
        this.logger.log(`✅ ${result.length} new separation orders saved.`);
      };

      // Executar com timeout
      await Promise.race([syncOperation(), timeout]);
    } catch (error) {
      this.logger.error('❌ Erro na sincronização:', error.message);
      // Não interromper o processo por erro na sincronização
    }
  }
}
