"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SeedService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const role_entity_1 = require("../modules/role/entity/role.entity");
const role_seeder_1 = require("./role.seeder");
const user_service_1 = require("../modules/user/user.service");
let SeedService = class SeedService {
    roleRepo;
    userService;
    constructor(roleRepo, userService) {
        this.roleRepo = roleRepo;
        this.userService = userService;
    }
    async onModuleInit() {
        await (0, role_seeder_1.seedRoles)(this.roleRepo);
        console.log('Seed de roles executada com sucesso');
        const adminDto = {
            name: 'Admin Teste',
            email: '<EMAIL>',
            password: 'admin123',
            phone: '999999999',
            role: 'ADMIN',
        };
        try {
            await this.userService.create(adminDto);
            console.log('Usuário admin de teste criado com sucesso');
        }
        catch (e) {
            if (e.message && e.message.includes('E-mail já cadastrado')) {
                console.log('Usuário admin de teste já existe');
            }
            else {
                console.error('Erro ao criar usuário admin de teste:', e);
            }
        }
    }
};
exports.SeedService = SeedService;
exports.SeedService = SeedService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(role_entity_1.RolesEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        user_service_1.UserService])
], SeedService);
//# sourceMappingURL=seed.service.js.map