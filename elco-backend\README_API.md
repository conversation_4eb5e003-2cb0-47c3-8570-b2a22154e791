# 📖 API ELCO - Documentação Completa

## 🚀 Visão Geral

A API ELCO é um sistema completo para gerenciamento de logística e expedições, desenvolvido com NestJS e totalmente documentado com Swagger.

### 🔗 Links Importantes
- **Swagger UI**: [http://localhost:3000/api](http://localhost:3000/api)
- **API Base**: [http://localhost:3000](http://localhost:3000)
- **Ambiente**: Desenvolvimento

## 🎯 Como Iniciar

### 1. Iniciar o Servidor
```bash
# Modo padrão
npm run start:dev

# Modo com documentação destacada
npm run start:docs
```

### 2. Acessar a Documentação
Abra [http://localhost:3000/api](http://localhost:3000/api) no seu navegador.

## 🔐 Autenticação

### Fazer Login
```http
POST /auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "senha123"
}
```

**Resposta:**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": 1,
    "name": "João Silva",
    "email": "<EMAIL>",
    "role": {
      "id": 1,
      "name": "admin"
    }
  }
}
```

### Usar o Token
1. Copie o `access_token` da resposta
2. No Swagger UI, clique em **"Authorize"**
3. Cole o token no campo (sem "Bearer ")
4. Clique em **"Authorize"**

## 📊 Endpoints por Categoria

### 🔑 1. Autenticação
| Método | Endpoint | Descrição |
|--------|----------|-----------|
| `POST` | `/auth/login` | Fazer login e obter token JWT |

### 👥 2. Usuários
| Método | Endpoint | Descrição |
|--------|----------|-----------|
| `GET` | `/users/me` | Perfil do usuário logado |
| `PUT` | `/users/me` | Atualizar perfil próprio |
| `GET` | `/users` | Listar todos os usuários |
| `GET` | `/users/view/:id` | Obter usuário por ID |
| `PUT` | `/users/edit/:id` | Atualizar usuário por ID |
| `POST` | `/users` | Criar novo usuário |
| `DELETE` | `/users/delete/:id` | Desativar usuário |

### 📍 3. Endereços
| Método | Endpoint | Descrição |
|--------|----------|-----------|
| `GET` | `/addresses` | Listar endereços |
| `GET` | `/addresses/:id` | Obter endereço por ID |
| `POST` | `/addresses` | Criar endereço |
| `PUT` | `/addresses/:id` | Atualizar endereço |
| `DELETE` | `/addresses/:id` | Deletar endereço |

### 🚛 4. Motoristas
| Método | Endpoint | Descrição |
|--------|----------|-----------|
| `GET` | `/drivers/list` | Listar motoristas |
| `GET` | `/drivers/:id` | Obter motorista por ID |
| `POST` | `/drivers/create` | Criar motorista (com upload CNH) |
| `PUT` | `/drivers/:id` | Atualizar motorista |
| `DELETE` | `/drivers/:id` | Deletar motorista |

### 🚗 5. Veículos
| Método | Endpoint | Descrição |
|--------|----------|-----------|
| `GET` | `/vehicles/list` | Listar veículos |
| `GET` | `/vehicles/:id` | Obter veículo por ID |
| `POST` | `/vehicles/create` | Criar veículo (com upload docs) |
| `PUT` | `/vehicles/:id` | Atualizar veículo |
| `DELETE` | `/vehicles/:id` | Deletar veículo |

### 🏢 6. Transportadoras
| Método | Endpoint | Descrição |
|--------|----------|-----------|
| `GET` | `/transporters` | Listar transportadoras |
| `GET` | `/transporters/:id` | Obter transportadora |
| `POST` | `/transporters` | Criar transportadora |
| `PUT` | `/transporters/:id` | Atualizar transportadora |
| `DELETE` | `/transporters/:id` | Deletar transportadora |

### 📦 7. Produtos
| Método | Endpoint | Descrição |
|--------|----------|-----------|
| `GET` | `/products` | Listar produtos |
| `GET` | `/products/weights` | Listar pesos de produtos |
| `GET` | `/products/:id` | Obter produto por ID |
| `POST` | `/products` | Criar produto |
| `DELETE` | `/products/:id` | Deletar produto |
| `PATCH` | `/products/verify` | Verificar múltiplos produtos |
| `PATCH` | `/products/verify-many` | Verificar produtos por IDs |
| `POST` | `/products/product-packaging` | Sincronizar produto-embalagem |
| `PATCH` | `/products/:id/weight` | Atualizar peso do produto |
| `POST` | `/products/weights` | Criar peso de produto |
| `PUT` | `/products/weights/:id` | Atualizar peso de produto |
| `DELETE` | `/products/weights/:id` | Deletar peso de produto |

### 📦 8. Embalagens
| Método | Endpoint | Descrição |
|--------|----------|-----------|
| `GET` | `/packaging` | Listar embalagens |
| `GET` | `/packaging/:id` | Obter embalagem por ID |
| `POST` | `/packaging` | Criar embalagem |
| `PUT` | `/packaging/:id` | Atualizar embalagem |
| `DELETE` | `/packaging/:id` | Deletar embalagem |

### 📋 9. Romaneios
| Método | Endpoint | Descrição |
|--------|----------|-----------|
| `GET` | `/romaneios` | Listar romaneios |
| `GET` | `/romaneios/:id` | Obter romaneio por ID |
| `POST` | `/romaneios` | Criar romaneio |
| `POST` | `/romaneios/draft` | Criar rascunho de romaneio |
| `PUT` | `/romaneios/:id` | Atualizar romaneio |
| `DELETE` | `/romaneios/:id` | Deletar romaneio |
| `POST` | `/romaneios/:id/finalize` | Finalizar romaneio |

### 🧾 10. Notas Fiscais
| Método | Endpoint | Descrição |
|--------|----------|-----------|
| `GET` | `/invoices` | Listar notas fiscais (com paginação) |
| `GET` | `/invoices/errors` | Listar erros de notas fiscais |

### 📧 11. Fornecedores
| Método | Endpoint | Descrição |
|--------|----------|-----------|
| `GET` | `/supplier-emails` | Listar emails de fornecedores |
| `GET` | `/supplier-emails/:id` | Obter email por ID |
| `POST` | `/supplier-emails` | Criar email de fornecedor |
| `PUT` | `/supplier-emails/:id` | Atualizar email |
| `DELETE` | `/supplier-emails/:id` | Soft delete |
| `DELETE` | `/supplier-emails/:id/hard` | Hard delete |
| `POST` | `/supplier-emails/:id/restore` | Restaurar email |
| `GET` | `/supplier-emails/deleted` | Listar emails deletados |
| `POST` | `/supplier-emails/bulk-delete` | Deletar múltiplos emails |

### 🛡️ 12. Roles e Permissões
| Método | Endpoint | Descrição |
|--------|----------|-----------|
| `GET` | `/roles` | Listar roles disponíveis |

### 📦 13. Pedidos de Romaneio
| Método | Endpoint | Descrição |
|--------|----------|-----------|
| `GET` | `/romaneio-orders` | Listar pedidos |
| `GET` | `/romaneio-orders/:id` | Obter pedido por ID |
| `POST` | `/romaneio-orders` | Criar pedido |
| `PUT` | `/romaneio-orders/:id` | Atualizar pedido |
| `DELETE` | `/romaneio-orders/:id` | Deletar pedido |

### 📋 14. Ordens de Separação
| Método | Endpoint | Descrição |
|--------|----------|-----------|
| `GET` | `/separation-orders` | Listar ordens de separação |
| `GET` | `/separation-orders/:id` | Obter ordem por ID |
| `POST` | `/separation-orders` | Criar ordem |
| `PUT` | `/separation-orders/:id` | Atualizar ordem |

### 🔄 15. Mega/XML
| Método | Endpoint | Descrição |
|--------|----------|-----------|
| `GET` | `/mega` | Listar registros Mega |
| `GET` | `/mega/:id` | Obter registro por ID |
| `POST` | `/mega` | Processar XML |
| `PUT` | `/mega/:id` | Atualizar registro |

### 📄 16. VNF (Notas Fiscais Virtuais)
| Método | Endpoint | Descrição |
|--------|----------|-----------|
| `GET` | `/vnf` | Listar VNFs |
| `POST` | `/vnf` | Processar VNF |
| `GET` | `/vnf/:id` | Obter VNF por ID |

## 💡 Exemplos de Uso

### 1. Fluxo Completo de Autenticação
```javascript
// 1. Fazer login
const loginResponse = await fetch('http://localhost:3000/auth/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'senha123'
  })
});

const { access_token } = await loginResponse.json();

// 2. Usar token em outras requisições
const userProfile = await fetch('http://localhost:3000/users/me', {
  headers: {
    'Authorization': `Bearer ${access_token}`
  }
});
```

### 2. Criar um Motorista
```javascript
const formData = new FormData();
formData.append('name', 'João Silva');
formData.append('cnh', '123456789');
formData.append('phone', '(11) 99999-9999');
formData.append('cnhFile', cnhFile); // File object

const response = await fetch('http://localhost:3000/drivers/create', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${access_token}`
  },
  body: formData
});
```

### 3. Listar Produtos com Filtros
```javascript
const products = await fetch('http://localhost:3000/products', {
  headers: {
    'Authorization': `Bearer ${access_token}`
  }
});
```

## 🔧 Recursos Avançados

### 📄 Upload de Arquivos
- **Motoristas**: Upload de CNH (`/drivers/create`)
- **Veículos**: Upload de documentos (`/vehicles/create`)
- Formato: `multipart/form-data`

### 🔍 Paginação
- **Notas Fiscais**: Suporte a paginação com `page` e `limit`
- **Fornecedores**: Listagem com filtros

### 🛡️ Segurança
- **JWT Bearer Authentication**
- **Validação de dados** com class-validator
- **Guards de autenticação** em endpoints protegidos

### 📊 Validação
- **DTOs tipados** para todas as operações
- **Schemas detalhados** no Swagger
- **Exemplos práticos** em cada endpoint

## 🐛 Códigos de Status

| Status | Significado |
|--------|-------------|
| `200` | Sucesso |
| `201` | Criado com sucesso |
| `400` | Dados inválidos |
| `401` | Não autorizado (token inválido/ausente) |
| `403` | Proibido (sem permissão) |
| `404` | Não encontrado |
| `500` | Erro interno do servidor |

## 🧪 Testing

### Usar o Swagger UI
1. Acesse [http://localhost:3000/api](http://localhost:3000/api)
2. Teste o endpoint `/auth/login`
3. Copie o token retornado
4. Clique em "Authorize" e cole o token
5. Teste todos os outros endpoints

### Postman/Insomnia
- Importe a coleção do Swagger em `http://localhost:3000/api-json`
- Configure o Bearer Token globalmente

## 🚀 Produção

### Variáveis de Ambiente
```env
PORT=3000
DATABASE_HOST=localhost
DATABASE_PORT=3306
DATABASE_USERNAME=root
DATABASE_PASSWORD=password
DATABASE_NAME=elco
JWT_SECRET=sua_chave_secreta_aqui
```

### Build
```bash
npm run build
npm run start:prod
```

## 📞 Suporte

- **Documentação**: Swagger UI em `/api`
- **Logs**: Verificar console do servidor
- **Ambiente**: Development com hot-reload

---

**🎯 Total: 60+ endpoints documentados em 12 categorias organizadas** 