"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const user_entity_1 = require("./entity/user.entity");
const role_entity_1 = require("../role/entity/role.entity");
const bcrypt = require("bcrypt");
let UserService = class UserService {
    userRepo;
    roleRepo;
    constructor(userRepo, roleRepo) {
        this.userRepo = userRepo;
        this.roleRepo = roleRepo;
    }
    async update(id, dto) {
        const user = await this.userRepo.findOne({
            where: { id },
            relations: ['role'],
        });
        if (!user) {
            throw new common_1.NotFoundException('Usuário não encontrado');
        }
        if (dto.name !== undefined)
            user.name = dto.name;
        if (dto.phone !== undefined)
            user.phone = dto.phone;
        if (dto.email !== undefined)
            user.email = dto.email;
        if (dto.role) {
            const role = await this.roleRepo.findOneBy({ key: dto.role });
            if (!role) {
                throw new common_1.NotFoundException(`Tipo de perfil '${dto.role}' não encontrado`);
            }
            user.role_id = role.id;
            user.role = role;
        }
        return this.userRepo.save(user);
    }
    async findByEmail(email) {
        return this.userRepo.findOne({
            where: { email },
            withDeleted: false
        });
    }
    async findOne(id) {
        const user = await this.userRepo.findOne({
            where: { id },
            withDeleted: false
        });
        if (!user)
            throw new common_1.NotFoundException('Usuário não encontrado');
        return user;
    }
    findAll() {
        return this.userRepo.find({
            withDeleted: false,
            relations: ['role']
        });
    }
    async updateProfile(userId, data) {
        const user = await this.userRepo.findOne({
            where: { id: userId },
            withDeleted: false
        });
        if (!user) {
            throw new common_1.NotFoundException('Usuário não encontrado');
        }
        user.name = data.name ?? user.name;
        user.email = data.email ?? user.email;
        user.phone = data.phone ?? user.phone;
        return await this.userRepo.save(user);
    }
    async create(dto) {
        const existingUser = await this.userRepo.findOne({
            where: { email: dto.email },
            withDeleted: true
        });
        if (existingUser) {
            if (existingUser.deleted_at) {
                await this.userRepo.restore(existingUser.id);
                const role = await this.roleRepo.findOneBy({ key: dto.role });
                if (!role) {
                    throw new common_1.NotFoundException(`Tipo de perfil '${dto.role}' não encontrado`);
                }
                const hashedPassword = await bcrypt.hash(dto.password, 10);
                existingUser.name = dto.name;
                existingUser.phone = dto.phone;
                existingUser.password = hashedPassword;
                existingUser.role_id = role.id;
                existingUser.role = role;
                existingUser.deleted_at = undefined;
                return this.userRepo.save(existingUser);
            }
            else {
                throw new common_1.ConflictException('E-mail já cadastrado');
            }
        }
        const role = await this.roleRepo.findOneBy({ key: dto.role });
        if (!role) {
            throw new common_1.NotFoundException(`Tipo de perfil '${dto.role}' não encontrado`);
        }
        const hashedPassword = await bcrypt.hash(dto.password, 10);
        const user = this.userRepo.create({
            name: dto.name,
            email: dto.email,
            phone: dto.phone,
            password: hashedPassword,
            role_id: role.id,
            role: role
        });
        return this.userRepo.save(user);
    }
    async softDelete(id) {
        const user = await this.userRepo.findOne({
            where: { id },
            withDeleted: false
        });
        if (!user) {
            throw new common_1.NotFoundException('Usuário não encontrado');
        }
        await this.userRepo.softDelete(id);
        return { message: 'Usuário excluído com sucesso (deleção lógica)' };
    }
};
exports.UserService = UserService;
exports.UserService = UserService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __param(1, (0, typeorm_1.InjectRepository)(role_entity_1.RolesEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], UserService);
//# sourceMappingURL=user.service.js.map