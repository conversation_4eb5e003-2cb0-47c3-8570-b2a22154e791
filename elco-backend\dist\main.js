"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const app_module_1 = require("./app.module");
const swagger_1 = require("@nestjs/swagger");
async function bootstrap() {
    const app = await core_1.NestFactory.create(app_module_1.AppModule);
    app.enableCors();
    const config = new swagger_1.DocumentBuilder()
        .setTitle('ELCO API')
        .setDescription(`
      API completa do sistema ELCO para gerenciamento de logística e expedições.
      
      ## Recursos Principais:
      - **Autenticação**: Login e gerenciamento de usuários
      - **Gestão de Usuários**: CRUD completo de usuários com diferentes roles
      - **Endereços**: Gerenciamento de endereços de entrega
      - **Motoristas**: Cadastro e controle de motoristas
      - **Veículos**: Gerenciamento da frota de veículos
      - **Transportadoras**: Cadastro de empresas transportadoras
      - **Produtos**: Controle de produtos e pesos
      - **Embalagens**: Gestão de tipos de embalagem
      - **Romaneios**: Criação e controle de romaneios de carga
      - **Notas Fiscais**: Processamento e controle de NFe
      - **Fornecedores**: Gestão de emails de fornecedores
      - **Sincronização**: Integração com sistemas externos
      
      ## Autenticação:
      A maioria dos endpoints requer autenticação via JWT Bearer Token.
      Para obter o token, utilize o endpoint de login.
    `)
        .setVersion('1.0')
        .addBearerAuth({
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'JWT',
        description: 'Token JWT para autenticação',
        in: 'header',
    }, 'JWT-auth')
        .addTag('Autenticação', 'Endpoints para login e autenticação')
        .addTag('Usuários', 'Gerenciamento de usuários do sistema')
        .addTag('Endereços', 'Cadastro e controle de endereços')
        .addTag('Motoristas', 'Gestão de motoristas')
        .addTag('Veículos', 'Controle da frota de veículos')
        .addTag('Transportadoras', 'Cadastro de empresas transportadoras')
        .addTag('Produtos', 'Gestão de produtos e controle de peso')
        .addTag('Embalagens', 'Tipos e gestão de embalagens')
        .addTag('Romaneios', 'Criação e controle de romaneios')
        .addTag('Notas Fiscais', 'Processamento e controle de NFe')
        .addTag('Fornecedores', 'Gestão de emails de fornecedores')
        .addTag('Roles', 'Controle de permissões e roles')
        .addTag('Pedidos de Separação', 'Gestão de ordens de separação')
        .addTag('Mega/XML', 'Integração com sistema Mega e processamento XML')
        .addTag('VNF', 'Processamento de notas fiscais virtuais')
        .addTag('Sincronização', 'Endpoints de sincronização com sistemas externos')
        .build();
    const document = swagger_1.SwaggerModule.createDocument(app, config);
    swagger_1.SwaggerModule.setup('api', app, document, {
        swaggerOptions: {
            persistAuthorization: true,
            displayRequestDuration: true,
            docExpansion: 'none',
            filter: true,
            showRequestHeaders: true,
        },
    });
    await app.listen(process.env.PORT ?? 3000);
    console.log(`🚀 API rodando em: http://localhost:${process.env.PORT ?? 3000}`);
    console.log(`📖 Documentação Swagger disponível em: http://localhost:${process.env.PORT ?? 3000}/api`);
}
bootstrap();
//# sourceMappingURL=main.js.map