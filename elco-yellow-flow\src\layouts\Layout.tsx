
import React, { useState, useEffect } from 'react';
import Header from './Header';
import { cn } from '@/lib/utils';
import { useIsMobile } from '@/hooks/use-mobile';
import EngineeringLoader from '@/components/ui/EngineeringLoader';
import Sidebar from '@/layouts/Sidebar';

interface LayoutProps {
  children: React.ReactNode;
  title: string;
}

const Layout: React.FC<LayoutProps> = ({ children, title }) => {
  const isMobile = useIsMobile();
  const [loading, setLoading] = useState(true);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setLoading(false);
    }, 500);
    
    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="flex min-h-screen bg-gray-50 relative w-full">
      <Sidebar onCollapseChange={setSidebarCollapsed} />
      <div className={cn(
        "flex-1 flex flex-col min-h-screen transition-all duration-300 ease-in-out w-full", 
        isMobile 
          ? "ml-0" 
          : sidebarCollapsed 
            ? "ml-20" 
            : "ml-[280px]"
      )}>
        <Header title={title} />
        <main className="p-2 sm:p-4 md:p-6 flex-1 overflow-y-auto overflow-x-hidden">
          {loading ? (
            <div className="flex items-center justify-center min-h-[60vh]">
              <EngineeringLoader />
            </div>
          ) : (
            <div className="max-w-[1400px] mx-auto w-full text-left">
              {children}
            </div>
          )}
        </main>
      </div>
    </div>
  );
};

export default Layout;
