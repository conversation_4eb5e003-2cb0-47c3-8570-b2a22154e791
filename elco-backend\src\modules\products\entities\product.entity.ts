import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity()
export class Product {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'id_enterprise_external', type: 'integer', nullable: false })
  idEnterpriseExternal: number;

  @Column({ name: 'id_depositor_external', type: 'integer', nullable: false })
  idDepositorExternal: number;

  @Column({ type: 'varchar', nullable: true })
  depositor: string;

  @Column({ type: 'float', nullable: true })
  quantity: number;

  @Column({ name: 'id_external', type: 'integer', nullable: false })
  idExternal: number;

  @Column({ type: 'varchar', nullable: true })
  name: string;

  @Column({ name: 'full_name', type: 'varchar', nullable: true })
  fullName: string;

  @Column({ type: 'varchar', nullable: true })
  description: string;

  @Column({ name: 'type', type: 'varchar', nullable: true })
  type: string;

  @Column({ name: 'id_romanio', type: 'integer', nullable: false })
  idRomanio: number;

  @Column({ name: 'verified', type: 'boolean', default: 0 })
  verified: boolean;

  @Column({ name: 'id_packaging', type: 'integer', nullable: true })
  idPackaging: number;

  @Column({ name: 'id_separation_order', type: 'integer', nullable: true })
  idSeparationOrder: number;

  @Column({ name: 'volume', type: 'varchar', nullable: true })
  volume: string;

  @Column({ name: 'weight', type: 'float', nullable: true })
  weight: number;

  @Column({ name: 'verified_date', type: 'date', nullable: true })
  verifiedDate: string;

  @Column({ name: 'verified_time', type: 'time', nullable: true })
  verifiedTime: string;

  @Column({ name: 'verified_by', type: 'varchar', nullable: true })
  verifiedBy: string;

  @Column({ name: 'tax_note_number', type: 'varchar', nullable: true })
  taxNoteNumber: string;

  @Column({ name: 'unit_value', type: 'decimal', precision: 10, scale: 2, nullable: true })
  unitValue: number;

  @Column({ name: 'total_value', type: 'decimal', precision: 10, scale: 2, nullable: true })
  totalValue: number;

  @Column({
    name: 'created_at',
    type: 'timestamp',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
  })
  createdAt: Date;

  @Column({
    name: 'updated_at',
    type: 'timestamp',
    nullable: true,
  })
  updatedAt: Date;

  @Column({
    name: 'deleted_at',
    type: 'timestamp',
    nullable: true,
  })
  deletedAt: Date;
}
