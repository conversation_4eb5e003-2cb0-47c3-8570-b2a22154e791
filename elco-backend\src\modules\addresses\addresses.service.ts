import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Address } from './address.entity';
import { CreateAddressDto } from './dto/create-address.dto';
import { UpdateAddressDto } from './dto/update-address.dto';

@Injectable()
export class AddressesService {
  constructor(
    @InjectRepository(Address)
    private readonly addressRepository: Repository<Address>,
  ) {}

  async create(createAddressDto: CreateAddressDto): Promise<Address> {
    const address = this.addressRepository.create(createAddressDto);
    return this.addressRepository.save(address);
  }

  async findAll(): Promise<Address[]> {
    return this.addressRepository.find();
  }

  async findOne(id: number): Promise<Address> {
    const address = await this.addressRepository.findOneBy({ id });
    if (!address) {
      throw new Error(`Endereço com id ${id} não encontrado`);
    }
    return address;
  }

  async update(id: number, updateAddressDto: UpdateAddressDto): Promise<Address> {
    await this.addressRepository.update(id, updateAddressDto);
    return this.findOne(id);
  }

  async remove(id: number): Promise<void> {
    await this.addressRepository.delete(id);
  }

  async findByProjectName(projectName: string): Promise<Address[]> {
    console.log('🔍 Buscando endereços para projectName:', projectName);
    
    // Limpar e normalizar o nome do projeto
    const cleanProjectName = projectName.trim().toLowerCase();
    
    // Busca exata primeiro (case insensitive)
    let addresses = await this.addressRepository
      .createQueryBuilder('address')
      .where('LOWER(address.projectName) = LOWER(:projectName)', { projectName })
      .getMany();
    console.log('📋 Resultado busca exata:', addresses.length, 'endereços encontrados');
    
    // Se não encontrar, fazer busca parcial (case insensitive)
    if (addresses.length === 0) {
      console.log('🔍 Tentando busca parcial para:', projectName);
      addresses = await this.addressRepository
        .createQueryBuilder('address')
        .where('LOWER(address.projectName) LIKE LOWER(:projectName)', { projectName: `%${projectName}%` })
        .getMany();
      console.log('📋 Resultado busca parcial:', addresses.length, 'endereços encontrados');
    }
    
    // Se ainda não encontrar, tentar buscar por código numérico
    if (addresses.length === 0) {
      const numericCode = projectName.match(/\d+/)?.[0];
      if (numericCode) {
        console.log('🔍 Tentando busca por código numérico:', numericCode);
        addresses = await this.addressRepository
          .createQueryBuilder('address')
          .where('LOWER(address.projectName) LIKE LOWER(:numericCode)', { numericCode: `%${numericCode}%` })
          .getMany();
        console.log('📋 Resultado busca por código numérico:', addresses.length, 'endereços encontrados');
      }
    }
    
    // Se ainda não encontrar, tentar buscar por projectCode
    if (addresses.length === 0) {
      console.log('🔍 Tentando busca por projectCode:', projectName);
      addresses = await this.addressRepository
        .createQueryBuilder('address')
        .where('LOWER(address.projectCode) LIKE LOWER(:projectCode)', { projectCode: `%${projectName}%` })
        .getMany();
      console.log('📋 Resultado busca por projectCode:', addresses.length, 'endereços encontrados');
    }
    
    console.log('✅ Endereços encontrados:', addresses.map(a => ({ id: a.id, projectName: a.projectName, address: a.address })));
    return addresses;
  }
} 