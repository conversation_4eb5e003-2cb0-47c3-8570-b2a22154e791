"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProcessarLotePorDataDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class ProcessarLotePorDataDto {
    data;
}
exports.ProcessarLotePorDataDto = ProcessarLotePorDataDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Data no formato DD/MM/YYYY para buscar as notas fiscais',
        example: '05/08/2025',
        pattern: '^\\d{2}/\\d{2}/\\d{4}$',
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Data é obrigatória' }),
    (0, class_validator_1.IsString)({ message: 'Data deve ser uma string' }),
    (0, class_validator_1.Matches)(/^\d{2}\/\d{2}\/\d{4}$/, {
        message: 'Data deve estar no formato DD/MM/YYYY',
    }),
    __metadata("design:type", String)
], ProcessarLotePorDataDto.prototype, "data", void 0);
//# sourceMappingURL=processar-lote-por-data.dto.js.map