"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RomaneiosService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const romaneio_entity_1 = require("./entities/romaneio.entity");
const romaneio_order_entity_1 = require("../romaneio-orders/entities/romaneio-order.entity");
const separation_order_entity_1 = require("../separation-orders/entity/separation-order.entity");
const product_entity_1 = require("../products/entities/product.entity");
const axios_1 = require("axios");
const mega_service_1 = require("../mega/mega.service");
const romaneio_draft_entity_1 = require("./entities/romaneio-draft.entity");
let RomaneiosService = class RomaneiosService {
    repository;
    draftRepository;
    dataSource;
    megaService;
    oracleConnectionFactory;
    constructor(repository, draftRepository, dataSource, megaService, oracleConnectionFactory) {
        this.repository = repository;
        this.draftRepository = draftRepository;
        this.dataSource = dataSource;
        this.megaService = megaService;
        this.oracleConnectionFactory = oracleConnectionFactory;
    }
    async findProductByCode(productCode, depositorName, apiKey) {
        let page = 1;
        const pageSize = 10;
        const maxPages = 100;
        while (page <= maxPages) {
            try {
                const productRes = await axios_1.default.get('https://apigateway.smartgo.com.br/produto', {
                    params: {
                        CodigoInterno: productCode,
                        Page: page,
                        PageSize: pageSize,
                    },
                    headers: { Accept: 'application/json', api_key: apiKey },
                });
                const products = productRes.data?.model?.items ?? [];
                if (products.length === 0) {
                    break;
                }
                const product = products.find(prod => prod.depositante === depositorName);
                if (product) {
                    return product;
                }
                const totalItems = productRes.data?.model?.totalItems || 0;
                if (page * pageSize >= totalItems) {
                    break;
                }
                page++;
            }
            catch (error) {
                console.error(`Erro ao buscar produto na página ${page}:`, error);
                break;
            }
        }
        return null;
    }
    async create(createRomaneioDto) {
        if (createRomaneioDto.productPackagings && createRomaneioDto.productPackagings.length > 0) {
            const productsWithoutPackaging = createRomaneioDto.productPackagings.filter(product => !product.packagingId || product.packagingId === null || product.packagingId === undefined);
            if (productsWithoutPackaging.length > 0) {
                const missingProducts = productsWithoutPackaging
                    .map(p => `${p.productName} (${p.orderCode})`)
                    .join(', ');
                throw new common_1.BadRequestException(`Os seguintes produtos não possuem embalagem selecionada: ${missingProducts}. Selecione uma embalagem para todos os produtos antes de criar o romaneio.`);
            }
        }
        const apiKey = process.env.WMS_API_KEY || '';
        let response;
        let romaneioId;
        const { projeto, externalCode, internalCode, volumeTotalRomaneio, volumeTotalSeparation } = await this.prepareOracleData(createRomaneioDto);
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            const newData = new romaneio_entity_1.Romaneio();
            newData.address = createRomaneioDto.address;
            newData.carrierId = createRomaneioDto.carrierId;
            newData.driverId = createRomaneioDto.driverId;
            newData.vehicleId = createRomaneioDto.vehicleId;
            newData.totalLength = createRomaneioDto.totalLength;
            newData.totalWidth = createRomaneioDto.totalWidth;
            newData.totalHeight = createRomaneioDto.totalHeight;
            newData.totalWeight = createRomaneioDto.totalWeight;
            newData.totalVolume = createRomaneioDto.totalVolume;
            newData.mainPackagingId = createRomaneioDto.mainPackagingId;
            newData.secondaryPackagingId = createRomaneioDto.secondaryPackagingId || undefined;
            newData.linking = createRomaneioDto.linking;
            newData.userId = 1;
            newData.observations = createRomaneioDto.observations;
            const responseRomaneio = await queryRunner.manager.save(newData);
            romaneioId = responseRomaneio.id;
            const romaneioOrdersArray = await this.createRomaneioOrders(queryRunner, createRomaneioDto.romaneioOrders, responseRomaneio.id);
            await this.removeDrafts(queryRunner, romaneioOrdersArray);
            await queryRunner.commitTransaction();
            response = {
                ...responseRomaneio,
                romaneioOrders: romaneioOrdersArray.map((order) => ({
                    id: order.id,
                    romaneioId: order.romaneioId,
                    separationOrderId: order.separationOrderId ?? 0,
                    numberPackaging: order.numberPackaging,
                    volume: order.volume,
                    length: order.length,
                    width: order.width,
                    height: order.height,
                    netWeight: order.netWeight,
                })),
                productPackagings: createRomaneioDto.productPackagings
            };
        }
        catch (error) {
            console.log('🚀 ~ RomaneiosService ~ create ~ error:', error);
            await queryRunner.rollbackTransaction();
            throw new common_1.BadRequestException('Erro ao criar romaneio. A transação foi revertida.', error.message);
        }
        finally {
            await queryRunner.release();
        }
        this.handlePostCreationTasks(romaneioId, createRomaneioDto, projeto, externalCode, internalCode, volumeTotalRomaneio, volumeTotalSeparation, apiKey).catch(error => {
            console.error('Erro nas operações pós-criação:', error);
        });
        return { response };
    }
    async findAll() {
        await this.autoVerifyOldRomaneios();
        const queryData = await this.repository
            .createQueryBuilder('romaneio')
            .select('romaneio.id', 'id')
            .addSelect('romaneio.date_issue', 'dateIssue')
            .addSelect('user.name', 'userName')
            .addSelect('romaneio.address', 'address')
            .addSelect('romaneio.total_length', 'totalLength')
            .addSelect('romaneio.total_width', 'totalWidth')
            .addSelect('romaneio.total_height', 'totalHeight')
            .addSelect('romaneio.total_weight', 'totalWeight')
            .addSelect('romaneio.total_volume', 'totalVolume')
            .addSelect('romaneio.main_packaging_id', 'mainPackagingId')
            .addSelect('romaneio.secondary_packaging_id', 'secondaryPackagingId')
            .addSelect('romaneio.observations', 'observations')
            .addSelect('romaneio.verified_observations', 'verifiedObservations')
            .addSelect('packagings.type', 'packagingType')
            .addSelect('packagings.weight', 'mainPackagingWeight')
            .addSelect('packagings.length', 'mainPackagingLength')
            .addSelect('packagings.width', 'mainPackagingWidth')
            .addSelect('packagings.height', 'mainPackagingHeight')
            .addSelect('secondary_packagings.type', 'secondaryPackagingType')
            .addSelect('secondary_packagings.weight', 'secondaryPackagingWeight')
            .addSelect('secondary_packagings.length', 'secondaryPackagingLength')
            .addSelect('secondary_packagings.width', 'secondaryPackagingWidth')
            .addSelect('secondary_packagings.height', 'secondaryPackagingHeight')
            .addSelect('transporters.name', 'carrierName')
            .addSelect('transporters.erpCode', 'erpCode')
            .addSelect('drivers.name', 'driverName')
            .addSelect('vehicles.plate', 'vehiclePlace')
            .addSelect('romaneio.status_id', 'statusId')
            .leftJoin('user', 'user', 'user.id = romaneio.user_id')
            .leftJoin('transporters', 'transporters', 'transporters.id = romaneio.carrier_id')
            .leftJoin('drivers', 'drivers', 'drivers.id = romaneio.driver_id')
            .leftJoin('vehicles', 'vehicles', 'vehicles.id = romaneio.vehicle_id')
            .leftJoin('packagings', 'packagings', 'packagings.id = romaneio.main_packaging_id')
            .leftJoin('packagings', 'secondary_packagings', 'secondary_packagings.id = romaneio.secondary_packaging_id')
            .where('romaneio.deleted_at IS NULL')
            .orderBy('romaneio.date_issue', 'DESC')
            .execute();
        for (const data of queryData) {
            switch (data.statusId) {
                case 0:
                    data.statusName = 'Aguardando Notas';
                    break;
                case 1:
                    data.statusName = 'Pendente';
                    break;
                case 2:
                    data.statusName = 'Nota Emitida';
                    break;
                case 3:
                    data.statusName = 'Entregue';
                    break;
                case 4:
                    data.statusName = 'Verificado Automaticamente';
                    break;
                case 5:
                    data.statusName = 'Verificado Manualmente';
                    break;
                default:
                    data.statusName = '';
                    break;
            }
            let products = [];
            try {
                products = await this.dataSource
                    .createQueryBuilder()
                    .select('product.id', 'id')
                    .addSelect('product.name', 'name')
                    .addSelect('product.id_external', 'idExternal')
                    .addSelect('product.description', 'description')
                    .addSelect('product.depositor', 'depositor')
                    .addSelect('product.quantity', 'quantity')
                    .addSelect('product.verified', 'verified')
                    .addSelect('product.id_packaging', 'idPackaging')
                    .addSelect('product.id_separation_order', 'idSeparationOrder')
                    .addSelect('product.volume', 'volume')
                    .addSelect('product.weight', 'weight')
                    .addSelect('product.verified_date', 'verifiedDate')
                    .addSelect('product.verified_time', 'verifiedTime')
                    .addSelect('product.verified_by', 'verifiedBy')
                    .addSelect('product.tax_note_number', 'taxNumber')
                    .addSelect('product.unit_value', 'unitValue')
                    .addSelect('product.total_value', 'totalValue')
                    .addSelect('so.internalCode', 'internalCode')
                    .from('product', 'product')
                    .leftJoin('separation_orders', 'so', 'so.id = product.id_separation_order')
                    .where('product.id_romanio = :idRomanio', { idRomanio: data.id })
                    .andWhere('product.deleted_at IS NULL')
                    .orderBy('product.idPackaging')
                    .execute();
            }
            catch (error) {
                console.error(`Erro ao buscar produtos para romaneio ${data.id}:`, error);
                products = [];
            }
            const packagingIds = products.map(p => p.idPackaging).filter(Boolean);
            let packagings = [];
            if (packagingIds.length > 0) {
                try {
                    packagings = await this.dataSource
                        .createQueryBuilder()
                        .select('pck.*')
                        .from('packagings', 'pck')
                        .where('pck.id IN (:...ids)', { ids: packagingIds })
                        .execute();
                }
                catch (error) {
                    console.error(`Erro ao buscar embalagens para romaneio ${data.id}:`, error);
                    packagings = [];
                }
            }
            const packagingMap = new Map(packagings.map(pck => [pck.id, pck]));
            for (const product of products) {
                product.packaging = packagingMap.get(product.idPackaging) || null;
            }
            let romaneioOrderRaw = [];
            try {
                romaneioOrderRaw = await this.dataSource
                    .createQueryBuilder()
                    .select('ro.id', 'romaneioOrderId')
                    .addSelect('ro.romaneio_id', 'romaneioId')
                    .addSelect('ro.number_packaging', 'PackageOrder')
                    .addSelect('pk.type', 'PackagingType')
                    .addSelect('iv.numeroDanfe', 'danfeNumber')
                    .addSelect('so.id', 'sepId')
                    .addSelect('so.internalCode', 'internalCode')
                    .addSelect('so.depositorName', 'depositorName')
                    .addSelect('so.orderDate', 'orderDate')
                    .addSelect('so.status', 'status')
                    .addSelect('so.externalCode', 'externalCode')
                    .addSelect('so.environment', 'environment')
                    .addSelect('so.observation', 'observation')
                    .addSelect('so.typeOfBond', 'typeOfBond')
                    .addSelect('so.volume', 'volume')
                    .from('romaneio_order', 'ro')
                    .leftJoin('separation_orders', 'so', 'so.id = ro.separation_order_id')
                    .leftJoin('packagings', 'pk', 'pk.id = ro.number_packaging')
                    .leftJoin('invoices', 'iv', 'iv.id_ordem = so.id')
                    .where('ro.romaneio_id = :id', { id: data.id })
                    .execute();
            }
            catch (error) {
                console.error(`Erro ao buscar romaneio orders para romaneio ${data.id}:`, error);
                romaneioOrderRaw = [];
            }
            let taxOrder = null;
            try {
                taxOrder = await this.dataSource
                    .createQueryBuilder()
                    .select('iv.numeroDanfe', 'numeroDanfe')
                    .from('invoices', 'iv')
                    .where('iv.id_romaneio = :id', { id: data.id })
                    .getOne();
            }
            catch (error) {
                console.error(`Erro ao buscar nota fiscal para romaneio ${data.id}:`, error);
            }
            data.taxOrder = taxOrder?.numeroDanfe || null;
            const romaneioOrdersMap = new Map();
            for (const row of romaneioOrderRaw) {
                const orderId = row.romaneioOrderId;
                if (!romaneioOrdersMap.has(orderId)) {
                    romaneioOrdersMap.set(orderId, {
                        id: orderId,
                        romaneioId: row.romaneioId,
                        separationOrders: []
                    });
                }
                if (row.sepId) {
                    romaneioOrdersMap.get(orderId).separationOrders.push({
                        id: row.sepId,
                        internalCode: row.internalCode,
                        depositorName: row.depositorName,
                        orderDate: row.orderDate,
                        status: row.status,
                        externalCode: row.externalCode,
                        environment: row.environment,
                        observation: row.observation,
                        typeOfBond: row.typeOfBond,
                        volume: row.volume
                    });
                }
            }
            data.romaneioOrders = Array.from(romaneioOrdersMap.values());
            data.products = products;
            data.totalProducts = products.length;
            if (data.mainPackagingId) {
                data.mainPackaging = {
                    id: data.mainPackagingId,
                    type: data.packagingType,
                    weight: data.mainPackagingWeight,
                    length: data.mainPackagingLength,
                    width: data.mainPackagingWidth,
                    height: data.mainPackagingHeight
                };
            }
            if (data.secondaryPackagingId) {
                data.secondaryPackaging = {
                    id: data.secondaryPackagingId,
                    type: data.secondaryPackagingType,
                    weight: data.secondaryPackagingWeight,
                    length: data.secondaryPackagingLength,
                    width: data.secondaryPackagingWidth,
                    height: data.secondaryPackagingHeight
                };
            }
        }
        return queryData;
    }
    async saveProductsBySeparationOrderId(queryRunner, separationOrderId, apiKey, romaneioId, productPackagings) {
        const separationOrder = await queryRunner.manager.findOne(separation_order_entity_1.SeparationOrder, {
            where: { id: separationOrderId },
        });
        if (!separationOrder)
            return;
        const duplicatedProducts = productPackagings.filter(p => p.productCode.includes('_duplicate_'));
        const originalProducts = productPackagings.filter(p => !p.productCode.includes('_duplicate_'));
        for (const duplicatedProduct of duplicatedProducts) {
            try {
                const originalProductCode = duplicatedProduct.productCode.split('_duplicate_')[0];
                const product = await this.findProductByCode(originalProductCode, separationOrder.depositorName, apiKey);
                if (!product)
                    continue;
                const res = await axios_1.default.get(`https://apigateway.smartgo.com.br/expedicao/pedido/${separationOrder.internalCode}`, {
                    params: {
                        CarregarInformacoesFiscais: true,
                        CarregarProdutos: true,
                        CarregarInformacoesEntrada: true
                    },
                    headers: { Accept: 'application/json', api_key: apiKey },
                });
                const itens = res.data?.model?.itens ?? [];
                const itemProd = itens.find((item) => item.produtos?.some((p) => p.codigoInternoProduto === originalProductCode));
                if (!itemProd)
                    continue;
                const firstDadosFiscais = itemProd.produto?.dadosFiscaisEntrada?.[0];
                const itemNF = firstDadosFiscais?.itemNF;
                const unitValue = itemNF?.produtoValorUnitario || 0;
                const totalValue = itemNF?.produtoValorTotal || 0;
                const packagingId = duplicatedProduct.packagingId ?? null;
                const secondaryVolume = duplicatedProduct.secondaryVolume ?? 0;
                const totalSecondaryVolumes = duplicatedProduct.totalSecondaryVolumes ?? 0;
                const newProduct = new product_entity_1.Product();
                newProduct.idEnterpriseExternal = product.idEmpresa;
                newProduct.idDepositorExternal = product.idDepositante;
                newProduct.depositor = product.depositante;
                newProduct.idExternal = product.codigoInterno;
                newProduct.name = duplicatedProduct.productName || product.nome;
                newProduct.fullName = product.nomeCompleto;
                newProduct.description = product.descricao;
                newProduct.quantity = duplicatedProduct.productQuantity || 0;
                newProduct.idRomanio = romaneioId;
                newProduct.type = product.codigoUnidadeEstocagem;
                newProduct.idSeparationOrder = separationOrderId;
                newProduct.idPackaging = packagingId;
                newProduct.volume = `${secondaryVolume}/${totalSecondaryVolumes}`;
                newProduct.weight = Number(duplicatedProduct.productWeight) || 0;
                newProduct.unitValue = unitValue;
                newProduct.totalValue = totalValue;
                await queryRunner.manager.save(newProduct);
            }
            catch (err) {
                console.error('Erro ao salvar produto duplicado:', err);
            }
        }
        const res = await axios_1.default.get(`https://apigateway.smartgo.com.br/expedicao/pedido/${separationOrder.internalCode}`, {
            params: {
                CarregarInformacoesFiscais: true,
                CarregarProdutos: true,
                CarregarInformacoesEntrada: true
            },
            headers: { Accept: 'application/json', api_key: apiKey },
        });
        const itens = res.data?.model?.itens ?? [];
        const uniqueItens = [];
        const seenProductCodes = new Set();
        for (const itemProd of itens) {
            const firstProduct = itemProd.produtos?.[0];
            if (firstProduct && !seenProductCodes.has(firstProduct.codigoInternoProduto)) {
                seenProductCodes.add(firstProduct.codigoInternoProduto);
                uniqueItens.push({ ...itemProd, produto: firstProduct });
            }
        }
        for (const itemProd of uniqueItens) {
            try {
                const product = await this.findProductByCode(itemProd.produto.codigoInternoProduto, separationOrder.depositorName, apiKey);
                if (!product)
                    continue;
                const firstDadosFiscais = itemProd.produto.dadosFiscaisEntrada?.[0];
                const itemNF = firstDadosFiscais?.itemNF;
                const unitValue = itemNF?.produtoValorUnitario || 0;
                const totalValue = itemNF?.produtoValorTotal || 0;
                const isProductSplit = productPackagings.some(p => p.productCode.startsWith(product.codigoInterno + '_split_') ||
                    p.productCode.startsWith(product.codigoInterno + '_duplicate_'));
                if (isProductSplit) {
                    continue;
                }
                const packagingEntry = originalProducts.find(p => p.productCode === product.codigoInterno &&
                    p.orderCode === separationOrder.internalCode);
                const packagingId = packagingEntry?.packagingId ?? null;
                const secondaryVolume = packagingEntry?.secondaryVolume ?? 0;
                const totalSecondaryVolumes = packagingEntry?.totalSecondaryVolumes ?? 0;
                const existing = await queryRunner.manager
                    .getRepository(product_entity_1.Product)
                    .createQueryBuilder('product')
                    .select('product.id')
                    .where('product.id_external = :idExternal', {
                    idExternal: product.codigoInterno,
                })
                    .andWhere('product.idSeparationOrder = :sepId', { sepId: separationOrderId })
                    .getRawOne();
                if (!existing?.id) {
                    const newProduct = new product_entity_1.Product();
                    newProduct.idEnterpriseExternal = product.idEmpresa;
                    newProduct.idDepositorExternal = product.idDepositante;
                    newProduct.depositor = product.depositante;
                    newProduct.idExternal = product.codigoInterno;
                    newProduct.name = packagingEntry?.productName || product.nome;
                    newProduct.fullName = product.nomeCompleto;
                    newProduct.description = product.descricao;
                    newProduct.quantity = packagingEntry?.productQuantity || itemProd.quantidadeSolicitada;
                    newProduct.idRomanio = romaneioId;
                    newProduct.type = product.codigoUnidadeEstocagem;
                    newProduct.idSeparationOrder = separationOrderId;
                    newProduct.idPackaging = packagingId;
                    newProduct.volume = `${secondaryVolume}/${totalSecondaryVolumes}`;
                    newProduct.weight = Number(packagingEntry?.productWeight) || 0;
                    newProduct.unitValue = unitValue;
                    newProduct.totalValue = totalValue;
                    await queryRunner.manager.save(newProduct);
                }
                else {
                    const updateProduct = await queryRunner.manager.preload(product_entity_1.Product, {
                        id: existing.id,
                        idPackaging: packagingId,
                        name: packagingEntry?.productName || product.nome,
                        quantity: packagingEntry?.productQuantity || itemProd.quantidadeSolicitada,
                        unitValue: unitValue,
                        totalValue: totalValue,
                    });
                    if (updateProduct) {
                        await queryRunner.manager.save(updateProduct);
                    }
                }
            }
            catch (err) {
                console.error('Erro ao buscar ou salvar produto:', err);
            }
        }
    }
    async prepareOracleData(createRomaneioDto) {
        let projeto = null;
        let externalCode = null;
        let internalCode = null;
        let volumeTotal = 0;
        const firstOrder = createRomaneioDto.romaneioOrders[0];
        if (firstOrder?.separationOrderId) {
            const separationOrder = await this.repository.manager.findOne(separation_order_entity_1.SeparationOrder, {
                where: { id: firstOrder.separationOrderId }
            });
            if (separationOrder?.externalCode) {
                const externalCodeParts = separationOrder.externalCode.split(' - ');
                const apelido = externalCodeParts[0]?.trim();
                externalCode = separationOrder.externalCode;
                internalCode = separationOrder.internalCode;
                if (apelido) {
                    const queryProjeto = `
            SELECT PRO_IN_REDUZIDO FROM ELCO.glo_projetos@ELCO 
            WHERE PRO_ST_APELIDO = :apelido
          `;
                    let connection;
                    try {
                        connection = await this.oracleConnectionFactory();
                        const result = await connection.execute(queryProjeto, [apelido]);
                        if (result && result.rows && result.rows.length > 0 && result.rows[0][0]) {
                            projeto = result.rows[0][0];
                        }
                    }
                    catch (e) {
                        console.error(`Erro ao consultar projeto no Oracle:`, e);
                    }
                    finally {
                        if (connection)
                            await connection.close();
                    }
                }
            }
        }
        const volumeTotalRomaneio = createRomaneioDto.romaneioOrders.reduce((total, order) => {
            const volumeQuantidade = Number(order.volume) || 0;
            return total + volumeQuantidade;
        }, 0);
        let volumeTotalSeparation = 0;
        for (const order of createRomaneioDto.romaneioOrders) {
            if (order.separationOrderId) {
                const separationOrder = await this.repository.manager.findOne(separation_order_entity_1.SeparationOrder, {
                    where: { id: order.separationOrderId }
                });
                if (separationOrder?.volume) {
                    volumeTotalSeparation += Number(separationOrder.volume) || 0;
                }
            }
        }
        return { projeto, externalCode, internalCode, volumeTotalRomaneio, volumeTotalSeparation };
    }
    async createRomaneioOrders(queryRunner, romaneioOrders, romaneioId) {
        const romaneioOrdersArray = [];
        for (const item of romaneioOrders) {
            const newRomaneioOrder = new romaneio_order_entity_1.RomaneioOrder();
            newRomaneioOrder.romaneioId = romaneioId;
            newRomaneioOrder.separationOrderId = item.separationOrderId;
            newRomaneioOrder.numberPackaging = item.numberPackaging;
            newRomaneioOrder.volume = item.volume;
            newRomaneioOrder.length = item.length;
            newRomaneioOrder.width = item.width;
            newRomaneioOrder.height = item.height;
            newRomaneioOrder.netWeight = item.netWeight;
            const separationOrder = await queryRunner.manager.preload(separation_order_entity_1.SeparationOrder, { id: Number(item.separationOrderId) });
            if (separationOrder) {
                separationOrder.status = 'ROMANIO';
                await queryRunner.manager.save(separationOrder);
            }
            newRomaneioOrder.separationOrderId = separationOrder?.id ?? null;
            const order = await queryRunner.manager.save(newRomaneioOrder);
            romaneioOrdersArray.push({
                id: order.id,
                romaneioId: romaneioId,
                separationOrderId: order.separationOrderId,
                numberPackaging: order.numberPackaging,
                volume: order.volume,
                length: order.length,
                width: order.width,
                height: order.height,
                netWeight: order.netWeight,
            });
        }
        return romaneioOrdersArray;
    }
    async removeDrafts(queryRunner, romaneioOrdersArray) {
        for (const order of romaneioOrdersArray) {
            if (!order.separationOrderId)
                continue;
            const separationOrder = await queryRunner.manager.findOne(separation_order_entity_1.SeparationOrder, {
                where: { id: order.separationOrderId }
            });
            if (separationOrder?.externalCode) {
                await queryRunner.manager.delete(romaneio_draft_entity_1.RomaneioDraft, { externalCode: separationOrder.externalCode });
            }
        }
    }
    async handlePostCreationTasks(romaneioId, createRomaneioDto, projeto, externalCode, internalCode, volumeTotalRomaneio, volumeTotalSeparation, apiKey) {
        try {
            await this.saveAllProducts(romaneioId, createRomaneioDto, apiKey);
            const produtosSalvos = await this.repository.manager
                .getRepository(product_entity_1.Product)
                .find({
                where: { idRomanio: romaneioId },
                relations: []
            });
            const pesoLiquido = produtosSalvos.reduce((total, product) => {
                return total + (Number(product.weight) || 0);
            }, 0);
            const romaneio = await this.repository.manager.findOne(romaneio_entity_1.Romaneio, {
                where: { id: romaneioId }
            });
            let pesoEmbalagens = 0;
            if (romaneio?.mainPackagingId) {
                const embalagemPrincipal = await this.repository.manager
                    .createQueryBuilder()
                    .select('weight')
                    .from('packagings', 'p')
                    .where('p.id = :id', { id: romaneio.mainPackagingId })
                    .getRawOne();
                if (embalagemPrincipal?.weight) {
                    pesoEmbalagens += Number(embalagemPrincipal.weight);
                }
            }
            if (romaneio?.secondaryPackagingId) {
                const embalagemSecundaria = await this.repository.manager
                    .createQueryBuilder()
                    .select('weight')
                    .from('packagings', 'p')
                    .where('p.id = :id', { id: romaneio.secondaryPackagingId })
                    .getRawOne();
                if (embalagemSecundaria?.weight) {
                    pesoEmbalagens += Number(embalagemSecundaria.weight);
                }
            }
            const pesoEmbalagensProdutos = await this.repository.manager
                .createQueryBuilder()
                .select('SUM(p.weight)', 'totalWeight')
                .from('packagings', 'p')
                .innerJoin('product', 'prod', 'prod.id_packaging = p.id')
                .where('prod.id_romanio = :romaneioId', { romaneioId })
                .andWhere('prod.id_packaging IS NOT NULL')
                .getRawOne();
            if (pesoEmbalagensProdutos?.totalWeight) {
                pesoEmbalagens += Number(pesoEmbalagensProdutos.totalWeight);
            }
            const pesoBruto = pesoLiquido + pesoEmbalagens;
            const centroCusto = 37;
            const isMatriz = createRomaneioDto.linking === 'matriz';
            const filInCodigo = isMatriz ? 4 : 8;
            const calcAgnStCodigo = isMatriz ? "4" : "8";
            const romaneiot = await this.repository.manager.findOne(romaneio_entity_1.Romaneio, {
                where: { id: romaneioId }
            });
            const transportador = await this.repository.manager
                .createQueryBuilder()
                .select('erpCode, name')
                .from('transporters', 't')
                .where('t.id = :id', { id: romaneiot?.carrierId })
                .getRawOne();
            console.log('Gerando observações da nota fiscal:', {
                externalCode,
                romaneioId,
                address: createRomaneioDto.address,
                volumeTotalSeparation
            });
            const dadosNotaFiscal = {
                pPRO_IN_ID: 501,
                pUSU_IN_CODIGO: 1,
                pTransacao: 0,
                pSistema: 1,
                CUS_IN_REDUZIDO: centroCusto,
                PROJ_IN_REDUZIDO: projeto,
                notaFiscal: {
                    FIL_IN_CODIGO: filInCodigo,
                    TPD_IN_CODIGO: 312,
                    AGN_TAU_ST_CODIGO: "G",
                    CALC_AGN_ST_CODIGO: calcAgnStCodigo,
                    CALC_AGN_ST_TIPOCODIGO: "COD",
                    NOT_DT_EMISSAO: new Date().toLocaleDateString('pt-BR'),
                    CCF_IN_REDUZIDO: 37,
                    PROJ_IN_REDUZIDO: projeto,
                    ...(isMatriz && {
                        TRA_IN_CODIGO: transportador.erpCode,
                    }),
                    ItemNotaFiscal: (() => {
                        const groupedProducts = new Map();
                        produtosSalvos.forEach((product) => {
                            let originalCode = String(product.idExternal);
                            if (originalCode.includes('_split_')) {
                                originalCode = originalCode.split('_split_')[0];
                            }
                            else if (originalCode.includes('_duplicate_')) {
                                originalCode = originalCode.split('_duplicate_')[0];
                            }
                            const numericCode = parseInt(originalCode);
                            const finalCode = isNaN(numericCode) ? originalCode : numericCode;
                            if (groupedProducts.has(finalCode)) {
                                const existing = groupedProducts.get(finalCode);
                                existing.quantity += Number(product.quantity) || 0;
                                existing.totalValue = Number((Number(existing.totalValue) + (Number(product.totalValue) || 0)).toFixed(2));
                                existing.unitValue = Number(product.unitValue) || existing.unitValue;
                            }
                            else {
                                groupedProducts.set(finalCode, {
                                    idExternal: finalCode,
                                    name: product.name,
                                    type: product.type,
                                    quantity: Number(product.quantity) || 0,
                                    unitValue: Number(product.unitValue) || 0,
                                    totalValue: Number(product.totalValue) || 0,
                                });
                            }
                        });
                        return Array.from(groupedProducts.values()).map((product, index) => {
                            const itemBase = {
                                OPERACAO: "I",
                                ITN_IN_SEQUENCIA: index + 1,
                                PRO_ST_ALTERNATIVO: product.idExternal,
                                ITN_ST_DESCRICAO: product.name || "Produto sem nome",
                                UNI_ST_UNIDADE: product.type,
                                ITN_RE_QUANTIDADE: product.quantity || 1,
                                ITN_RE_VALORUNITARIO: product.unitValue || 1,
                                ITN_RE_VALORTOTAL: Number(Number(product.totalValue).toFixed(2)),
                            };
                            if (isMatriz) {
                                itemBase.ITN_CH_STICMS_A = "0";
                                itemBase.ITN_CH_STICMS_B = "90";
                                itemBase.ITN_ST_STIPI = "99";
                                itemBase.ENI_ST_CODIGO = "999";
                                itemBase.ITE_CST_PIS = "08";
                                itemBase.ITE_CST_COFINS = "08";
                            }
                            return itemBase;
                        });
                    })(),
                    VolumeNF: [{
                            VOL_IN_SEQUENCIA: 1,
                            VOL_ST_MARCA: '',
                            VOL_IN_NUMERO: volumeTotalSeparation,
                            VOL_IN_QUANTIDADE: volumeTotalSeparation,
                            VOL_ST_ESPECIE: "VOLUMES",
                            VOL_RE_PESOBRUTO: pesoBruto,
                            VOL_RE_PESOLIQUIDO: pesoLiquido
                        }],
                    ObservacaoNF: {
                        OPERACAO: "I",
                        NOB_CH_TIPOOBSERVACAO: "N",
                        NOB_ST_OBSERVACAO: `Não incidência de ICMS conf. art. 3º, inciso V do decreto nº 8.871/2017 (RICMS) - ${externalCode ? `OBRA: ${externalCode} - ` : ''}Romaneio Nº ${romaneioId} - ENDEREÇO DE ENTREGA: ${createRomaneioDto.address} - ${volumeTotalSeparation} VL`
                    }
                }
            };
            await this.megaService.enviarNotaFiscal(dadosNotaFiscal, 0, romaneioId);
        }
        catch (error) {
            console.error('Erro nas operações pós-criação:', error);
            await this.repository.update(romaneioId, { statusId: 3 });
        }
    }
    async saveAllProducts(romaneioId, createRomaneioDto, apiKey) {
        const savePromises = createRomaneioDto.romaneioOrders.map(async (item) => {
            if (item.separationOrderId) {
                return this.saveProductsBySeparationOrderIdExternal(item.separationOrderId, apiKey, romaneioId, createRomaneioDto.productPackagings);
            }
        });
        await Promise.all(savePromises);
    }
    async saveProductsBySeparationOrderIdExternal(separationOrderId, apiKey, romaneioId, productPackagings) {
        const separationOrder = await this.repository.manager.findOne(separation_order_entity_1.SeparationOrder, {
            where: { id: separationOrderId },
        });
        if (!separationOrder)
            return;
        const duplicatedProducts = productPackagings.filter(p => p.productCode.includes('_duplicate_'));
        const splitProducts = productPackagings.filter(p => p.productCode.includes('_split_'));
        const originalProducts = productPackagings.filter(p => !p.productCode.includes('_duplicate_') && !p.productCode.includes('_split_'));
        for (const duplicatedProduct of duplicatedProducts) {
            try {
                const originalProductCode = duplicatedProduct.productCode.split('_duplicate_')[0];
                const product = await this.findProductByCode(originalProductCode, separationOrder.depositorName, apiKey);
                if (!product)
                    continue;
                const res = await axios_1.default.get(`https://apigateway.smartgo.com.br/expedicao/pedido/${separationOrder.internalCode}`, {
                    params: {
                        CarregarInformacoesFiscais: true,
                        CarregarProdutos: true,
                        CarregarInformacoesEntrada: true
                    },
                    headers: { Accept: 'application/json', api_key: apiKey },
                });
                const itens = res.data?.model?.itens ?? [];
                const itemProd = itens.find((item) => item.produtos?.some((p) => p.codigoInternoProduto === originalProductCode));
                if (!itemProd)
                    continue;
                const firstDadosFiscais = itemProd.produto?.dadosFiscaisEntrada?.[0];
                const itemNF = firstDadosFiscais?.itemNF;
                const unitValue = itemNF?.produtoValorUnitario || 0;
                const totalValue = itemNF?.produtoValorTotal || 0;
                const packagingId = duplicatedProduct.packagingId ?? null;
                const secondaryVolume = duplicatedProduct.secondaryVolume ?? 0;
                const totalSecondaryVolumes = duplicatedProduct.totalSecondaryVolumes ?? 0;
                const newProduct = new product_entity_1.Product();
                newProduct.idEnterpriseExternal = product.idEmpresa;
                newProduct.idDepositorExternal = product.idDepositante;
                newProduct.depositor = product.depositante;
                newProduct.idExternal = product.codigoInterno;
                newProduct.name = duplicatedProduct.productName || product.nome;
                newProduct.fullName = product.nomeCompleto;
                newProduct.description = product.descricao;
                newProduct.quantity = duplicatedProduct.productQuantity || 0;
                newProduct.idRomanio = romaneioId;
                newProduct.type = product.codigoUnidadeEstocagem;
                newProduct.idSeparationOrder = separationOrderId;
                newProduct.idPackaging = packagingId;
                newProduct.volume = `${secondaryVolume}/${totalSecondaryVolumes}`;
                newProduct.weight = Number(duplicatedProduct.productWeight) || 0;
                newProduct.unitValue = unitValue;
                newProduct.totalValue = totalValue;
                await this.repository.manager.save(newProduct);
            }
            catch (err) {
                console.error('Erro ao salvar produto duplicado:', err);
            }
        }
        for (const splitProduct of splitProducts) {
            try {
                const originalProductCode = splitProduct.productCode.split('_split_')[0];
                const product = await this.findProductByCode(originalProductCode, separationOrder.depositorName, apiKey);
                if (!product)
                    continue;
                const res = await axios_1.default.get(`https://apigateway.smartgo.com.br/expedicao/pedido/${separationOrder.internalCode}`, {
                    params: {
                        CarregarInformacoesFiscais: true,
                        CarregarProdutos: true,
                        CarregarInformacoesEntrada: true
                    },
                    headers: { Accept: 'application/json', api_key: apiKey },
                });
                const itens = res.data?.model?.itens ?? [];
                const itemProd = itens.find((item) => item.produtos?.some((p) => p.codigoInternoProduto === originalProductCode));
                if (!itemProd)
                    continue;
                const firstDadosFiscais = itemProd.produtos?.[0]?.dadosFiscaisEntrada?.[0];
                const itemNF = firstDadosFiscais?.itemNF;
                const unitValue = itemNF?.produtoValorUnitario || 0;
                const totalValue = itemNF?.produtoValorTotal || 0;
                const packagingId = splitProduct.packagingId ?? null;
                const secondaryVolume = splitProduct.secondaryVolume ?? 0;
                const totalSecondaryVolumes = splitProduct.totalSecondaryVolumes ?? 0;
                const newProduct = new product_entity_1.Product();
                newProduct.idEnterpriseExternal = product.idEmpresa;
                newProduct.idDepositorExternal = product.idDepositante;
                newProduct.depositor = product.depositante;
                newProduct.idExternal = product.codigoInterno;
                newProduct.name = splitProduct.productName || product.nome;
                newProduct.fullName = product.nomeCompleto;
                newProduct.description = product.descricao;
                newProduct.quantity = splitProduct.productQuantity || 0;
                newProduct.idRomanio = romaneioId;
                newProduct.type = product.codigoUnidadeEstocagem;
                newProduct.idSeparationOrder = separationOrderId;
                newProduct.idPackaging = packagingId;
                newProduct.volume = `${secondaryVolume}/${totalSecondaryVolumes}`;
                newProduct.weight = Number(splitProduct.productWeight) || 0;
                newProduct.unitValue = unitValue;
                newProduct.totalValue = totalValue;
                await this.repository.manager.save(newProduct);
            }
            catch (err) {
                console.error('Erro ao salvar produto repartido:', err);
            }
        }
        const res = await axios_1.default.get(`https://apigateway.smartgo.com.br/expedicao/pedido/${separationOrder.internalCode}`, {
            params: {
                CarregarInformacoesFiscais: true,
                CarregarProdutos: true,
                CarregarInformacoesEntrada: true
            },
            headers: { Accept: 'application/json', api_key: apiKey },
        });
        const itens = res.data?.model?.itens ?? [];
        console.log("🚀 ~ RomaneiosService ~ itens:", itens);
        const uniqueItens = [];
        const seenProductCodes = new Set();
        for (const itemProd of itens) {
            const firstProduct = itemProd.produtos?.[0];
            if (firstProduct && !seenProductCodes.has(firstProduct.codigoInternoProduto)) {
                seenProductCodes.add(firstProduct.codigoInternoProduto);
                uniqueItens.push({ ...itemProd, produto: firstProduct });
            }
        }
        for (const itemProd of uniqueItens) {
            try {
                const product = await this.findProductByCode(itemProd.produto.codigoInternoProduto, separationOrder.depositorName, apiKey);
                if (!product)
                    continue;
                const firstDadosFiscais = itemProd.produto.dadosFiscaisEntrada?.[0];
                const itemNF = firstDadosFiscais?.itemNF;
                const unitValue = itemNF?.produtoValorUnitario || 0;
                const totalValue = itemNF?.produtoValorTotal || 0;
                const isProductSplit = productPackagings.some(p => p.productCode.startsWith(product.codigoInterno + '_split_') ||
                    p.productCode.startsWith(product.codigoInterno + '_duplicate_'));
                if (isProductSplit) {
                    continue;
                }
                const packagingEntry = originalProducts.find(p => p.productCode === product.codigoInterno &&
                    p.orderCode === separationOrder.internalCode);
                const packagingId = packagingEntry?.packagingId ?? null;
                const secondaryVolume = packagingEntry?.secondaryVolume ?? 0;
                const totalSecondaryVolumes = packagingEntry?.totalSecondaryVolumes ?? 0;
                const existing = await this.repository.manager
                    .getRepository(product_entity_1.Product)
                    .createQueryBuilder('product')
                    .select('product.id')
                    .where('product.id_external = :idExternal', {
                    idExternal: product.codigoInterno,
                })
                    .andWhere('product.idSeparationOrder = :sepId', { sepId: separationOrderId })
                    .getRawOne();
                if (!existing?.id) {
                    const newProduct = new product_entity_1.Product();
                    newProduct.idEnterpriseExternal = product.idEmpresa;
                    newProduct.idDepositorExternal = product.idDepositante;
                    newProduct.depositor = product.depositante;
                    newProduct.idExternal = product.codigoInterno;
                    newProduct.name = packagingEntry?.productName || product.nome;
                    newProduct.fullName = product.nomeCompleto;
                    newProduct.description = product.descricao;
                    newProduct.quantity = packagingEntry?.productQuantity || itemProd.quantidadeSolicitada;
                    newProduct.idRomanio = romaneioId;
                    newProduct.idSeparationOrder = separationOrderId;
                    newProduct.idPackaging = packagingId;
                    newProduct.volume = `${secondaryVolume}/${totalSecondaryVolumes}`;
                    newProduct.weight = Number(packagingEntry?.productWeight) || 0;
                    newProduct.unitValue = unitValue;
                    newProduct.totalValue = totalValue;
                    await this.repository.manager.save(newProduct);
                }
                else {
                    const updateProduct = await this.repository.manager.preload(product_entity_1.Product, {
                        id: existing.id,
                        idPackaging: packagingId,
                        name: packagingEntry?.productName || product.nome,
                        quantity: packagingEntry?.productQuantity || itemProd.quantidadeSolicitada,
                        unitValue: unitValue,
                        totalValue: totalValue,
                    });
                    if (updateProduct) {
                        await this.repository.manager.save(updateProduct);
                    }
                }
            }
            catch (err) {
                console.error('Erro ao buscar ou salvar produto:', err);
            }
        }
    }
    async findOne(id) {
        await this.autoVerifyOldRomaneios();
        return this.repository.findOne({ where: { id } });
    }
    async update(id, updateRomaneioDto) {
        const { romaneioOrders, productPackagings, ...updateData } = updateRomaneioDto;
        await this.repository.update(id, updateData);
        if (romaneioOrders && romaneioOrders.length > 0) {
            console.log('RomaneioOrders encontrados no update, mas não implementado ainda');
        }
        return this.repository.findOne({ where: { id } });
    }
    async deleteRomaneio(id) {
        const romaneio = await this.repository.findOne({ where: { id } });
        if (!romaneio) {
            throw new common_1.BadRequestException('Romaneio não encontrado');
        }
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            const romaneioOrders = await queryRunner.manager
                .getRepository(romaneio_order_entity_1.RomaneioOrder)
                .find({
                where: { romaneioId: id }
            });
            const separationOrderIds = romaneioOrders
                .map(order => order.separationOrderId)
                .filter(id => id !== null);
            console.log('Romaneio orders encontrados:', romaneioOrders.length);
            console.log('Separation order IDs para atualizar:', separationOrderIds);
            await queryRunner.manager
                .getRepository(product_entity_1.Product)
                .delete({ idRomanio: id });
            await queryRunner.manager
                .getRepository(romaneio_order_entity_1.RomaneioOrder)
                .delete({ romaneioId: id });
            if (separationOrderIds.length > 0) {
                console.log(`Atualizando ${separationOrderIds.length} separation orders para status PENDING e volume NULL`);
                const currentSeparationOrders = await queryRunner.manager
                    .getRepository(separation_order_entity_1.SeparationOrder)
                    .find({
                    where: { id: (0, typeorm_2.In)(separationOrderIds) }
                });
                console.log('Valores atuais dos separation orders:', currentSeparationOrders.map(so => ({
                    id: so.id,
                    status: so.status,
                    volume: so.volume
                })));
                await queryRunner.manager
                    .createQueryBuilder()
                    .update(separation_order_entity_1.SeparationOrder)
                    .set({
                    status: 'PENDING'
                })
                    .where('id IN (:...ids)', { ids: separationOrderIds })
                    .execute();
                await queryRunner.manager.query(`UPDATE separation_orders SET volume = NULL WHERE id IN (${separationOrderIds.map(() => '?').join(',')})`, separationOrderIds);
                const updatedSeparationOrders = await queryRunner.manager
                    .getRepository(separation_order_entity_1.SeparationOrder)
                    .find({
                    where: { id: (0, typeorm_2.In)(separationOrderIds) }
                });
                console.log('Valores após atualização dos separation orders:', updatedSeparationOrders.map(so => ({
                    id: so.id,
                    status: so.status,
                    volume: so.volume
                })));
                console.log('Separation orders atualizados com sucesso');
            }
            await queryRunner.manager
                .getRepository(romaneio_entity_1.Romaneio)
                .delete({ id });
            await queryRunner.commitTransaction();
            return {
                message: 'Romaneio excluído com sucesso',
                deletedProducts: true,
                deletedRomaneioOrders: romaneioOrders.length,
                updatedSeparationOrders: separationOrderIds.length
            };
        }
        catch (error) {
            console.error('Erro ao excluir romaneio:', error);
            await queryRunner.rollbackTransaction();
            throw new common_1.BadRequestException('Erro ao excluir romaneio. A transação foi revertida.', error.message);
        }
        finally {
            await queryRunner.release();
        }
    }
    async saveDraft(dto) {
        if (!dto || !dto.romaneioOrders?.length) {
            throw new common_1.BadRequestException('Dados incompletos para salvar rascunho');
        }
        const firstOrder = dto.romaneioOrders[0];
        const separationOrder = await this.repository.manager.findOne(separation_order_entity_1.SeparationOrder, {
            where: { id: firstOrder.separationOrderId },
        });
        if (!separationOrder?.externalCode) {
            throw new common_1.BadRequestException('Pedido de separação inválido');
        }
        const existing = await this.draftRepository.findOne({
            where: { externalCode: separationOrder.externalCode },
        });
        if (existing) {
            const previousData = existing.data;
            const updatedData = {
                ...previousData,
                ...dto,
                romaneioOrders: [
                    ...new Map([
                        ...previousData.romaneioOrders,
                        ...dto.romaneioOrders,
                    ].map(item => [item.separationOrderId, item])).values(),
                ],
                productPackagings: [
                    ...[...previousData.productPackagings, ...dto.productPackagings]
                        .filter((v, i, a) => a.findIndex(t => t.orderCode === v.orderCode && t.productCode === v.productCode) === i),
                ],
            };
            existing.data = JSON.stringify(updatedData);
            existing.updatedAt = new Date();
            return await this.draftRepository.save(existing);
        }
        else {
            const draft = this.draftRepository.create({
                externalCode: separationOrder.externalCode,
                data: JSON.stringify(dto),
            });
            return await this.draftRepository.save(draft);
        }
    }
    async getDraftByExternalCode(externalCode) {
        const draft = await this.draftRepository.findOne({ where: { externalCode } });
        if (!draft)
            return null;
        return {
            ...draft,
            data: draft.data,
        };
    }
    async calcularPesoRomaneio(romaneioId) {
        const produtosSalvos = await this.repository.manager
            .getRepository(product_entity_1.Product)
            .find({
            where: { idRomanio: romaneioId },
            relations: []
        });
        const pesoLiquido = produtosSalvos.reduce((total, product) => {
            return total + (Number(product.weight) || 0);
        }, 0);
        const romaneio = await this.repository.manager.findOne(romaneio_entity_1.Romaneio, {
            where: { id: romaneioId }
        });
        let pesoEmbalagens = 0;
        if (romaneio?.mainPackagingId) {
            const embalagemPrincipal = await this.repository.manager
                .createQueryBuilder()
                .select('weight')
                .from('packagings', 'p')
                .where('p.id = :id', { id: romaneio.mainPackagingId })
                .getRawOne();
            if (embalagemPrincipal?.weight) {
                pesoEmbalagens += Number(embalagemPrincipal.weight);
            }
        }
        if (romaneio?.secondaryPackagingId) {
            const embalagemSecundaria = await this.repository.manager
                .createQueryBuilder()
                .select('weight')
                .from('packagings', 'p')
                .where('p.id = :id', { id: romaneio.secondaryPackagingId })
                .getRawOne();
            if (embalagemSecundaria?.weight) {
                pesoEmbalagens += Number(embalagemSecundaria.weight);
            }
        }
        const pesoEmbalagensProdutos = await this.repository.manager
            .createQueryBuilder()
            .select('SUM(p.weight)', 'totalWeight')
            .from('packagings', 'p')
            .innerJoin('product', 'prod', 'prod.id_packaging = p.id')
            .where('prod.id_romanio = :romaneioId', { romaneioId })
            .andWhere('prod.id_packaging IS NOT NULL')
            .getRawOne();
        if (pesoEmbalagensProdutos?.totalWeight) {
            pesoEmbalagens += Number(pesoEmbalagensProdutos.totalWeight);
        }
        const pesoBruto = pesoLiquido + pesoEmbalagens;
        return { pesoLiquido, pesoBruto };
    }
    async autoVerifyOldRomaneios() {
        try {
            const fiveDaysAgo = new Date();
            fiveDaysAgo.setDate(fiveDaysAgo.getDate() - 5);
            console.log(`🔍 Verificando romaneios não verificados criados antes de: ${fiveDaysAgo.toISOString()}`);
            console.log(`📅 Data atual: ${new Date().toISOString()}`);
            const allUnverifiedRomaneios = await this.repository
                .createQueryBuilder('romaneio')
                .select('romaneio.id', 'id')
                .addSelect('romaneio.date_issue', 'dateIssue')
                .addSelect('romaneio.status_id', 'statusId')
                .where('romaneio.status_id IN (:...statusIds)', { statusIds: [0, 1] })
                .andWhere('romaneio.deleted_at IS NULL')
                .orderBy('romaneio.date_issue', 'ASC')
                .getRawMany();
            console.log(`📋 Total de romaneios não verificados encontrados: ${allUnverifiedRomaneios.length}`);
            for (const romaneio of allUnverifiedRomaneios) {
                const romaneioDate = new Date(romaneio.dateIssue);
                const isOlderThan5Days = romaneioDate < fiveDaysAgo;
                console.log(`📊 Romaneio ${romaneio.id}: criado em ${romaneio.dateIssue} (${romaneioDate.toISOString()}) - Status: ${romaneio.statusId} - Mais de 5 dias: ${isOlderThan5Days}`);
            }
            const allRomaneios = await this.repository
                .createQueryBuilder('romaneio')
                .select('romaneio.id', 'id')
                .addSelect('romaneio.date_issue', 'dateIssue')
                .addSelect('romaneio.status_id', 'statusId')
                .addSelect('romaneio.deleted_at', 'deletedAt')
                .where('romaneio.deleted_at IS NULL')
                .orderBy('romaneio.date_issue', 'ASC')
                .getRawMany();
            console.log(`🔍 Total de romaneios existentes (não deletados): ${allRomaneios.length}`);
            for (const romaneio of allRomaneios) {
                const romaneioDate = new Date(romaneio.dateIssue);
                const isOlderThan5Days = romaneioDate < fiveDaysAgo;
                const statusName = romaneio.statusId === 0 ? 'Aguardando Notas' :
                    romaneio.statusId === 1 ? 'Pendente' :
                        romaneio.statusId === 2 ? 'Nota Emitida' :
                            romaneio.statusId === 3 ? 'Entregue' :
                                romaneio.statusId === 4 ? 'Verificado Automaticamente' :
                                    romaneio.statusId === 5 ? 'Verificado Manualmente' : 'Desconhecido';
                console.log(`📊 Romaneio ${romaneio.id}: criado em ${romaneio.dateIssue} (${romaneioDate.toISOString()}) - Status: ${romaneio.statusId} (${statusName}) - Mais de 5 dias: ${isOlderThan5Days}`);
            }
            const unverifiedRomaneios = await this.repository
                .createQueryBuilder('romaneio')
                .where('romaneio.status_id IN (:...statusIds)', { statusIds: [0, 1, 2] })
                .andWhere('romaneio.date_issue < :fiveDaysAgo', { fiveDaysAgo })
                .andWhere('romaneio.deleted_at IS NULL')
                .getMany();
            console.log(`📋 Encontrados ${unverifiedRomaneios.length} romaneios para verificação automática`);
            for (const romaneio of unverifiedRomaneios) {
                console.log(`🔄 Processando romaneio ${romaneio.id} criado em ${romaneio.dateIssue}`);
                const verifiedProducts = await this.dataSource
                    .createQueryBuilder()
                    .select('COUNT(*)', 'count')
                    .from('product', 'product')
                    .where('product.id_romanio = :romaneioId', { romaneioId: romaneio.id })
                    .andWhere('product.verified = :verified', { verified: true })
                    .andWhere('product.deleted_at IS NULL')
                    .getRawOne();
                if (verifiedProducts && parseInt(verifiedProducts.count) > 0) {
                    console.log(`⚠️ Romaneio ${romaneio.id} já possui produtos verificados, pulando verificação automática`);
                    continue;
                }
                const products = await this.dataSource
                    .createQueryBuilder()
                    .select('product.*')
                    .from('product', 'product')
                    .where('product.id_romanio = :romaneioId', { romaneioId: romaneio.id })
                    .andWhere('product.deleted_at IS NULL')
                    .execute();
                console.log(`📦 Romaneio ${romaneio.id} possui ${products.length} produtos`);
                if (products.length > 0) {
                    romaneio.statusId = 4;
                    romaneio.verifiedObservations = 'Verificação automática realizada pelo sistema após 5 dias';
                    await this.repository.save(romaneio);
                    const now = new Date();
                    const year = now.getFullYear();
                    const month = (now.getMonth() + 1).toString().padStart(2, '0');
                    const day = now.getDate().toString().padStart(2, '0');
                    const hours = now.getHours().toString().padStart(2, '0');
                    const minutes = now.getMinutes().toString().padStart(2, '0');
                    const seconds = now.getSeconds().toString().padStart(2, '0');
                    const verifiedDate = `${year}-${month}-${day}`;
                    const verifiedTime = `${hours}:${minutes}:${seconds}`;
                    const updateResult = await this.dataSource
                        .createQueryBuilder()
                        .update('product')
                        .set({
                        verified: true,
                        verifiedDate: verifiedDate,
                        verifiedTime: verifiedTime,
                        verifiedBy: 'Sistema (Auto Verificação)',
                        updatedAt: now
                    })
                        .where('id_romanio = :romaneioId', { romaneioId: romaneio.id })
                        .andWhere('deleted_at IS NULL')
                        .execute();
                    console.log(`✅ Romaneio ${romaneio.id} verificado automaticamente após 5 dias. Produtos atualizados: ${updateResult.affected}`);
                }
            }
        }
        catch (error) {
            console.error('❌ Erro na verificação automática de romaneios:', error);
        }
    }
};
exports.RomaneiosService = RomaneiosService;
exports.RomaneiosService = RomaneiosService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(romaneio_entity_1.Romaneio)),
    __param(1, (0, typeorm_1.InjectRepository)(romaneio_draft_entity_1.RomaneioDraft)),
    __param(4, (0, common_1.Inject)('OracleConnectionFactory')),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.DataSource,
        mega_service_1.MegaService, Function])
], RomaneiosService);
//# sourceMappingURL=romaneios.service.js.map