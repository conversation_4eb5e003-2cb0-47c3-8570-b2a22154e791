"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddressesController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const addresses_service_1 = require("./addresses.service");
const create_address_dto_1 = require("./dto/create-address.dto");
const update_address_dto_1 = require("./dto/update-address.dto");
let AddressesController = class AddressesController {
    addressesService;
    constructor(addressesService) {
        this.addressesService = addressesService;
    }
    create(createAddressDto) {
        return this.addressesService.create(createAddressDto);
    }
    findAll(projectName) {
        if (projectName) {
            return this.addressesService.findByProjectName(projectName);
        }
        return this.addressesService.findAll();
    }
    findOne(id) {
        return this.addressesService.findOne(Number(id));
    }
    update(id, updateAddressDto) {
        return this.addressesService.update(Number(id), updateAddressDto);
    }
    remove(id) {
        return this.addressesService.remove(Number(id));
    }
};
exports.AddressesController = AddressesController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Criar novo endereço',
        description: 'Cria um novo endereço de entrega no sistema.',
    }),
    (0, swagger_1.ApiBody)({
        type: create_address_dto_1.CreateAddressDto,
        description: 'Dados do novo endereço',
        examples: {
            create: {
                summary: 'Exemplo de endereço',
                value: {
                    name: 'Endereço Principal',
                    street: 'Rua das Flores, 123',
                    neighborhood: 'Centro',
                    city: 'São Paulo',
                    state: 'SP',
                    zipCode: '01234-567',
                    projectName: 'Projeto Alpha',
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Endereço criado com sucesso',
        schema: {
            type: 'object',
            properties: {
                id: { type: 'number', example: 1 },
                name: { type: 'string', example: 'Endereço Principal' },
                street: { type: 'string', example: 'Rua das Flores, 123' },
                neighborhood: { type: 'string', example: 'Centro' },
                city: { type: 'string', example: 'São Paulo' },
                state: { type: 'string', example: 'SP' },
                zipCode: { type: 'string', example: '01234-567' },
                projectName: { type: 'string', example: 'Projeto Alpha' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dados inválidos' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_address_dto_1.CreateAddressDto]),
    __metadata("design:returntype", void 0)
], AddressesController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Listar endereços',
        description: 'Lista todos os endereços ou filtra por nome do projeto.',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'projectName',
        required: false,
        description: 'Nome do projeto para filtrar endereços',
        example: 'Projeto Alpha',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de endereços',
        schema: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    id: { type: 'number', example: 1 },
                    name: { type: 'string', example: 'Endereço Principal' },
                    street: { type: 'string', example: 'Rua das Flores, 123' },
                    neighborhood: { type: 'string', example: 'Centro' },
                    city: { type: 'string', example: 'São Paulo' },
                    state: { type: 'string', example: 'SP' },
                    zipCode: { type: 'string', example: '01234-567' },
                    projectName: { type: 'string', example: 'Projeto Alpha' },
                },
            },
        },
    }),
    __param(0, (0, common_1.Query)('projectName')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AddressesController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Obter endereço por ID',
        description: 'Retorna os detalhes de um endereço específico.',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID do endereço',
        type: 'string',
        example: '1',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Dados do endereço',
        schema: {
            type: 'object',
            properties: {
                id: { type: 'number', example: 1 },
                name: { type: 'string', example: 'Endereço Principal' },
                street: { type: 'string', example: 'Rua das Flores, 123' },
                neighborhood: { type: 'string', example: 'Centro' },
                city: { type: 'string', example: 'São Paulo' },
                state: { type: 'string', example: 'SP' },
                zipCode: { type: 'string', example: '01234-567' },
                projectName: { type: 'string', example: 'Projeto Alpha' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Endereço não encontrado' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AddressesController.prototype, "findOne", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Atualizar endereço',
        description: 'Atualiza os dados de um endereço existente.',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID do endereço',
        type: 'string',
        example: '1',
    }),
    (0, swagger_1.ApiBody)({ type: update_address_dto_1.UpdateAddressDto }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Endereço atualizado com sucesso',
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Endereço não encontrado' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dados inválidos' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_address_dto_1.UpdateAddressDto]),
    __metadata("design:returntype", void 0)
], AddressesController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Deletar endereço',
        description: 'Remove um endereço do sistema.',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID do endereço',
        type: 'string',
        example: '1',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Endereço removido com sucesso',
        schema: {
            type: 'object',
            properties: {
                message: { type: 'string', example: 'Endereço removido com sucesso' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Endereço não encontrado' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AddressesController.prototype, "remove", null);
exports.AddressesController = AddressesController = __decorate([
    (0, swagger_1.ApiTags)('Endereços'),
    (0, common_1.Controller)('addresses'),
    __metadata("design:paramtypes", [addresses_service_1.AddressesService])
], AddressesController);
//# sourceMappingURL=addresses.controller.js.map