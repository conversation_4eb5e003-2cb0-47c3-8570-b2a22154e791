import { InvoicesService } from './invoices.service';
import { InvoiceListResponseDto, InvoiceErrorListResponseDto } from './dto/invoice-list.dto';
export declare class InvoicesController {
    private readonly invoicesService;
    constructor(invoicesService: InvoicesService);
    findAll(page?: number, limit?: number): Promise<InvoiceListResponseDto>;
    listInvoiceErrors(): Promise<InvoiceErrorListResponseDto>;
}
